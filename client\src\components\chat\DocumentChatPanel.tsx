import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'; // For user initials/icons
import { Send } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils'; // For conditional class names

// Assuming DocumentChatMessage is imported from where it's defined (e.g., shared/schema or a local types file)
import type { DocumentChatMessage } from '../../../../shared/schema';

interface DocumentChatPanelProps {
  chatMessages: DocumentChatMessage[];
  sendChatMessage: (messageText: string) => void;
  currentUserId: number | null; // Current logged-in user's ID
}

export function DocumentChatPanel({ chatMessages, sendChatMessage, currentUserId }: DocumentChatPanelProps) {
  const [newMessage, setNewMessage] = useState('');
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]); // Scroll when new messages arrive

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim()) {
      sendChatMessage(newMessage.trim());
      setNewMessage('');
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'MMM d, HH:mm');
    } catch (e) {
      console.error("Failed to format timestamp:", timestamp, e);
      return "Invalid date";
    }
  };

  // Pre-process messages to group them by sender
  const groupedMessages = chatMessages.reduce<Array<{
    userId: number;
    username: string;
    avatarFallback: string; // Store this once per group
    isCurrentUserGroup: boolean;
    messages: Array<{ id: string | number; messageText: string; createdAt: string }>;
  }>>((acc, msg) => {
    const lastGroup = acc.length > 0 ? acc[acc.length - 1] : null;
    if (lastGroup && lastGroup.userId === msg.userId) {
      lastGroup.messages.push({
        id: msg.id,
        messageText: msg.messageText,
        createdAt: msg.createdAt,
      });
    } else {
      acc.push({
        userId: msg.userId,
        username: msg.username,
        avatarFallback: msg.username.substring(0, 2).toUpperCase(),
        isCurrentUserGroup: msg.userId === currentUserId,
        messages: [{
          id: msg.id,
          messageText: msg.messageText,
          createdAt: msg.createdAt,
        }],
      });
    }
    return acc;
  }, []);

  return (
    <div className="flex flex-col h-full"> {/* Removed bg-background and border-l */}
      <div className="p-4 border-b"> {/* This border-b is for the header within the panel */}
        <h3 className="text-lg font-semibold">Document Chat</h3>
      </div>

      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4"> {/* Increased vertical spacing between groups */}
          {groupedMessages.map((group, groupIndex) => (
            <div
              key={`${group.userId}-${groupIndex}`} // Unique key for each group
              className={cn(
                "flex items-start gap-3", // Increased gap between avatar and message bubble
                group.isCurrentUserGroup ? "flex-row ml-auto" : "flex-row-reverse mr-auto", // Conditional direction and alignment
                groupIndex > 0 ? "mt-4" : "" // Increased margin top for subsequent groups
              )}
            >
              {/* Avatar is always the first element in DOM for simplicity, flex-direction handles visual order */}
              <Avatar className="h-8 w-8 border self-start shrink-0">
                <AvatarFallback className={cn(
                  group.isCurrentUserGroup ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                )}>
                  {group.avatarFallback}
                </AvatarFallback>
              </Avatar>

              <div
                className={cn(
                  "flex flex-col p-4 rounded-lg flex-1 items-start max-w-[80%]", // Increased padding and added max-width
                  group.isCurrentUserGroup
                    ? "bg-primary text-primary-foreground" // Conditional background and text color
                    : "bg-muted" // Conditional background
                )}
              >
                {/* Username displayed once per group */}
                {/* Username displayed once per group */}
                <p className={cn(
                  "text-sm font-semibold",
                  // group.isCurrentUserGroup ? "self-end" : "self-start" // Already handled by items-end/start on parent
                )}>
                  {group.username}
                </p>

                {/* Map over messages within this group */}
                {group.messages.map((messageItem, msgIndex) => (
                  // Using React.Fragment to return multiple elements if needed, or just structure directly
                  // Each messageItem will produce a timestamp span and a message paragraph
                  // Add margin-top to subsequent message blocks within the same bubble
                  <React.Fragment key={messageItem.id}>
                    <span className={cn(
                      "text-xs",
                      msgIndex > 0 ? "mt-3" : "mt-1", // Increased margin between messages
                      group.isCurrentUserGroup ? "text-primary-foreground/80" : "text-muted-foreground"
                      // Alignment of this span is handled by items-end/start on the bubble container
                    )}>
                      {formatTimestamp(messageItem.createdAt)}
                    </span>
                    <p className={cn(
                      "text-sm whitespace-pre-wrap break-words mb-1", // Added bottom margin
                      "text-left" // Always align text to the left within the paragraph
                    )}>
                      {messageItem.messageText}
                    </p>
                  </React.Fragment>
                ))}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      <div className="p-4 border-t">
        <form onSubmit={handleSendMessage} className="flex items-center gap-2">
          <Input
            type="text"
            placeholder="Type a message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            className="flex-1"
            autoComplete="off"
          />
          <Button type="submit" size="icon" disabled={!newMessage.trim()}>
            <Send className="h-4 w-4" />
            <span className="sr-only">Send message</span>
          </Button>
        </form>
      </div>
    </div>
  );
}

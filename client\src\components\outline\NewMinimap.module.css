/* NewMinimap.module.css */

.minimapWindow {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: hsl(var(--background)); /* Updated to theme background */
  cursor: pointer;
}

.minimapPreview {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform-origin: top left;
  overflow: hidden; /* Ensure scaled content is clipped by this container */
}

.minimapPreviewContent {
  width: 100%;
  background-color: transparent; /* Reverted */
  position: relative; /* Keep for stacking context if needed, or remove if not */
  /* z-index: 1 !important; */ /* Reverted/Removed, let natural flow dictate or set explicitly if still needed */
  font-size: 1px;
  line-height: 1.2;
  color: hsl(var(--muted-foreground));
  overflow: hidden;
  white-space: pre;
}

/*
  The styles for .minimapText, .minimapElement, and specific snapshot items
  are removed from this module. The snapshot content is now expected to be
  styled by global CSS rules in index.css, targeting classes like
  'minimap-outline-item' and 'outline-item-title' which are added directly
  in NewMinimap.tsx, similar to how the old minimap worked.
  This CSS module will style the main minimap containers and the viewport indicator.
*/

.minimapLoading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
  font-size: 10px;
}

.minimapWindow::-webkit-scrollbar {
  width: 7px;
}

.minimapWindow::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.minimapWindow::-webkit-scrollbar-thumb {
  background-color: #424242;
  border-radius: 4px;
  border: 1px solid #1e1e1e;
}

.minimapWindow::-webkit-scrollbar-thumb:hover {
  background-color: #555555;
}

.minimapWindow {
  scrollbar-width: thin;
  scrollbar-color: #424242 #1e1e1e;
}

.viewportIndicator {
  position: absolute;
  left: 0;
  right: 0;
  width: auto;
  background-color: hsla(var(--foreground), 0.2); /* Reverted: Semi-transparent foreground */
  border: 1px solid hsla(var(--foreground), 0.4); /* Reverted */
  z-index: 10; /* Reverted */
  pointer-events: none;
  border-radius: 2px;
  transition: top 0.05s linear, height 0.05s linear;
  opacity: 1; /* Reverted: Default opacity */
}

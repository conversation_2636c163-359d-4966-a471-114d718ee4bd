import { Citation } from '@/lib/types';
import type { Reference } from '@shared/schema';

// Citation style types
export type CitationStyle = 'apa' | 'mla' | 'chicago' | 'harvard' | 'ieee';

/**
 * Format authors for citation
 */
export function formatAuthors(authors: string[] = [], style: CitationStyle): string {
  if (!authors.length) return '';
  
  // Format authors based on style
  switch (style) {
    case 'apa':
      if (authors.length === 1) {
        // Last, F. M.
        const nameParts = authors[0].split(' ');
        if (nameParts.length < 2) return authors[0];
        
        const lastName = nameParts.pop() || '';
        const initials = nameParts.map(n => `${n.charAt(0)}.`).join(' ');
        return `${lastName}, ${initials}`;
      } else if (authors.length === 2) {
        // Last1, F. M. & Last2, F. M.
        const formattedAuthors = authors.map(author => {
          const nameParts = author.split(' ');
          if (nameParts.length < 2) return author;
          
          const lastName = nameParts.pop() || '';
          const initials = nameParts.map(n => `${n.charAt(0)}.`).join(' ');
          return `${lastName}, ${initials}`;
        });
        return `${formattedAuthors[0]} & ${formattedAuthors[1]}`;
      } else {
        // Last1, F. M., Last2, F. M., & Last3, F. M.
        const formattedAuthors = authors.map(author => {
          const nameParts = author.split(' ');
          if (nameParts.length < 2) return author;
          
          const lastName = nameParts.pop() || '';
          const initials = nameParts.map(n => `${n.charAt(0)}.`).join(' ');
          return `${lastName}, ${initials}`;
        });
        
        const lastAuthor = formattedAuthors.pop() || '';
        return `${formattedAuthors.join(', ')}, & ${lastAuthor}`;
      }
      
    case 'mla':
      if (authors.length === 1) {
        // Last, First M.
        const nameParts = authors[0].split(' ');
        if (nameParts.length < 2) return authors[0];
        
        const lastName = nameParts.pop() || '';
        const firstName = nameParts.join(' ');
        return `${lastName}, ${firstName}`;
      } else if (authors.length === 2) {
        // Last1, First1 M., and First2 M. Last2
        const [author1, author2] = authors;
        
        const nameParts1 = author1.split(' ');
        const lastName1 = nameParts1.pop() || '';
        const firstName1 = nameParts1.join(' ');
        
        return `${lastName1}, ${firstName1}, and ${author2}`;
      } else {
        // Last1, First1 M., et al.
        const nameParts = authors[0].split(' ');
        if (nameParts.length < 2) return `${authors[0]}, et al.`;
        
        const lastName = nameParts.pop() || '';
        const firstName = nameParts.join(' ');
        return `${lastName}, ${firstName}, et al.`;
      }
      
    case 'chicago':
      if (authors.length === 1) {
        // Last, First M.
        const nameParts = authors[0].split(' ');
        if (nameParts.length < 2) return authors[0];
        
        const lastName = nameParts.pop() || '';
        const firstName = nameParts.join(' ');
        return `${lastName}, ${firstName}`;
      } else if (authors.length === 2) {
        // Last1, First1 M., and First2 M. Last2
        const [author1, author2] = authors;
        
        const nameParts1 = author1.split(' ');
        const lastName1 = nameParts1.pop() || '';
        const firstName1 = nameParts1.join(' ');
        
        return `${lastName1}, ${firstName1}, and ${author2}`;
      } else if (authors.length <= 3) {
        // Last1, First1 M., First2 M. Last2, and First3 M. Last3
        const lastAuthor = authors.pop() || '';
        const firstAuthor = authors[0];
        
        const nameParts = firstAuthor.split(' ');
        if (nameParts.length < 2) 
          return `${firstAuthor}, ${authors.slice(1).join(', ')}, and ${lastAuthor}`;
        
        const lastName = nameParts.pop() || '';
        const firstName = nameParts.join(' ');
        
        return `${lastName}, ${firstName}, ${authors.slice(1).join(', ')}, and ${lastAuthor}`;
      } else {
        // Last1, First1, et al.
        const nameParts = authors[0].split(' ');
        if (nameParts.length < 2) return `${authors[0]}, et al.`;
        
        const lastName = nameParts.pop() || '';
        const firstName = nameParts.join(' ');
        return `${lastName}, ${firstName}, et al.`;
      }
      
    case 'harvard':
      if (authors.length === 1) {
        return authors[0];
      } else if (authors.length === 2) {
        return `${authors[0]} and ${authors[1]}`;
      } else if (authors.length === 3) {
        return `${authors[0]}, ${authors[1]} and ${authors[2]}`;
      } else {
        return `${authors[0]} et al.`;
      }
      
    case 'ieee':
      if (authors.length === 1) {
        // F. M. Last
        const nameParts = authors[0].split(' ');
        if (nameParts.length < 2) return authors[0];
        
        const lastName = nameParts.pop() || '';
        const initials = nameParts.map(n => `${n.charAt(0)}.`).join(' ');
        return `${initials} ${lastName}`;
      } else {
        // List all authors with commas (IEEE style typically lists all authors)
        const formattedAuthors = authors.map(author => {
          const nameParts = author.split(' ');
          if (nameParts.length < 2) return author;
          
          const lastName = nameParts.pop() || '';
          const initials = nameParts.map(n => `${n.charAt(0)}.`).join(' ');
          return `${initials} ${lastName}`;
        });
        
        return formattedAuthors.join(', ');
      }
      
    default:
      return authors.join(', ');
  }
}

/**
 * Format a citation into a proper bibliography entry
 */
export function formatCitation(citation: Citation | Reference, style: CitationStyle = 'apa'): string {
  // If there's only the reference field, return it as is
  if (!citation.title && !citation.authors && citation.reference) {
    return citation.reference;
  }
  
  // Prepare formatting elements based on citation type and style
  const { title, year, publisher, journal, volume, issue, pages, url, doi, type } = citation;
  
  // Format authors
  const formattedAuthors = formatAuthors(citation.authors || [], style);
  
  // Format based on style
  switch (style) {
    case 'apa':
      switch (type) {
        case 'book':
          return `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '. ' : ''}${publisher ? publisher + '.' : ''}`;
        
        case 'article':
        case 'journal':
          return `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '. ' : ''}${journal ? journal + ', ' : ''}${volume ? volume : ''}${issue ? '(' + issue + ')' : ''}${pages ? ', ' + pages : ''}. ${doi ? 'https://doi.org/' + doi : ''}`;
        
        case 'website':
          return `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '. ' : ''}${url ? 'Retrieved from ' + url : ''}`;
          
        case 'conference':
          return `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '. ' : ''}${publisher ? 'In ' + publisher : ''}${pages ? ', ' + pages : ''}.`;
          
        default:
          return citation.reference || `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '.' : ''}`;
      }
      
    case 'mla':
      switch (type) {
        case 'book':
          return `${formattedAuthors ? formattedAuthors + '. ' : ''}${title ? '"' + title + '." ' : ''}${publisher ? publisher + ', ' : ''}${year || 'n.d.'}.`;
        
        case 'article':
        case 'journal':
          return `${formattedAuthors ? formattedAuthors + '. ' : ''}${title ? '"' + title + '." ' : ''}${journal ? journal + ', ' : ''}${volume ? 'vol. ' + volume + ', ' : ''}${issue ? 'no. ' + issue + ', ' : ''}${year ? year + ', ' : ''}${pages ? 'pp. ' + pages : ''}.`;
        
        case 'website':
          return `${formattedAuthors ? formattedAuthors + '. ' : ''}${title ? '"' + title + '." ' : ''}${publisher ? publisher + ', ' : ''}${year || 'n.d.'}${url ? ', ' + url : ''}.`;
          
        default:
          return citation.reference || `${formattedAuthors ? formattedAuthors + '. ' : ''}${title ? '"' + title + '." ' : ''}${year ? year + '.' : ''}`;
      }
      
    case 'chicago':
      switch (type) {
        case 'book':
          return `${formattedAuthors ? formattedAuthors + '. ' : ''}${title ? title + '. ' : ''}${publisher ? publisher + ', ' : ''}${year || 'n.d.'}.`;
        
        case 'article':
        case 'journal':
          return `${formattedAuthors ? formattedAuthors + '. ' : ''}${title ? '"' + title + '." ' : ''}${journal ? journal + ' ' : ''}${volume ? volume : ''}${issue ? ', no. ' + issue + ' ' : ' '}(${year || 'n.d.'})${pages ? ': ' + pages : ''}.`;
        
        case 'website':
          return `${formattedAuthors ? formattedAuthors + '. ' : ''}${title ? '"' + title + '." ' : ''}${publisher ? publisher + ', ' : ''}${year ? year + '. ' : ''}${url ? url : ''}.`;
          
        default:
          return citation.reference || `${formattedAuthors ? formattedAuthors + '. ' : ''}${title ? title + '. ' : ''}${year ? year + '.' : ''}`;
      }
      
    case 'harvard':
      switch (type) {
        case 'book':
          return `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '. ' : ''}${publisher ? publisher + '.' : ''}`;
        
        case 'article':
        case 'journal':
          return `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '. ' : ''}${journal ? journal + ', ' : ''}${volume ? volume : ''}${issue ? '(' + issue + ')' : ''}${pages ? ', pp. ' + pages : ''}.`;
        
        case 'website':
          return `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '. ' : ''}${url ? 'Available at: ' + url : ''}.`;
          
        default:
          return citation.reference || `${formattedAuthors ? formattedAuthors + ' ' : ''}(${year || 'n.d.'}). ${title ? title + '.' : ''}`;
      }
      
    case 'ieee':
      switch (type) {
        case 'book':
          return `${formattedAuthors ? formattedAuthors + ', ' : ''}${title ? '"' + title + '," ' : ''}${publisher ? publisher + ', ' : ''}${year || 'n.d.'}.`;
        
        case 'article':
        case 'journal':
          return `${formattedAuthors ? formattedAuthors + ', ' : ''}${title ? '"' + title + '," ' : ''}${journal ? journal + ', ' : ''}${volume ? 'vol. ' + volume + ', ' : ''}${issue ? 'no. ' + issue + ', ' : ''}${pages ? 'pp. ' + pages + ', ' : ''}${year || 'n.d.'}.`;
        
        case 'website':
          return `${formattedAuthors ? formattedAuthors + ', ' : ''}${title ? '"' + title + '," ' : ''}${url ? 'Available: ' + url : ''}, ${year || 'n.d.'}.`;
          
        default:
          return citation.reference || `${formattedAuthors ? formattedAuthors + ', ' : ''}${title ? '"' + title + '," ' : ''}${year || 'n.d.'}.`;
      }
      
    default:
      return citation.reference || `${title || ''}`;
  }
}
import { useState, useEffect } from 'react';
import { AiSuggestion } from '@/components/ai/context-ai-popup';

export type AiContext = 
  | 'title' 
  | 'empty-section' 
  | 'text-selection' 
  | 'citation' 
  | 'reference-title'
  | 'reference-author'
  | 'reference-abstract'
  | 'reference-publisher'
  | 'note-title'
  | 'note-content'
  | 'journal-name'
  | 'none';

export function useAiContext(
  context: AiContext, 
  selectedText?: string
): AiSuggestion[] {
  const [suggestions, setSuggestions] = useState<AiSuggestion[]>([]);
  
  useEffect(() => {
    // Different suggestions based on context
    let contextSuggestions: AiSuggestion[] = [];
    
    switch (context) {
      case 'title':
        contextSuggestions = [
          {
            id: 'improve-title',
            icon: 'ri-edit-line',
            label: 'Revise Title',
            description: 'Make title more engaging and clear',
            isPremium: true
          },
          {
            id: 'academic-title',
            icon: 'ri-book-open-line',
            label: 'Make Academic',
            description: 'Format title for academic papers',
            isPremium: true
          }
        ];
        break;
        
      case 'empty-section':
        contextSuggestions = [
          {
            id: 'generate-draft',
            icon: 'ri-magic-line',
            label: 'Generate Draft',
            description: 'Create initial content for this section',
            isPremium: true
          },
          {
            id: 'outline-section',
            icon: 'ri-list-check',
            label: 'Create Outline',
            description: 'Generate a structured outline',
            isPremium: true
          }
        ];
        break;
        
      case 'text-selection':
        // Only show these if text is actually selected
        if (selectedText && selectedText.trim().length > 0) {
          contextSuggestions = [
            {
              id: 'improve-text',
              icon: 'ri-edit-line',
              label: 'Improve Writing',
              description: 'Enhance clarity and style',
              isPremium: true
            },
            {
              id: 'simplify-text',
              icon: 'ri-translate-2',
              label: 'Simplify',
              description: 'Make text clearer and more concise',
              isPremium: true
            },
            {
              id: 'expand-text',
              icon: 'ri-text-wrap',
              label: 'Expand',
              description: 'Add more detail and explanation',
              isPremium: true
            },
            {
              id: 'academic-text',
              icon: 'ri-book-open-line',
              label: 'Academic Style',
              description: 'Adapt to formal academic writing',
              isPremium: true
            }
          ];
        }
        break;
        
      case 'citation':
        contextSuggestions = [
          {
            id: 'find-citations',
            icon: 'ri-search-line',
            label: 'Find Citations',
            description: 'Suggest relevant citations',
            isPremium: true
          },
          {
            id: 'format-citation',
            icon: 'ri-file-list-line',
            label: 'Format Citations',
            description: 'Adjust to APA, MLA, or Chicago style',
            isPremium: true
          }
        ];
        break;
        
      case 'reference-title':
        contextSuggestions = [
          {
            id: 'suggest-title',
            icon: 'ri-book-open-line',
            label: 'Suggest Titles',
            description: 'Get title format suggestions',
            isPremium: true
          },
          {
            id: 'format-title',
            icon: 'ri-text-wrap',
            label: 'Format Title',
            description: 'Fix capitalization and style',
            isPremium: true
          }
        ];
        break;
        
      case 'reference-author':
        contextSuggestions = [
          {
            id: 'format-authors',
            icon: 'ri-user-line',
            label: 'Format Authors',
            description: 'Fix author name formatting',
            isPremium: true
          },
          {
            id: 'expand-authors',
            icon: 'ri-user-add-line',
            label: 'Find Co-authors',
            description: 'Suggest possible co-authors',
            isPremium: true
          }
        ];
        break;
        
      case 'reference-abstract':
        contextSuggestions = [
          {
            id: 'summarize-abstract',
            icon: 'ri-text-wrap',
            label: 'Summarize',
            description: 'Create a shorter abstract',
            isPremium: true
          },
          {
            id: 'enhance-abstract',
            icon: 'ri-edit-line',
            label: 'Enhance Abstract',
            description: 'Improve clarity and impact',
            isPremium: true
          }
        ];
        break;
        
      case 'reference-publisher':
        contextSuggestions = [
          {
            id: 'format-publisher',
            icon: 'ri-building-line',
            label: 'Format Publisher',
            description: 'Fix publisher name formatting',
            isPremium: true
          },
          {
            id: 'complete-publisher',
            icon: 'ri-search-line',
            label: 'Find Publisher Info',
            description: 'Complete publisher details',
            isPremium: true
          }
        ];
        break;
        
      case 'note-title':
        contextSuggestions = [
          {
            id: 'generate-note-title',
            icon: 'ri-magic-line',
            label: 'Generate Title',
            description: 'Create title from note content',
            isPremium: true
          },
          {
            id: 'improve-note-title',
            icon: 'ri-edit-line',
            label: 'Improve Title',
            description: 'Make title more descriptive',
            isPremium: true
          }
        ];
        break;
        
      case 'note-content':
        contextSuggestions = [
          {
            id: 'summarize-note',
            icon: 'ri-text-wrap',
            label: 'Summarize',
            description: 'Create a concise summary',
            isPremium: true
          },
          {
            id: 'expand-note',
            icon: 'ri-text-spacing',
            label: 'Expand Note',
            description: 'Add more details and context',
            isPremium: true
          },
          {
            id: 'academic-note',
            icon: 'ri-book-open-line',
            label: 'Academic Style',
            description: 'Convert to academic format',
            isPremium: true
          }
        ];
        break;
        
      case 'journal-name':
        contextSuggestions = [
          {
            id: 'find-journal',
            icon: 'ri-search-line',
            label: 'Find Journal',
            description: 'Suggest matching journals',
            isPremium: true
          },
          {
            id: 'format-journal',
            icon: 'ri-file-list-line',
            label: 'Format Name',
            description: 'Fix journal name formatting',
            isPremium: true
          }
        ];
        break;
        
      case 'none':
      default:
        // Default suggestions when no specific context
        contextSuggestions = [
          {
            id: 'improve-writing',
            icon: 'ri-edit-line',
            label: 'Improve Writing',
            description: 'Enhance clarity and style',
            isPremium: true
          },
          {
            id: 'generate-ideas',
            icon: 'ri-lightbulb-line',
            label: 'Generate Ideas',
            description: 'Brainstorm for your document',
            isPremium: true
          }
        ];
        break;
    }
    
    setSuggestions(contextSuggestions);
  }, [context, selectedText]);
  
  return suggestions;
}
import React, { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Note } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { PDFViewerBasic } from "./pdf-viewer-basic";

interface AttachmentListProps {
  note: Note;
  onDelete?: (type: 'image' | 'video' | 'pdf' | 'file' | 'primary', url: string) => void;
}

export function AttachmentList({ note, onDelete }: AttachmentListProps) {
  const [selectedAttachment, setSelectedAttachment] = useState<string | null>(null);
  const { toast } = useToast();
  
  // Combine all attachments from the note
  const allAttachments: string[] = [
    ...(note.imageUrls || []),
    ...(note.videoUrls || []),
    ...(note.fileUrls || []),
    ...(note.primaryAssetUrl ? [note.primaryAssetUrl] : [])
  ];
  
  if (allAttachments.length === 0) {
    return null;
  }
  
  // Helper function to determine attachment type from URL
  const getAttachmentType = (url: string): "image" | "video" | "pdf" | "file" => {
    // Check if the URL contains a blob prefix - these are temporary URLs
    if (url.startsWith('blob:')) {
      return "file"; // Default to file for blob URLs that haven't been properly processed
    }
    
    // Extract the file extension, handling URLs with query parameters
    const basePath = url.split('?')[0]; // Remove any query parameters
    const extension = basePath.split('.').pop()?.toLowerCase() || "";
    
    // More comprehensive file type detection
    const imageExtensions = ["png", "jpg", "jpeg", "gif", "svg", "webp", "bmp", "tiff", "tif", "ico"];
    const videoExtensions = ["mp4", "webm", "ogg", "mov", "avi", "mkv", "flv", "wmv", "m4v"];
    const documentExtensions = ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "md", "rtf"];
    
    if (imageExtensions.includes(extension)) {
      return "image";
    } else if (videoExtensions.includes(extension)) {
      return "video";
    } else if (extension === "pdf") {
      return "pdf";
    } else {
      // For documents or unknown types, check if it's a path that includes "uploads"
      // This handles files on the server that might not have recognizable extensions
      if (url.includes("/uploads/")) {
        return "file";
      }
      return "file";
    }
  };
  
  // Helper function to get file name from URL or attachment metadata
  const getFileName = (url: string): string => {
    // Check if the note contains attachment metadata with original filenames
    // First, search in imageUrls
    if (note.imageUrls && note.imageUrlsData) {
      const attachmentData = note.imageUrlsData.find(data => data.url === url);
      if (attachmentData && attachmentData.displayName) {
        return attachmentData.displayName;
      }
    }
    
    // Then search in videoUrls
    if (note.videoUrls && note.videoUrlsData) {
      const attachmentData = note.videoUrlsData.find(data => data.url === url);
      if (attachmentData && attachmentData.displayName) {
        return attachmentData.displayName;
      }
    }
    
    // Then search in fileUrls
    if (note.fileUrls && note.fileUrlsData) {
      const attachmentData = note.fileUrlsData.find(data => data.url === url);
      if (attachmentData && attachmentData.displayName) {
        return attachmentData.displayName;
      }
    }
    
    // Fallback to extracting from URL if metadata isn't available
    const fileName = url.split("/").pop() || "Attachment";
    
    // For files from the server with randomly generated IDs, make them more readable
    if (fileName.includes('.') && fileName.length > 20) {
      // Extract the extension
      const extension = fileName.split('.').pop()?.toLowerCase() || "";
      
      // Determine file type for better display
      const imageExtensions = ["png", "jpg", "jpeg", "gif", "svg", "webp", "bmp", "tiff", "tif", "ico"];
      const videoExtensions = ["mp4", "webm", "ogg", "mov", "avi", "mkv", "flv", "wmv", "m4v"];
      const documentExtensions = ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "rtf"];
      
      // Create a user-friendly name based on file type
      if (imageExtensions.includes(extension)) {
        return `Image.${extension}`;
      } else if (videoExtensions.includes(extension)) {
        return `Video.${extension}`;
      } else if (documentExtensions.includes(extension)) {
        return `Document.${extension}`;
      } else if (extension === "txt" || extension === "md") {
        return `Text file.${extension}`;
      }
    }
    
    return fileName;
  };
  
  // New function to handle attachment deletion
  const handleDeleteAttachment = (url: string, e: React.MouseEvent) => {
    // Stop propagation to prevent opening the attachment 
    e.stopPropagation();
    
    // Determine the type of attachment to delete
    const type = getAttachmentType(url);
    
    // Get the filename for the toast notification
    const fileName = getFileName(url);
    
    // If onDelete prop is provided, use it and return early
    if (onDelete) {
      // Convert pdf type to file type for backward compatibility with existing components
      const fileType = type === 'pdf' ? 'file' : type;
      onDelete(fileType as 'image' | 'video' | 'pdf' | 'file' | 'primary', url);
      
      // Close the attachment viewer if it's showing the deleted attachment
      if (selectedAttachment === url) {
        setSelectedAttachment(null);
      }
      
      return;
    }
    
    // Otherwise, continue with default deletion logic
    // Create a new note object with the attachment removed
    let updatedNote = { ...note };
    
    // Remove from the appropriate array and metadata
    if (type === "image") {
      // Find the index of the url in the imageUrls array
      const index = note.imageUrls?.findIndex(imgUrl => imgUrl === url) ?? -1;
      
      if (index !== -1 && note.imageUrls) {
        // Remove the url from imageUrls
        updatedNote.imageUrls = [...note.imageUrls.slice(0, index), ...note.imageUrls.slice(index + 1)];
        
        // Remove metadata if it exists
        if (note.imageUrlsData) {
          updatedNote.imageUrlsData = [...note.imageUrlsData.slice(0, index), ...note.imageUrlsData.slice(index + 1)];
        }
      }
    } else if (type === "video") {
      // Find the index of the url in the videoUrls array
      const index = note.videoUrls?.findIndex(vidUrl => vidUrl === url) ?? -1;
      
      if (index !== -1 && note.videoUrls) {
        // Remove the url from videoUrls
        updatedNote.videoUrls = [...note.videoUrls.slice(0, index), ...note.videoUrls.slice(index + 1)];
        
        // Remove metadata if it exists
        if (note.videoUrlsData) {
          updatedNote.videoUrlsData = [...note.videoUrlsData.slice(0, index), ...note.videoUrlsData.slice(index + 1)];
        }
      }
    } else if (type === "file") {
      // Find the index of the url in the fileUrls array
      const index = note.fileUrls?.findIndex(fileUrl => fileUrl === url) ?? -1;
      
      if (index !== -1 && note.fileUrls) {
        // Remove the url from fileUrls
        updatedNote.fileUrls = [...note.fileUrls.slice(0, index), ...note.fileUrls.slice(index + 1)];
        
        // Remove metadata if it exists
        if (note.fileUrlsData) {
          updatedNote.fileUrlsData = [...note.fileUrlsData.slice(0, index), ...note.fileUrlsData.slice(index + 1)];
        }
      }
    } else if (url === note.primaryAssetUrl) {
      // Handle primary asset
      updatedNote.primaryAssetUrl = undefined;
      updatedNote.primaryAssetData = undefined;
    }
    
    // Delete the file from the server
    const fileId = url.split('/').pop()?.split('.')[0];
    if (fileId) {
      // Show deletion in progress toast
      toast({
        title: "Deleting attachment",
        description: `Removing ${fileName}...`,
        duration: 2000,
      });
      
      fetch(`/api/uploads/${fileId}`, { method: 'DELETE' })
        .then(response => {
          if (!response.ok) {
            console.error(`Failed to delete file ${fileId} from server:`, response.statusText);
            toast({
              title: "Error",
              description: `Failed to delete ${fileName} from server.`,
              variant: "destructive",
              duration: 3000,
            });
          } else {
            toast({
              title: "Attachment deleted",
              description: `Successfully removed ${fileName}.`,
              duration: 2000,
            });
          }
        })
        .catch(error => {
          console.error(`Error deleting file ${fileId} from server:`, error);
          toast({
            title: "Error",
            description: `Failed to delete ${fileName} from server.`,
            variant: "destructive",
            duration: 3000,
          });
        });
    }
    
    // Emit an event to update the note
    const customEvent = new CustomEvent('attachment-deleted', { 
      detail: { note: updatedNote }
    });
    document.dispatchEvent(customEvent);
  };

  return (
    <>
      {/* Simple bulleted list of attachments - no header */}
      <div>
        <ul className="list-disc pl-5 space-y-1">
          {allAttachments.map((url: string, index: number) => {
            const type = getAttachmentType(url);
            const fileName = getFileName(url);
            
            // Icon based on file type
            let icon = "ri-file-line";
            if (type === "image") icon = "ri-image-line";
            if (type === "video") icon = "ri-video-line";
            if (type === "pdf") icon = "ri-file-pdf-line";
            
            return (
              <li key={index} className="text-sm flex items-center justify-between pr-2 group">
                <button 
                  onClick={() => setSelectedAttachment(url)}
                  className="flex items-center hover:underline text-foreground cursor-pointer transition-colors"
                >
                  <i className={`${icon} mr-1 text-foreground`}></i>
                  {fileName}
                </button>
                <button 
                  onClick={(e) => handleDeleteAttachment(url, e)}
                  className="text-destructive hover:text-destructive/80 opacity-0 group-hover:opacity-100 transition-opacity"
                  title="Delete attachment"
                >
                  <i className="ri-delete-bin-line"></i>
                </button>
              </li>
            );
          })}
        </ul>
      </div>
      
      {/* Modal for showing attachments */}
      <Dialog open={!!selectedAttachment} onOpenChange={(open) => !open && setSelectedAttachment(null)}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] p-0 overflow-hidden">
          
          {selectedAttachment && (
            <div className="w-full flex flex-col items-center justify-center">
              {getAttachmentType(selectedAttachment) === "image" && (
                <>
                  <div className="w-full h-[70vh] bg-background flex items-center justify-center overflow-auto">
                    <img 
                      src={selectedAttachment} 
                      alt="Attachment" 
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                  <div className="w-full p-4 bg-background border-t border-border flex justify-between items-center">
                    <div className="flex items-center">
                      <i className="ri-image-line text-lg mr-2 text-muted-foreground"></i>
                      <span className="text-sm text-foreground">{getFileName(selectedAttachment)}</span>
                    </div>
                    <a 
                      href={selectedAttachment} 
                      download 
                      target="_blank"
                      className="inline-flex items-center px-3 py-1 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors text-sm"
                    >
                      <i className="ri-download-line mr-2"></i>
                      Download
                    </a>
                  </div>
                </>
              )}
              {getAttachmentType(selectedAttachment) === "video" && (
                <>
                  <div className="w-full h-[70vh] bg-background flex items-center justify-center">
                    <video 
                      src={selectedAttachment} 
                      controls
                      className="max-w-full max-h-full"
                    />
                  </div>
                  <div className="w-full p-4 bg-background border-t border-border flex justify-between items-center">
                    <div className="flex items-center">
                      <i className="ri-video-line text-lg mr-2 text-muted-foreground"></i>
                      <span className="text-sm text-foreground">{getFileName(selectedAttachment)}</span>
                    </div>
                    <a 
                      href={selectedAttachment} 
                      download 
                      target="_blank"
                      className="inline-flex items-center px-3 py-1 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors text-sm"
                    >
                      <i className="ri-download-line mr-2"></i>
                      Download
                    </a>
                  </div>
                </>
              )}
              {getAttachmentType(selectedAttachment) === "pdf" && (
                <div className="w-full h-[80vh]">
                  <PDFViewerBasic fileUrl={selectedAttachment} />
                </div>
              )}
              {getAttachmentType(selectedAttachment) === "file" && (
                <div className="flex flex-col items-center justify-center p-12 w-full">
                  <div className="w-24 h-24 bg-muted rounded-lg flex items-center justify-center mb-6">
                    <i className="ri-file-line text-5xl text-primary"></i>
                  </div>
                  <h3 className="text-lg font-medium text-foreground mb-2">{getFileName(selectedAttachment)}</h3>
                  <p className="text-sm text-muted-foreground mb-6">Click the button below to download this file</p>
                  <a 
                    href={selectedAttachment} 
                    download 
                    target="_blank"
                    className="inline-flex items-center px-5 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                  >
                    <i className="ri-download-line mr-2"></i>
                    Download File
                  </a>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
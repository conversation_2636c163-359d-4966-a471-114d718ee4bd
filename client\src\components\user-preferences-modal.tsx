import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { UserPreferences } from '@shared/schema';
import { useAuth } from '@/hooks/use-auth';
import { usePreferences } from '@/hooks/use-preferences';
import { useMemo } from 'react';
import { HexColorPicker } from 'react-colorful';

const colorSchemeOptions = ["Light", "Dark"];
const fontFaceOptions = ["Aptos", "Arial", "Calibri", "Times New Roman", "Georgia"];
const fontScaleOptions = ["75%", "90%", "100%", "110%", "125%", "150%"];
const vocalPitchOptions = ["Soprano", "Alto", "Tenor", "Baritone", "Bass"];
const localeOptions = ["US Canada English", "UK English", "Australian English", "Indian English"];
const speechStyleOptions = ["Precise and articulate", "Casual and relaxed", "Energetic", "Professional"];

interface UserPreferencesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function UserPreferencesModal({ isOpen, onClose }: UserPreferencesModalProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const { preferences: globalPreferences, updatePreferences: savePreferences, speakText } = usePreferences();
  const [isSaving, setIsSaving] = useState(false);
  const [localPreferences, setLocalPreferences] = useState<UserPreferences>({
    colorScheme: "Light",
    customThemeColor: "#3b82f6", // Default blue primary color
    fontFaceLabels: "Aptos",
    fontScaleLabels: "100%",
    fontFaceUserText: "Aptos",
    fontScaleUserText: "100%",
    textToSpeechVocalPitch: "Baritone",
    textToSpeechLocale: "US Canada English",
    textToSpeechStyle: "Precise and articulate",
    subscriberNameOptions: {
      title: true,
      firstName: true,
      middleName: true,
      lastName: true,
      suffix: true,
    }
  });
  const [sampleText, setSampleText] = useState<string>("This is a sample text that demonstrates how the text-to-speech feature will sound with your current settings.");
  const previewTextRef = useRef<HTMLTextAreaElement>(null);
  const originalPreferences = useRef<UserPreferences | null>(null);

  useEffect(() => {
    if (isOpen && globalPreferences) {
      originalPreferences.current = {...globalPreferences};
      setLocalPreferences({...globalPreferences});
    }
  }, [isOpen, globalPreferences]);
  
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      const dropdown = document.getElementById('colorPickerDropdown');
      const colorSwatch = document.getElementById('colorSwatch');
      
      if (dropdown && !dropdown.classList.contains('hidden')) {
        if (event.target instanceof Node && 
            !dropdown.contains(event.target) && 
            colorSwatch && !colorSwatch.contains(event.target)) {
          dropdown.classList.add('hidden');
        }
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleClose = (open: boolean) => {
    if (!open && originalPreferences.current) {
      console.log("Reverting to original preferences:", originalPreferences.current);
      savePreferences(originalPreferences.current, true);
      toast({
        title: "Changes canceled",
        description: "Your preferences have been reverted to the previous settings."
      });
      setLocalPreferences({...originalPreferences.current});
    }
    onClose();
  };

  const handleSave = () => {
    setIsSaving(true);
    savePreferences(localPreferences, true);
    originalPreferences.current = {...localPreferences};
    setTimeout(() => {
      setIsSaving(false);
      onClose();
    }, 500);
  };

  const handleRestoreDefaults = () => {
    const defaultPrefs: UserPreferences = { // Explicitly type for clarity
      colorScheme: "Light",
      customThemeColor: "#3b82f6",
      fontFaceLabels: "Aptos",
      fontScaleLabels: "100%",
      fontFaceUserText: "Aptos",
      fontScaleUserText: "100%",
      textToSpeechVocalPitch: "Baritone",
      textToSpeechLocale: "US Canada English",
      textToSpeechStyle: "Precise and articulate",
      subscriberNameOptions: {
        title: true,
        firstName: true,
        middleName: true,
        lastName: true,
        suffix: true,
      }
    };
    
    setLocalPreferences(defaultPrefs);
    savePreferences(defaultPrefs, true);
  };
  
  const handleResetColor = () => {
    const updatedPreferences = {
      ...localPreferences,
      customThemeColor: "#3b82f6" 
    };
    
    setLocalPreferences(updatedPreferences);
    savePreferences(updatedPreferences, true);
    
    toast({
      title: "Theme color reset",
      description: "Your theme color has been reset to the default blue."
    });
  };

  const handleValueChange = (field: keyof UserPreferences, value: any) => {
    const updatedPreferences = {
      ...localPreferences,
      [field]: value
    };
    
    setLocalPreferences(updatedPreferences);
    savePreferences(updatedPreferences);
  };

  const handleCheckboxChange = (field: keyof UserPreferences['subscriberNameOptions'], checked: boolean) => {
    const updatedPreferences = {
      ...localPreferences,
      subscriberNameOptions: {
        ...localPreferences.subscriberNameOptions,
        [field]: checked
      }
    };
    
    setLocalPreferences(updatedPreferences);
    savePreferences(updatedPreferences);
  };
  
  const handlePreviewSpeech = () => {
    if (previewTextRef.current) {
      speakText(previewTextRef.current.value);
    } else {
      speakText(sampleText);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose} modal={true}>
      {isOpen && <div className="fixed inset-0 bg-background/80 z-50" aria-hidden="true" />}
      <DialogContent className="max-w-4xl border-border border shadow-xl" 
        style={{ 
          // @ts-ignore 
          backgroundColor: "hsl(var(--background))", 
          // @ts-ignore 
          color: "hsl(var(--foreground))"
        }}>
        <DialogHeader>
          <DialogTitle className="text-xl">User Preferences</DialogTitle>
          <DialogDescription>
            Customize your work environment and document settings.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="appearance" className="py-4">
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="text-to-speech">Text to Speech</TabsTrigger>
            <TabsTrigger value="subscriber">Subscriber Name</TabsTrigger>
          </TabsList>
          
          {/* Appearance Tab */}
          <TabsContent value="appearance" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Color Settings */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="colorScheme">Color scheme</Label>
                  <Select
                    value={localPreferences.colorScheme}
                    onValueChange={(value) => handleValueChange('colorScheme', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select color scheme" />
                    </SelectTrigger>
                    <SelectContent>
                      {colorSchemeOptions.map(option => (
                        <SelectItem key={option} value={option}>{option}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Primary Theme Color */}
                <div className="space-y-3">
                  <Label htmlFor="customThemeColor">Primary Theme Color</Label>
                  <div className="flex items-center w-full space-x-2">
                    <div 
                      id="colorSwatch"
                      className="w-10 h-10 rounded-md border cursor-pointer relative overflow-hidden group"
                      style={{ 
                        backgroundColor: localPreferences.customThemeColor,
                        // @ts-ignore 
                        borderColor: "hsl(var(--border))"
                      }}
                      onClick={() => {
                        setTimeout(() => {
                          const dropdown = document.getElementById('colorPickerDropdown');
                          if (dropdown) {
                            dropdown.classList.toggle('hidden');
                            if (!dropdown.classList.contains('hidden')) {
                              dropdown.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                            }
                          }
                        }, 10);
                      }}
                    >
                        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center bg-foreground/10 transition-opacity">
                          <i className="ri-paint-brush-line text-background text-lg"></i>
                      </div>
                    </div>
                    <Input
                      value={localPreferences.customThemeColor}
                      onChange={(e) => handleValueChange('customThemeColor', e.target.value)}
                      className="flex-1 font-mono text-sm"
                    />
                    <Button 
                      variant="outline"
                      onClick={handleResetColor}
                      className="whitespace-nowrap"
                    >
                      Reset
                    </Button>
                  </div>
                  
                  <div 
                    id="colorPickerDropdown" 
                    className="hidden relative z-10 mt-2 w-full"
                  >
                    <div className="absolute top-0 left-0 border rounded-md shadow-lg p-3 bg-card" 
                         // @ts-ignore 
                         style={{borderColor: "hsl(var(--border))"}}>
                      <HexColorPicker 
                        color={localPreferences.customThemeColor} 
                        onChange={(color) => handleValueChange('customThemeColor', color)}
                        style={{ width: '100%', height: '150px' }} 
                      />
                      
                      <div className="mt-3 flex flex-wrap gap-2 justify-center">
                        {["#3b82f6", "#0ea5e9", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#ec4899", "#000000"].map((color) => (
                          <div 
                            key={color}
                            className="w-6 h-6 rounded-full border cursor-pointer transition-transform hover:scale-110"
                            // @ts-ignore 
                            style={{ backgroundColor: color, borderColor: "hsl(var(--border))" }}
                            onClick={() => handleValueChange('customThemeColor', color)}
                            title={color}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Color Pickers - Only shown when custom theme is selected */}
                {/* These are removed as per Option A for simplification */}
                {/* {localPreferences.colorScheme === "Custom theme" && (
                  <>
                    <div className="space-y-3 border-t pt-3 mt-4">
                      <Label htmlFor="customSecondaryColor">Secondary Color</Label>
                      ...
                    </div>
                    <div className="space-y-3 mt-4">
                      <Label htmlFor="customAccentColor">Accent Color</Label>
                      ...
                    </div>
                    <div className="space-y-3 mt-4">
                      <Label htmlFor="customBackgroundColor">Background Color</Label>
                      ...
                    </div>
                    <div className="space-y-3 border-t pt-3 mt-4">
                      <Label className="text-sm font-medium">Icon Colors</Label>
                      ...
                    </div>
                  </>
                )} */}
              </div>
              
              {/* Font Settings */}
              <div className="space-y-4">
                <Accordion type="single" collapsible defaultValue="labels">
                  <AccordionItem value="labels">
                    <AccordionTrigger className="text-sm">Label Fonts</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3 pt-2">
                        <div className="space-y-2">
                          <Label htmlFor="fontFaceLabels">Font face</Label>
                          <Select
                            value={localPreferences.fontFaceLabels}
                            onValueChange={(value) => handleValueChange('fontFaceLabels', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select font face" />
                            </SelectTrigger>
                            <SelectContent>
                              {fontFaceOptions.map(option => (
                                <SelectItem key={option} value={option}>{option}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="fontScaleLabels">Font scale</Label>
                          <Select
                            value={localPreferences.fontScaleLabels}
                            onValueChange={(value) => handleValueChange('fontScaleLabels', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select font scale" />
                            </SelectTrigger>
                            <SelectContent>
                              {fontScaleOptions.map(option => (
                                <SelectItem key={option} value={option}>{option}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="usertext">
                    <AccordionTrigger className="text-sm">User Text Fonts</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3 pt-2">
                        <div className="space-y-2">
                          <Label htmlFor="fontFaceUserText">Font face</Label>
                          <Select
                            value={localPreferences.fontFaceUserText}
                            onValueChange={(value) => handleValueChange('fontFaceUserText', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select font face" />
                            </SelectTrigger>
                            <SelectContent>
                              {fontFaceOptions.map(option => (
                                <SelectItem key={option} value={option}>{option}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="fontScaleUserText">Font scale</Label>
                          <Select
                            value={localPreferences.fontScaleUserText}
                            onValueChange={(value) => handleValueChange('fontScaleUserText', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select font scale" />
                            </SelectTrigger>
                            <SelectContent>
                              {fontScaleOptions.map(option => (
                                <SelectItem key={option} value={option}>{option}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            </div>
          </TabsContent>
          
          {/* Text to Speech Tab */}
          <TabsContent value="text-to-speech" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="textToSpeechVocalPitch">Vocal pitch</Label>
                  <Select
                    value={localPreferences.textToSpeechVocalPitch}
                    onValueChange={(value) => handleValueChange('textToSpeechVocalPitch', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select vocal pitch" />
                    </SelectTrigger>
                    <SelectContent>
                      {vocalPitchOptions.map(option => (
                        <SelectItem key={option} value={option}>{option}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="textToSpeechLocale">Locale / Accent</Label>
                  <Select
                    value={localPreferences.textToSpeechLocale}
                    onValueChange={(value) => handleValueChange('textToSpeechLocale', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select locale" />
                    </SelectTrigger>
                    <SelectContent>
                      {localeOptions.map(option => (
                        <SelectItem key={option} value={option}>{option}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="textToSpeechStyle">Speech style</Label>
                  <Select
                    value={localPreferences.textToSpeechStyle}
                    onValueChange={(value) => handleValueChange('textToSpeechStyle', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select speech style" />
                    </SelectTrigger>
                    <SelectContent>
                      {speechStyleOptions.map(option => (
                        <SelectItem key={option} value={option}>{option}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="ttsPreview">Preview</Label>
                <Textarea 
                  id="ttsPreview"
                  ref={previewTextRef}
                  className="user-text mt-2 h-20"
                  defaultValue={sampleText}
                  placeholder="Enter text to preview text-to-speech"
                />
                <Button 
                  type="button" 
                  onClick={handlePreviewSpeech}
                  className="mt-2 w-full"
                  variant="secondary"
                >
                  <i className="ri-volume-up-line mr-2"></i>
                  Preview Speech
                </Button>
              </div>
            </div>
          </TabsContent>
          
          {/* Subscriber Name Tab */}
          <TabsContent value="subscriber" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="title"
                    checked={localPreferences.subscriberNameOptions.title}
                    onCheckedChange={(checked) => handleCheckboxChange('title', !!checked)}
                  />
                  <Label htmlFor="title">Title (e.g., Mr., Mrs., Ms., Dr.)</Label>
                </div>
                
                <div>
                  <Input 
                    className="mt-1" 
                    placeholder="Title"
                    disabled={!localPreferences.subscriberNameOptions.title}
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="firstName"
                    checked={localPreferences.subscriberNameOptions.firstName}
                    onCheckedChange={(checked) => handleCheckboxChange('firstName', !!checked)}
                  />
                  <Label htmlFor="firstName">First Name or Initial</Label>
                </div>
                
                <div>
                  <Input 
                    className="mt-1" 
                    placeholder="First name"
                    disabled={!localPreferences.subscriberNameOptions.firstName}
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="middleName"
                    checked={localPreferences.subscriberNameOptions.middleName}
                    onCheckedChange={(checked) => handleCheckboxChange('middleName', !!checked)}
                  />
                  <Label htmlFor="middleName">Middle Name(s) or Initial</Label>
                </div>
                
                <div>
                  <Input 
                    className="mt-1" 
                    placeholder="Middle name"
                    disabled={!localPreferences.subscriberNameOptions.middleName}
                  />
                </div>
                
                <div className="pt-1">
                  <Label htmlFor="lastName">Last Name</Label>
                </div>
                
                <div>
                  <Input 
                    className="mt-1" 
                    placeholder="Last name"
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="suffix"
                    checked={localPreferences.subscriberNameOptions.suffix}
                    onCheckedChange={(checked) => handleCheckboxChange('suffix', !!checked)}
                  />
                  <Label htmlFor="suffix">Suffix (e.g., Jr., Sr., II, III)</Label>
                </div>
                
                <div>
                  <Input 
                    className="mt-1" 
                    placeholder="Suffix"
                    disabled={!localPreferences.subscriberNameOptions.suffix}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="mt-6 flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleRestoreDefaults}
            className="mr-auto"
          >
            Restore Defaults
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handleClose(false)}
          >
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-foreground border-t-transparent"></div>
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
import React, { useState } from 'react';
import { usePara<PERSON>, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

export function RequestAccessPage() {
  const { documentId } = useParams<{ documentId: string }>();
  const location = useLocation();
  const documentTitle = location.state?.documentTitle || 'this document';
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleRequestAccess = async () => {
    setIsLoading(true);
    try {
      const response = await apiRequest('POST', `/api/documents/${documentId}/request-access`);
      if (response.ok) {
        toast({
          title: 'Request Sent',
          description: 'Your request to access the document has been sent to the owner.',
        });
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.message || 'Failed to send access request.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Access Denied</CardTitle>
          <CardDescription>You do not have permission to access {documentTitle}.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>To access this document, you can request access from the owner.</p>
        </CardContent>
        <CardFooter>
          <Button onClick={handleRequestAccess} disabled={isLoading} className="w-full">
            {isLoading ? 'Sending Request...' : 'Request Access'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

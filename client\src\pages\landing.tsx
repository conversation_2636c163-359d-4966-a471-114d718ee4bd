import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { <PERSON><PERSON><PERSON>, ArrowRight, Mail, CheckCircle, Loader2 } from "lucide-react";

export default function LandingPage() {
  const { user, isLoading } = useAuth();
  const [_, navigate] = useLocation();
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number; delay: number }>>([]);

  // Generate floating particles for background animation
  useEffect(() => {
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 5,
    }));
    setParticles(newParticles);
  }, []);

  // Redirect to dashboard if user is logged in
  useEffect(() => {
    if (user && !isLoading) {
      navigate("/");
    }
  }, [user, isLoading, navigate]);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubmitting(true);

    try {
      // Call our server endpoint which will handle the external API
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      });

      if (response.ok) {
        setIsSubscribed(true);
        setEmail("");
        console.log('Email successfully saved');
      } else {
        const errorData = await response.json();
        console.error('Failed to save email:', errorData.message);
        // Still show success to user for better UX, but log the error
        setIsSubscribed(true);
        setEmail("");
      }
    } catch (error) {
      console.error('Error saving email:', error);
      // Still show success to user for better UX, but log the error
      setIsSubscribed(true);
      setEmail("");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white relative overflow-hidden flex flex-col">
      {/* Paper Texture Background */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3Ccircle cx='37' cy='23' r='1'/%3E%3Ccircle cx='23' cy='37' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Animated Background Particles */}
      <div className="absolute inset-0">
        {particles.map((particle) => (
          <div
            key={particle.id}
            className="absolute w-0.5 h-0.5 bg-gray-400/30 rounded-full animate-pulse"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              animationDelay: `${particle.delay}s`,
              animationDuration: '4s',
            }}
          />
        ))}
      </div>

      {/* Subtle Shadow Orbs */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gray-100/50 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gray-50/80 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />

      {/* Header */}
      <header className="relative z-10 p-6 border-b border-gray-100">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-3">
            <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
              <path d="M16 2H8C4.691 2 2 4.691 2 8v13a1 1 0 0 0 1 1h13c3.309 0 6-2.691 6-6V8c0-3.309-2.691-6-6-6zm4 14c0 2.206-1.794 4-4 4H4V8c0-2.206 1.794-4 4-4h8c2.206 0 4 1.794 4 4v8z"/>
              <path d="M7 14.987v2a1 1 0 0 0 1 1h2a1 1 0 0 0 .707-.293l6.4-6.4a.999.999 0 0 0 0-1.414l-2-2a.999.999 0 0 0-1.414 0l-6.4 6.4A1 1 0 0 0 7 14.987zm3-5.274 1.293 1.293-4.986 4.986H8c-1.103 0-2-.897-2-2v-1.294l4-3.985z"/>
            </svg>
            <span className="text-2xl font-bold text-gray-900">Inspire</span>
          </div>
          <Button
            variant="outline"
            onClick={() => navigate("/auth")}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Sign In
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-6">
        <div className="text-center max-w-4xl mx-auto">
          {/* Coming Soon Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-50 rounded-full border border-gray-200 mb-8 shadow-sm">
            <div className="w-2 h-2 bg-gray-800 rounded-full animate-pulse" />
            <span className="text-gray-700 text-sm font-medium">Coming Soon</span>
          </div>

          {/* Main Headline */}
          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
            The Future of
            <span className="block text-gray-600 italic">
              Creative Expression
            </span>
          </h1>

          {/* Cryptic Description */}
          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
            Something extraordinary is coming. A new way to transform ideas into reality.
            Be among the first to experience the revolution.
          </p>
          {/* Email Signup */}
          {!isSubscribed ? (
            <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto mb-12">
              <div className="relative flex-1">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10 bg-white border-gray-300 text-gray-900 placeholder:text-gray-500 focus:border-gray-500 shadow-sm"
                  required
                />
              </div>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-gray-900 hover:bg-gray-800 text-white border-0 px-8 shadow-sm disabled:opacity-50"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    Notify Me
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </form>
          ) : (
            <div className="flex items-center justify-center gap-3 mb-12 p-4 bg-gray-50 rounded-lg border border-gray-200 max-w-md mx-auto shadow-sm">
              <CheckCircle className="w-5 h-5 text-gray-700" />
              <span className="text-gray-700 font-medium">You're on the list! We'll be in touch.</span>
            </div>
          )}

          {/* Cryptic Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-50 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-gray-200 shadow-sm">
                <div className="w-8 h-8 bg-gray-800 rounded-lg" />
              </div>
              <h3 className="text-gray-900 font-semibold mb-2">Intuitive</h3>
              <p className="text-gray-600 text-sm">Designed for the way you think</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-50 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-gray-200 shadow-sm">
                <div className="w-8 h-8 bg-gray-700 rounded-lg" />
              </div>
              <h3 className="text-gray-900 font-semibold mb-2">Powerful</h3>
              <p className="text-gray-600 text-sm">Beyond what you imagined possible</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gray-50 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-gray-200 shadow-sm">
                <div className="w-8 h-8 bg-gray-600 rounded-lg" />
              </div>
              <h3 className="text-gray-900 font-semibold mb-2">Revolutionary</h3>
              <p className="text-gray-600 text-sm">A completely new approach</p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="relative z-10 p-6 border-t border-gray-100 mt-auto">
        <div className="max-w-7xl mx-auto flex flex-col sm:flex-row justify-between items-center text-gray-500 text-sm">
          <div className="mb-4 sm:mb-0">
            © 2025 Inspire. All rights reserved.
          </div>
          <div className="flex gap-6">
            <a href="#" className="hover:text-gray-900 transition-colors">Privacy</a>
            <a href="#" className="hover:text-gray-900 transition-colors">Terms</a>
            <a href="#" className="hover:text-gray-900 transition-colors">Contact</a>
          </div>
        </div>
      </footer>
    </div>
  );
}
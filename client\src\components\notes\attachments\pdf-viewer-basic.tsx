import React, { useState } from 'react';
import { Button } from '@/components/ui/button';

interface PDFViewerBasicProps {
  fileUrl: string;
  height?: string | number;
  width?: string | number;
}

export function PDFViewerBasic({ fileUrl, height = '100%', width = '100%' }: PDFViewerBasicProps) {
  const [loading, setLoading] = useState(true);
  
  // Handle iframe load event
  const handleIframeLoad = () => {
    setLoading(false);
  };

  return (
    <div className="pdf-viewer flex flex-col" style={{ width, height }}>
      {/* PDF Controls */}
      <div className="flex justify-between items-center mb-3 bg-white dark:bg-neutral-800 p-2 rounded-t-md border-b border-neutral-200 dark:border-neutral-700">
        <div className="flex items-center">
          <i className="ri-file-pdf-line text-red-500 mr-2"></i>
          <span className="text-sm font-medium">PDF Document</span>
        </div>
        <a
          href={fileUrl}
          download
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex h-8 px-2 items-center justify-center text-xs rounded-md border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
        >
          <i className="ri-download-line mr-1"></i>
          Download PDF
        </a>
      </div>

      {/* PDF Viewer */}
      <div className="relative flex-1 overflow-hidden bg-neutral-100 dark:bg-neutral-900 rounded-b-md">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-neutral-50 dark:bg-neutral-800 bg-opacity-75 dark:bg-opacity-75 z-10">
            <div className="flex flex-col items-center">
              <i className="ri-loader-4-line animate-spin text-3xl text-primary mb-2"></i>
              <span className="text-sm text-neutral-600 dark:text-neutral-300">Loading PDF...</span>
            </div>
          </div>
        )}
        
        <iframe 
          src={fileUrl}
          style={{ width: '100%', height: '100%', border: 'none' }}
          title="PDF Viewer"
          onLoad={handleIframeLoad}
        />
      </div>
    </div>
  );
}
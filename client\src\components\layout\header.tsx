import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Share2 } from 'lucide-react';

interface HeaderProps {
  onSave: () => void;
  onSaveAndClose: () => void;
  onSpellCheck: () => void;
  onGrammarCheck: () => void;
  onShare: () => void;
  onUserMenuToggle: () => void;
}

export function Header({ onSave, onSaveAndClose, onSpellCheck, onGrammarCheck, onShare, onUserMenuToggle }: HeaderProps) {
  return (
    <header className="border-b border-neutral-200 bg-white shadow-sm py-3 px-4">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-2">
          <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M16 2H8C4.691 2 2 4.691 2 8v13a1 1 0 0 0 1 1h13c3.309 0 6-2.691 6-6V8c0-3.309-2.691-6-6-6zm4 14c0 2.206-1.794 4-4 4H4V8c0-2.206 1.794-4 4-4h8c2.206 0 4 1.794 4 4v8z"/>
            <path d="M7 14.987v2a1 1 0 0 0 1 1h2a1 1 0 0 0 .707-.293l6.4-6.4a.999.999 0 0 0 0-1.414l-2-2a.999.999 0 0 0-1.414 0l-6.4 6.4A1 1 0 0 0 7 14.987zm3-5.274 1.293 1.293-4.986 4.986H8c-1.103 0-2-.897-2-2v-1.294l4-3.985z"/>
          </svg>
          <h1 className="text-xl font-semibold text-neutral-900">Inspire</h1>
        </div>

        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className="flex items-center gap-1 text-neutral-600 hover:text-primary-600"
            onClick={onSave}
          >
            <i className="ri-save-line"></i>
            <span>Save</span>
          </Button>

          <Button 
            variant="ghost" 
            size="sm" 
            className="flex items-center gap-1 text-neutral-600 hover:text-primary-600"
            onClick={onSaveAndClose}
          >
            <i className="ri-save-3-line"></i>
            <span>Save & Close</span>
          </Button>
          
          <div className="h-5 border-r border-neutral-200"></div>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="text-neutral-600 hover:text-primary-600"
                  onClick={onSpellCheck}
                >
                  <i className="ri-spell-check-line"></i>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Check spelling</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="text-neutral-600 hover:text-primary-600"
                  onClick={onGrammarCheck}
                >
                  <i className="ri-file-text-line"></i>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Check grammar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="h-5 border-r border-neutral-200"></div>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="flex items-center gap-1 text-neutral-600 hover:text-primary-600"
                  onClick={onShare}
                >
                  <Share2 className="h-4 w-4" />
                  <span>Share</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Share document</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="h-5 border-r border-neutral-200"></div>
          
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-neutral-600 hover:text-primary-600"
            onClick={onUserMenuToggle}
          >
            <i className="ri-user-line"></i>
          </Button>
        </div>
      </div>
    </header>
  );
}

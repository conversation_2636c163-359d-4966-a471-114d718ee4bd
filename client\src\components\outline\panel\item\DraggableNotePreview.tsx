import React, { useState } from 'react';
import { Note, OutlineItem } from '@/lib/types';
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from '@/components/ui/collapsible';
import { WysiwygNoteEditor } from '@/components/notes/editor/wysiwyg';
import { InlineMediaViewer } from './InlineMediaViewer'; // Added import
// Assuming Button is used for delete/duplicate, though icons are more likely for compactness
// import { Button } from '@/components/ui/button';

// Helper function (copied from OutlineItem)
function generateSnippet(htmlContent: string, maxLength: number = 70): string {
  if (!htmlContent) return "";
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = htmlContent;
  let text = tempDiv.textContent || tempDiv.innerText || "";
  text = text.replace(/\s+/g, ' ').trim();
  if (text.length > maxLength) {
    return text.substring(0, maxLength) + "...";
  }
  return text;
}

export interface DraggableNotePreviewProps {
  note: Note;
  originalOutlineItemId: string;
  onNoteDelete: (noteId: string) => void;
  onNoteDuplicate: (noteId: string) => void;
  onNoteUpdate: (updatedNote: Note, forceSaveNow?: boolean) => void;
  onFileUpload: (file: File) => Promise<string>;
  allOutlineItems: OutlineItem[];
  searchTerm?: string;
  highlightTarget?: 'title' | 'content' | null;
  // Note drag and drop props
  onNoteDragStart?: (note: Note) => void;
  onNoteDragOver?: (targetType: 'note' | 'outline-item', targetId: string, position?: 'before' | 'after') => void;
  onNoteDrop?: (draggedNoteId: string, targetType: 'note' | 'outline-item', targetId: string, position?: 'before' | 'after') => void;
  onNoteDragEnd?: () => void;
  noteDragOverTarget?: {type: 'note' | 'outline-item'; id: string; position?: 'before' | 'after'} | null;
  draggedNote?: Note | null;
}

export function DraggableNotePreview({
  note,
  originalOutlineItemId,
  onNoteDelete,
  onNoteDuplicate,
  onNoteUpdate,
  onFileUpload,
  allOutlineItems,
  searchTerm,
  highlightTarget,
  // Note drag and drop props
  onNoteDragStart,
  onNoteDragOver,
  onNoteDrop,
  onNoteDragEnd,
  noteDragOverTarget,
  draggedNote,
}: DraggableNotePreviewProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const highlightedNoteTitle = React.useMemo(() => {
    if (searchTerm && note.title) {
      const searchPattern = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      if (!searchPattern) return note.title;
      const parts = note.title.split(new RegExp(`(${searchPattern})`, 'gi'));
      return parts.map((part, index) =>
        part.toLowerCase() === searchTerm.toLowerCase() ? (
          <mark key={index} className="bg-yellow-300 dark:bg-yellow-500 px-0.5 py-0 rounded text-black">
            {part}
          </mark>
        ) : (
          part
        )
      );
    }
    return note.title || 'Untitled Note';
  }, [note.title, searchTerm]);

  const highlightedSnippet = React.useMemo(() => {
    const snippet = generateSnippet(note.content);
    if (searchTerm && snippet) {
      const searchPattern = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      if (!searchPattern) return snippet;
      const parts = snippet.split(new RegExp(`(${searchPattern})`, 'gi'));
      return parts.map((part, index) =>
        part.toLowerCase() === searchTerm.toLowerCase() ? (
          <mark key={index} className="bg-yellow-300 dark:bg-yellow-500 px-0.5 py-0 rounded text-black">
            {part}
          </mark>
        ) : (
          part
        )
      );
    }
    return snippet;
  }, [note.content, searchTerm]);

  // Native HTML5 drag handlers
  const handleDragStart = (e: React.DragEvent) => {
    // Prevent default to ensure drag works
    e.stopPropagation();

    e.dataTransfer.setData('text/plain', note.id);
    e.dataTransfer.setData('application/json', note.id);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.dropEffect = 'move';

    // Set a drag image to make it clear what's being dragged
    const dragImage = document.createElement('div');
    dragImage.textContent = `📝 ${note.title}`;
    dragImage.style.position = 'absolute';
    dragImage.style.top = '-1000px';
    dragImage.style.background = 'white';
    dragImage.style.border = '1px solid #ccc';
    dragImage.style.padding = '4px 8px';
    dragImage.style.borderRadius = '4px';
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 0, 0);

    // Clean up drag image after a short delay
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);

    if (onNoteDragStart) {
      onNoteDragStart(note);
    }
  };

  const handleDragEnd = () => {
    if (onNoteDragEnd) onNoteDragEnd();
  };

  const handleDragOver = (e: React.DragEvent, position: 'before' | 'after') => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = 'move';

    if (onNoteDragOver) onNoteDragOver('note', note.id, position);
  };

  const handleDrop = (e: React.DragEvent, position: 'before' | 'after') => {
    e.preventDefault();
    e.stopPropagation();
    const draggedNoteId = e.dataTransfer.getData('application/json');

    if (onNoteDragOver) onNoteDragOver('note', ''); // Clear drag over state

    if (draggedNoteId && draggedNoteId !== note.id && onNoteDrop) {
      onNoteDrop(draggedNoteId, 'note', note.id, position);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    const relatedTarget = e.relatedTarget as Node;
    if (!relatedTarget || !e.currentTarget.contains(relatedTarget)) {
      if (onNoteDragOver) onNoteDragOver('note', '');
    }
  };

  const isDragging = draggedNote?.id === note.id;
  const isDropTarget = noteDragOverTarget?.type === 'note' && noteDragOverTarget.id === note.id;

  // Determine note icon based on type (simplified version, can be expanded)
  const getNoteTypeIcon = (noteType: Note['type']) => {
    switch (noteType) {
      case 'text': return <i className="ri-file-text-line text-muted-foreground"></i>;
      case 'image': return <i className="ri-image-line text-muted-foreground"></i>;
      case 'video': return <i className="ri-film-line text-muted-foreground"></i>;
      case 'file': return <i className="ri-file-3-line text-muted-foreground"></i>;
      default: return <i className="ri-file-text-line text-muted-foreground"></i>;
    }
  };

  return (
    <div className="relative">
      {/* Drop zone before note */}
      <div
        className={`h-1 -mb-0.5 ${
          isDropTarget && noteDragOverTarget?.position === 'before'
            ? 'bg-blue-400 rounded-sm opacity-75'
            : 'hover:bg-blue-200 opacity-0 hover:opacity-50'
        } transition-all duration-150 relative z-10`}
        onDragOver={(e) => {
          // Only handle note drags, not outline drags
          if (draggedNote) {
            handleDragOver(e, 'before');
          }
        }}
        onDrop={(e) => {
          // Only handle note drags, not outline drags
          if (draggedNote) {
            handleDrop(e, 'before');
          }
        }}
        onDragLeave={handleDragLeave}
      >
        {isDropTarget && noteDragOverTarget?.position === 'before' && (
          <div className="text-xs text-blue-600 font-medium px-1 py-0.5 bg-blue-100 rounded border border-blue-400 border-dashed absolute -top-1 left-0 right-0 text-center">
            ↑ DROP BEFORE
          </div>
        )}
      </div>

      <Collapsible open={isExpanded} onOpenChange={setIsExpanded} className="group relative">
        <div
          key={note.id}
          className={`p-1.5 rounded text-xs border border-transparent hover:border-border hover:bg-accent ${
            isDragging ? 'opacity-50 shadow-lg' : ''
          }`}
        >
        {/* Drag Handle - Outside of CollapsibleTrigger to avoid conflicts */}
        <div className="flex items-start space-x-1.5 w-full">
          <div
            className="w-6 h-6 cursor-grab opacity-50 group-hover:opacity-70 hover:opacity-100 text-muted-foreground self-center flex-shrink-0 select-none bg-transparent hover:bg-gray-100 rounded transition-all flex items-center justify-center"
            draggable
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onClick={(e) => {
              e.stopPropagation(); // Prevent any parent click handlers
              e.preventDefault();
            }}
            onMouseDown={(e) => {
              console.log('Drag handle mousedown');
              e.stopPropagation(); // Prevent triggering collapsible
            }}
            onContextMenu={(e) => {
              e.preventDefault(); // Prevent context menu
            }}
            aria-label="Drag note to re-link"
            style={{
              touchAction: 'none',
              userSelect: 'none',
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none'
            }}
            title="Drag to move note"
          >
            <i className="ri-drag-move-2-line text-sm"></i>
          </div>

          <CollapsibleTrigger asChild className="flex-1 min-w-0">
            <div
              className="flex items-start space-x-1.5 cursor-pointer flex-1 min-w-0"
              title={`Expand/Collapse note: ${note.title}`}
            >

            {/* Note Icon */}
            <div className="flex-shrink-0 mt-0.5"> {/* Removed onClick */}
              {getNoteTypeIcon(note.type)}
            </div>

            {/* Note Title and Snippet */}
            <div className="flex-1 min-w-0"> {/* Removed onClick */}
              <p className={`font-medium truncate ${highlightTarget === 'title' ? 'search-highlight-target' : ''}`}>{highlightedNoteTitle}</p>
              {note.type === 'text' && note.content && (
                <p className={`text-muted-foreground whitespace-normal break-words text-[0.7rem] leading-snug ${highlightTarget === 'content' ? 'search-highlight-target' : ''}`}>
                  {highlightedSnippet}
                </p>
              )}
          {/* Simplified attachment display for brevity */}
          {note.type === 'image' && (note.primaryAssetUrl || (note.imageUrls && note.imageUrls.length > 0)) && (
            <img src={note.primaryAssetUrl || note.imageUrls![0]} alt={note.title || "Image preview"} className="w-10 h-10 object-cover rounded mt-0.5 border" />
          )}
          {note.type === 'video' && (note.primaryAssetUrl || (note.videoUrls && note.videoUrls.length > 0)) && (
            <video src={note.primaryAssetUrl || note.videoUrls![0]} muted preload="metadata" className="w-10 h-10 object-cover rounded mt-0.5 bg-black border" style={{ pointerEvents: 'none' }} />
          )}
          {note.type === 'file' && note.primaryAssetUrl && (
            <div className="flex items-center text-muted-foreground mt-0.5">
              <i className={`mr-1 text-xs ${note.primaryAssetUrl.endsWith('.pdf') ? 'ri-file-pdf-fill' : 'ri-file-3-fill'}`}></i>
              <span className="text-[0.7rem] italic truncate">
                {(note.primaryAssetUrl.split('/').pop() || note.title || "File").substring(0,20)}
                {(note.primaryAssetUrl.split('/').pop() || note.title || "File").length > 20 && "..."}
              </span>
            </div>
          )}
            </div>

            {/* Action Buttons: Duplicate and Delete - These should not trigger collapse */}
            <div className="ml-auto flex items-center self-start pl-1 space-x-0.5">
              <button
                onClick={(e) => { e.stopPropagation(); onNoteDuplicate(note.id); }}
                className="p-0.5 opacity-0 group-hover:opacity-50 hover:opacity-100 text-blue-500 hover:text-blue-600"
            title="Duplicate note"
            aria-label="Duplicate note"
          >
            <i className="ri-file-copy-line text-xs"></i>
          </button>
          <button
            onClick={(e) => { e.stopPropagation(); onNoteDelete(note.id); }}
            className="p-0.5 opacity-0 group-hover:opacity-50 hover:opacity-100 text-red-500 hover:text-red-600"
            title="Delete note"
            aria-label="Delete note"
          >
            <i className="ri-delete-bin-line text-xs"></i>
          </button>
            </div>
            </div>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="w-full mt-1 outline-none"> {/* Added outline-none for consistency */}
          {isExpanded && (
            <div onClick={(e) => e.stopPropagation()} className="outline-none">
              {/* Stop propagation to prevent clicks in content from closing the collapsible */}
              {note.type === 'text' ? (
                <WysiwygNoteEditor
                  note={note}
                  onUpdate={onNoteUpdate}
                  onFileUpload={onFileUpload}
                  outlineItems={allOutlineItems}
                  onDelete={() => onNoteDelete(note.id)}
                  hideHeader={true}
                  editorAreaMinHeight="150px"
                />
              ) : ['image', 'video', 'file'].includes(note.type) ? (
                <InlineMediaViewer
                  note={note}
                  onNoteUpdate={onNoteUpdate}
                  onFileUpload={onFileUpload}
                />
              ) : (
                <div className="p-2 border-t text-sm text-muted-foreground pl-[calc(0.375rem+0.5rem+0.125rem)]">
                  Expanded content for this note type (ID: {note.id}) is not yet supported.
                </div>
              )}
            </div>
          )}
        </CollapsibleContent>
        </div>
      </Collapsible>

      {/* Drop zone after note */}
      <div
        className={`h-1 -mt-0.5 ${
          isDropTarget && noteDragOverTarget?.position === 'after'
            ? 'bg-blue-400 rounded-sm opacity-75'
            : 'hover:bg-blue-200 opacity-0 hover:opacity-50'
        } transition-all duration-150 relative z-10`}
        onDragOver={(e) => {
          console.log('🟡 Drop zone AFTER dragOver event, draggedNote:', !!draggedNote);
          // Only handle note drags, not outline drags
          if (draggedNote) {
            handleDragOver(e, 'after');
          } else {
            console.log('🔴 No draggedNote, ignoring drag over');
          }
        }}
        onDrop={(e) => {
          // Only handle note drags, not outline drags
          if (draggedNote) {
            handleDrop(e, 'after');
          }
        }}
        onDragLeave={handleDragLeave}
      >
        {isDropTarget && noteDragOverTarget?.position === 'after' && (
          <div className="text-xs text-blue-600 font-medium px-1 py-0.5 bg-blue-100 rounded border border-blue-400 border-dashed absolute -bottom-1 left-0 right-0 text-center">
            ↓ DROP AFTER
          </div>
        )}
      </div>
    </div>
  );
}

export default DraggableNotePreview;

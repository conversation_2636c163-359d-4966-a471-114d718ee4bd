import React, { useRef, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface EditorProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  value: string;
  onChange: (value: string) => void;
  minHeight?: string;
  onBlur?: () => void;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
}

export function Editor({
  value,
  onChange,
  minHeight = '150px',
  onBlur,
  placeholder,
  className,
  autoFocus,
  ...props
}: EditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
    adjustHeight();
  };

  const adjustHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.max(
        textareaRef.current.scrollHeight,
        parseInt(minHeight)
      )}px`;
    }
  };

  useEffect(() => {
    adjustHeight();
  }, [value]);

  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  return (
    <textarea
      ref={textareaRef}
      value={value}
      onChange={handleChange}
      onBlur={onBlur}
      placeholder={placeholder}
      className={cn(
        'w-full resize-none overflow-hidden border-none bg-transparent p-0 focus:ring-0 focus:outline-none',
        className
      )}
      style={{ minHeight }}
      {...props}
    />
  );
}

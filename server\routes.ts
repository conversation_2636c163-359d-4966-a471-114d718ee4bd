import "dotenv/config";
import express, { type Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import fileUpload from "express-fileupload";
import { z } from "zod";
import { nanoid } from "nanoid";
import { storage } from "./storage";
import { 
  documentContentSchema, 
  insertDocumentSchema, 
  DocumentVersion, 
  userPreferencesSchema, 
  type UserPreferences,
  type Reference,
  type ReferenceCollection,
  referenceSchema,
  referenceCollectionSchema
} from "@shared/schema";
import { setupAuth } from "./auth";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import fs from "fs";
import path from "path";
import { WebSocketServer, WebSocket } from "ws";


// Authentication middleware
function requireAuth(req: Request, res: Response, next: NextFunction) {
  console.log("Auth check:", {
    path: req.path,
    isAuthenticated: req.isAuthenticated(),
    hasUser: !!req.user,
    sessionID: req.sessionID,
    cookies: req.headers.cookie ? req.headers.cookie.substring(0, 100) + "..." : "missing",
    userAgent: req.headers['user-agent']?.substring(0, 50)
  });

  if (!req.isAuthenticated() || !req.user) {
    console.log("Authentication failed for:", req.path);
    return res.status(401).json({ message: "Authentication required" });
  }
  next();
}

// Admin authentication middleware
function requireAdmin(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated() || !req.user) {
    return res.status(401).json({ message: "Authentication required" });
  }

  // Only user with ID 1 is admin
  if (req.user.id !== 1) {
    return res.status(403).json({ message: "Admin access required" });
  }

  next();
}

// Type guard for req.user
function hasUser(req: Request): req is Request & { user: Express.User } {
  return req.isAuthenticated() && req.user !== undefined;
}

// For secure password hashing
const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export async function registerRoutes(app: Express): Promise<Server> {
  // For file uploads
  app.use(fileUpload({
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max file size
    abortOnLimit: true,
    createParentPath: true,
  }));

  // Middleware to parse JSON bodies
  app.use(express.json());
  
  // Setup authentication
  await setupAuth(app);

  // Initialize default system settings
  const initializeSystemSettings = async () => {
    try {
      const defaultSettings = [
        { key: "registration_enabled", value: "true", description: "Allow new user registration" },
        { key: "login_enabled", value: "true", description: "Allow user login" },
      ];

      for (const setting of defaultSettings) {
        const existing = await storage.getSystemSetting(setting.key);
        if (!existing) {
          await storage.setSystemSetting(setting.key, setting.value, setting.description);
        }
      }
    } catch (error) {
      console.error("Error initializing system settings:", error);
    }
  };

  await initializeSystemSettings();

  // // Ensure default user exists
  // const ensureDefaultUser = async () => {
  //   try {
  //     const defaultUserId = 1;
  //     const user = await storage.getUser(defaultUserId);
      
  //     if (!user) {
  //       console.log("Creating default user with secure password...");
  //       await storage.createUser({
  //         username: "default",
  //         password: await hashPassword("password123")
  //       });
  //     }
  //   } catch (error) {
  //     console.error("Error ensuring default user:", error);
  //   }
  // };
  
  // // Call immediately to ensure user exists
  // ensureDefaultUser();

  // Public endpoint to check system status (no auth required)
  app.get("/api/system/status", async (req: Request, res: Response) => {
    try {
      const registrationSetting = await storage.getSystemSetting("registration_enabled");
      const loginSetting = await storage.getSystemSetting("login_enabled");

      return res.json({
        registrationEnabled: registrationSetting?.value === "true",
        loginEnabled: loginSetting?.value === "true",
      });
    } catch (error) {
      console.error("Error fetching system status:", error);
      return res.status(500).json({ message: "Failed to fetch system status" });
    }
  });

  // Create a new document
  app.post("/api/documents", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const title = req.body.title || "Untitled Document";
      
      const document = await storage.createDocument(userId, title);
      return res.status(201).json(document);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to create document" });
    }
  });

  // Create a shareable link (token-based) for a document
  app.post("/api/documents/:documentId/share-links", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const { documentId } = req.params;
      const currentUserId = req.user.id;

      // Log the request body to check if it's parsed correctly
      console.log(`[POST /api/documents/${documentId}/share-links] Request body:`, req.body);

      const permissionLevel = req.body?.permissionLevel as 'view' | 'edit' | undefined;

      // Validate permissionLevel
      if (permissionLevel !== 'view' && permissionLevel !== 'edit') {
        return res.status(400).json({ message: "Invalid permissionLevel. Must be 'view' or 'edit'." });
      }

      // Verify ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      if (document.userId !== currentUserId) {
        return res.status(403).json({ message: "Access denied: Only the document owner can create share links." });
      }

      // Create the share token (e.g., expires in 7 days, or make configurable)
      // For now, let's make it not expire by default, or a long expiry like 30 days.
      // The plan step mentioned "expiresAt (optional, but good for future)". Let's default to no expiry for now.
      const token = await storage.createShareToken(documentId, permissionLevel, currentUserId /*, expiresInHours (optional) */);

      // Construct the shareable link
      // Ensure req.protocol and req.get('host') are available and correct in your environment
      // For simplicity, using a relative path which client can resolve.
      // Or, have a known BASE_URL from environment config.
      const baseUrl = `${req.protocol}://${req.get('host')}`; // Assumes server is directly internet-facing or behind a correctly configured proxy
      const shareLink = `${baseUrl}/document/${documentId}?token=${token}`;

      return res.status(201).json({ shareLink, token }); // Return both link and raw token for flexibility

    } catch (error) {
      console.error(`Error creating share link for document ${req.params.documentId}:`, error);
      return res.status(500).json({ message: "Failed to create share link" });
    }
  });

  // Request access to a document
  app.post("/api/documents/:documentId/request-access", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const { documentId } = req.params;
      const requestingUserId = req.user.id;

      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      const ownerId = document.userId;

      // Check if user already has access
      if (ownerId === requestingUserId) {
        return res.status(400).json({ message: "You are the owner of this document." });
      }
      const existingShare = await storage.getUserSharePermission(documentId, requestingUserId);
      if (existingShare) {
        return res.status(400).json({ message: "You already have access to this document." });
      }

      // Check if a request already exists
      const existingRequest = await storage.getExistingAccessRequest(documentId, requestingUserId);
      if (existingRequest) {
        return res.status(400).json({ message: "You have already requested access to this document." });
      }

      const accessRequest = await storage.createAccessRequest(documentId, requestingUserId, ownerId);
      return res.status(201).json(accessRequest);
    } catch (error) {
      console.error(`Error requesting access for document ${req.params.documentId}:`, error);
      return res.status(500).json({ message: "Failed to request access" });
    }
  });

  // Get chat messages for a document
  app.get("/api/documents/:documentId/chat", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const { documentId } = req.params;
      const userId = req.user.id;

      // Verify user has access to the document
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      let isAuthorized = false;
      if (document.userId === userId) {
        isAuthorized = true;
      } else {
        // Check if it's shared with the user
        const sharedDocs = await storage.getSharedDocumentsForUser(userId);
        if (sharedDocs.some(share => share.documentId === documentId)) {
          isAuthorized = true;
        }
      }

      if (!isAuthorized) {
        return res.status(403).json({ message: "Access denied to document chat" });
      }

      const messages = await storage.getChatMessages(documentId);
      return res.json(messages);
    } catch (error) {
      console.error(`Error fetching chat messages for document ${req.params.documentId}:`, error);
      return res.status(500).json({ message: "Failed to fetch chat messages" });
    }
  });

  // Get inline comments for a document
  app.get("/api/documents/:documentId/comments", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const { documentId } = req.params;
      const userId = req.user.id;

      // Verify user has access to the document
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      let isAuthorized = false;
      if (document.userId === userId) {
        isAuthorized = true;
      } else {
        // Check if it's shared with the user
        const sharedDocs = await storage.getSharedDocumentsForUser(userId);
        if (sharedDocs.some(share => share.documentId === documentId)) {
          isAuthorized = true;
        }
      }

      if (!isAuthorized) {
        return res.status(403).json({ message: "Access denied to document comments" });
      }

      const comments = await storage.getInlineComments(documentId);
      return res.json(comments);
    } catch (error) {
      console.error(`Error fetching comments for document ${req.params.documentId}:`, error);
      return res.status(500).json({ message: "Failed to fetch comments" });
    }
  });

  // Get all documents
  app.get("/api/documents", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const documents = await storage.getDocumentsByUserId(userId);
      return res.json(documents);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to fetch documents" });
    }
  });

  // Get a specific document
  app.get("/api/documents/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const documentId = req.params.id;
      const tokenFromQuery = req.query.token as string | undefined;
      const currentUserId = req.user.id;

      let document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      // Attempt to grant access via token if provided
      if (tokenFromQuery) {
        const tokenDetails = await storage.validateShareToken(tokenFromQuery);
        if (tokenDetails && tokenDetails.documentId === documentId) {
          // Ensure the user redeeming the token isn't the document owner (tokens are for sharing)
          if (document.userId !== currentUserId) {
            await storage.addShare(documentId, currentUserId, tokenDetails.permissionLevel);
            await storage.deleteShareToken(tokenFromQuery); // Make token single-use for this grant
            console.log(`Access granted to user ${currentUserId} for document ${documentId} via token ${tokenFromQuery} with permission ${tokenDetails.permissionLevel}`);
            // Re-fetch document or permission to ensure it reflects the new share
          } else {
            // Owner accessing their own document with a token - generally okay, token might not need to be consumed.
            // For simplicity, we'll still consume it if it was provided.
             await storage.deleteShareToken(tokenFromQuery);
             console.log(`Owner ${currentUserId} accessed document ${documentId} with their own share token ${tokenFromQuery}. Token consumed.`);
          }
           const permission = await storage.getUserSharePermission(documentId, currentUserId);
      if (permission) {
        const documentWithPermission = { ...document, currentUserPermissionLevel: permission };
        return res.json(documentWithPermission);
      }
        } else {
          // Invalid token or token for a different document
          // Do not immediately deny access; proceed to check direct ownership or existing shares.
          // If the user intended to use a token and it failed, they might still have direct access.
          // If they have no other access, the checks below will deny them.
          console.warn(`Invalid or mismatched share token provided for document ${documentId}: ${tokenFromQuery}`);
        }
      }

      // Check if the user owns the document
      if (document.userId === currentUserId) {
        const ownerDocument = { ...document, currentUserPermissionLevel: 'edit' as 'edit' }; // Owner has full edit
        return res.json(ownerDocument);
      }

      // Check if the user has been granted share access (now potentially including access via token)
      const permission = await storage.getUserSharePermission(documentId, currentUserId);
      if (permission) {
        const documentWithPermission = { ...document, currentUserPermissionLevel: permission };
        return res.json(documentWithPermission);
      }

      // If no ownership, no valid token processed for access, and no direct share, deny access.
      return res.status(403).json({ message: "Access denied" });

    } catch (error) {
      console.error(`Error fetching document ${req.params.id}:`, error);
      return res.status(500).json({ message: "Failed to fetch document" });
    }
  });

  // Update document title
  app.patch("/api/documents/:id/title", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const documentId = req.params.id;

      // Check document ownership or 'edit' share permission
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      let canEdit = false;
      if (document.userId === req.user.id) {
        canEdit = true;
      } else {
        const permission = await storage.getUserSharePermission(documentId, req.user.id);
        if (permission === 'edit') {
          canEdit = true;
        }
      }

      if (!canEdit) {
        return res.status(403).json({ message: "Access denied: Insufficient permissions to edit document title." });
      }

      const { title } = req.body;
      if (!title || typeof title !== "string") {
        return res.status(400).json({ message: "Title is required" });
      }

      const updatedDocument = await storage.updateDocumentTitle(documentId, title);
      return res.json(updatedDocument);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to update document title" });
    }
  });

  // Get document content
  app.get("/api/documents/:id/content", requireAuth, async (req: Request, res: Response) => {
    try {
      const documentId = req.params.id;

      // Check document ownership or share permission
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      let canAccess = false;
      if (document.userId === req.user.id) {
        canAccess = true;
      } else {
        const permission = await storage.getUserSharePermission(documentId, req.user.id);
        if (permission) { // Can be 'view' or 'edit'
          canAccess = true;
        }
      }

      if (!canAccess) {
        return res.status(403).json({ message: "Access denied to document content" });
      }

      const documentContent = await storage.getDocumentContent(documentId);
      if (!documentContent) {
        return res.status(404).json({ message: "Document content not found" });
      }

      return res.json(documentContent.content);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to fetch document content" });
    }
  });

  // Update document content
  app.patch("/api/documents/:id/content", requireAuth, async (req: Request, res: Response) => {
    try {
      const documentId = req.params.id;

      // Check document ownership or 'edit' share permission
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      let canEdit = false;
      if (document.userId === req.user.id) {
        canEdit = true;
      } else {
        const permission = await storage.getUserSharePermission(documentId, req.user.id);
        if (permission === 'edit') {
          canEdit = true;
        }
      }

      if (!canEdit) {
        return res.status(403).json({ message: "Access denied: Insufficient permissions to edit document content." });
      }

      // Log the incoming request body for debugging
      console.log("Update document content request:", JSON.stringify(req.body));

    // Define a more specific schema for a Note, including the new linkedOutlineId
    const noteSchema = z.object({
      id: z.string(),
      title: z.string(),
      content: z.string(),
      createdAt: z.string(), // Consider z.date() if you transform, otherwise string for ISO
      updatedAt: z.string(), // Consider z.date() if you transform, otherwise string for ISO
      linkedOutlineId: z.string(), // Changed from linkedOutlineIds?: string[]
      linkedOutlineNumber: z.string().optional(),
      imageUrls: z.array(z.string()).optional(),
      videoUrls: z.array(z.string()).optional(),
      fileUrls: z.array(z.string()).optional(),
      type: z.enum(['text', 'image', 'video', 'file']).optional(),
      primaryAssetUrl: z.string().optional(),
      imageUrlsData: z.array(z.any()).optional(), // Define AttachmentMetadata schema if needed
      videoUrlsData: z.array(z.any()).optional(), // Define AttachmentMetadata schema if needed
      fileUrlsData: z.array(z.any()).optional(),  // Define AttachmentMetadata schema if needed
      primaryAssetData: z.any().optional(),     // Define AttachmentMetadata schema if needed
    });

      const contentSchema = z.object({
      outline: z.array(z.any()), // Keep z.any() for outline or define its schema if it's stable
      notes: z.array(noteSchema), // Use the more specific noteSchema
      writing: z.record(z.string(), z.any()) // Keep z.any() or define structure
      });

      const content = contentSchema.parse(req.body);
      const updatedDocumentContent = await storage.updateDocumentContent(documentId, content);
      
      if (!updatedDocumentContent) {
        return res.status(404).json({ message: "Document content not found" });
      }
      
      // If user is premium, also create a document version for history tracking
      if (req.user.isPremium) {
        try {
          await storage.createDocumentVersion(
            documentId,
            req.user.id,
            content,
            "Auto-saved version"
          );
          console.log(`Created auto-save version for document ${documentId}`);
        } catch (versionError) {
          // Don't fail the entire request if versioning fails
          console.error("Error creating document version:", versionError);
        }
      }

      // Return the complete content object
      return res.json(updatedDocumentContent.content);
    } catch (error) {
      console.error("Error updating document content:", error);
      return res.status(500).json({ message: "Failed to update document content" });
    }
  });

  // Delete a document
  app.delete("/api/documents/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const documentId = req.params.id;

      // Check document ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      if (document.userId !== req.user.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      await storage.deleteDocument(documentId);
      return res.status(204).send();
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to delete document" });
    }
  });

  // Handle file uploads for notes (images, videos, documents, etc.)
  app.post("/api/upload", requireAuth, async (req: Request, res: Response) => {
    try {
      const files = req.files as { [fieldname: string]: any } | undefined;
      if (!files || Object.keys(files).length === 0) {
        return res.status(400).json({ message: "No files were uploaded" });
      }

      // Get the uploaded file (could be image, video, or any other file)
      const fileField = Object.keys(files)[0]; // Get the first field name (file, attachment, image, etc.)
      const file = files[fileField];
      const fileId = nanoid();
      const fileExt = file.name.split('.').pop().toLowerCase();
      const safeName = `${fileId}.${fileExt}`;
      const uploadPath = `public/uploads/${safeName}`;
      
      // Save the file to disk
      await file.mv(uploadPath);
      console.log(`File uploaded to: ${uploadPath}`);
      
      // Determine file type for client-side processing
      let fileType = 'file';
      if (file.mimetype.startsWith('image/')) {
        fileType = 'image';
      } else if (file.mimetype.startsWith('video/')) {
        fileType = 'video';
      } else if (file.mimetype.startsWith('audio/')) {
        fileType = 'audio';
      }
      
      // Return the URL that can be used to access the file
      const fileUrl = `/uploads/${safeName}`;

      return res.status(201).json({ 
        url: fileUrl, 
        id: fileId,
        type: fileType,
        name: file.name,
        displayName: file.name, // Include original filename as displayName
        size: file.size,
        mimetype: file.mimetype
      });
    } catch (error) {
      console.error("Error uploading file:", error);
      return res.status(500).json({ message: "Failed to upload file" });
    }
  });

  // User leaves a document share
  app.delete("/api/documents/:documentId/shares/me", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const { documentId } = req.params;
      const userId = req.user.id;

      // Check if the document exists to provide a better error message, though removeShare doesn't strictly need it.
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      // Attempt to remove the share
      // storage.removeShare will not throw an error if the share doesn't exist,
      // which is fine for a "leave" operation (idempotent).
      await storage.removeShare(documentId, userId);

      return res.status(204).send();
    } catch (error) {
      console.error("Error leaving document share:", error);
      return res.status(500).json({ message: "Failed to leave document share" });
    }
  });

  // --- Document Sharing Routes ---

  // List users a document is shared with
  app.get("/api/documents/:documentId/shares", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const { documentId } = req.params;
      const currentUserId = req.user.id;

      const shares = await storage.getDocumentShares(documentId);
      return res.json(shares);
    } catch (error) {
      console.error(`Error fetching shares for document ${req.params.documentId}:`, error);
      return res.status(500).json({ message: "Failed to fetch document shares" });
    }
  });
  
  // Delete file endpoint
  app.delete("/api/uploads/:fileId", requireAuth, async (req: Request, res: Response) => {
    try {
      const { fileId } = req.params;
      
      if (!fileId) {
        return res.status(400).json({ message: "File ID is required" });
      }
      
      // Get the file extension by finding the file in the uploads directory
      const uploadDir = "public/uploads";
      
      // Make sure directory exists
      if (!fs.existsSync(uploadDir)) {
        return res.status(404).json({ message: "Upload directory not found" });
      }
      
      // Find files that start with the fileId
      const files = fs.readdirSync(uploadDir);
      const matchingFile = files.find(file => file.startsWith(fileId));
      
      if (!matchingFile) {
        return res.status(404).json({ message: "File not found" });
      }
      
      // Delete the file
      const filePath = path.join(uploadDir, matchingFile);
      fs.unlinkSync(filePath);
      
      return res.status(200).json({ 
        success: true, 
        message: "File deleted successfully",
        fileId: fileId
      });
    } catch (error) {
      console.error("Error deleting file:", error);
      return res.status(500).json({ message: "Failed to delete file" });
    }
  });
  
  // Serve static files from the public directory
  app.use('/uploads', express.static('public/uploads'));

  // User search endpoint (for sharing)
  app.get("/api/users/search", requireAuth, async (req: Request, res: Response) => {
    try {
      const { username } = req.query; // Frontend sends search term as 'username'

      if (!username || typeof username !== 'string' || username.trim() === '') {
        return res.status(400).json({ message: "Username query parameter is required." });
      }

      const user = await storage.getUserByUsername(username.trim());

      if (!user) {
        // Return null or an empty object with 200 OK as ShareModal expects this for "not found"
        return res.json(null);
      }

      // Return a simplified user object
      return res.json({ id: user.id, username: user.username });

    } catch (error) {
      console.error("Error searching for user:", error);
      return res.status(500).json({ message: "Failed to search for user" });
    }
  });

  // Get pending access requests for the current user (as owner)
  app.get("/api/access-requests", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const ownerUserId = req.user.id;
      const requests = await storage.getPendingAccessRequests(ownerUserId);
      return res.json(requests);
    } catch (error) {
      console.error("Error fetching access requests:", error);
      return res.status(500).json({ message: "Failed to fetch access requests" });
    }
  });

  // Approve or deny an access request
  app.put("/api/access-requests/:requestId", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const ownerUserId = req.user.id;
      const { requestId } = req.params;
      const { status, permissionLevel } = req.body;

      if (!status || (status !== "approved" && status !== "denied")) {
        return res.status(400).json({ message: "Invalid status. Must be 'approved' or 'denied'." });
      }

      if (status === "approved" && (!permissionLevel || (permissionLevel !== 'view' && permissionLevel !== 'edit'))) {
        return res.status(400).json({ message: "Invalid permissionLevel. Must be 'view' or 'edit' for approved requests." });
      }

      const updatedRequest = await storage.updateAccessRequestStatus(parseInt(requestId), ownerUserId, status, permissionLevel);

      if (!updatedRequest) {
        return res.status(404).json({ message: "Access request not found or you are not authorized to modify it." });
      }

      return res.json(updatedRequest);
    } catch (error) {
      console.error(`Error updating access request ${req.params.requestId}:`, error);
      return res.status(500).json({ message: "Failed to update access request" });
    }
  });

  // Simple spelling/grammar check endpoint
  app.post("/api/check-text", requireAuth, async (req: Request, res: Response) => {
    try {
      const { text, type } = req.body;
      if (!text || typeof text !== "string") {
        return res.status(400).json({ message: "Text is required" });
      }

      if (type !== "spelling" && type !== "grammar") {
        return res.status(400).json({ message: "Invalid check type" });
      }

      // For the prototype, we'll just return a simple check result
      // In a real implementation, we would use a proper NLP service
      const errors = [];

      // Simple spelling check (just for demonstration)
      if (type === "spelling") {
        const words = text.split(/\s+/);
        const commonMisspellings: Record<string, string> = {
          "teh": "the",
          "recieve": "receive",
          "definately": "definitely",
          "seperate": "separate",
          "occured": "occurred"
        };

        for (let i = 0; i < words.length; i++) {
          const word = words[i].replace(/[^a-zA-Z]/g, '').toLowerCase();
          if (word in commonMisspellings) {
            errors.push({
              index: text.indexOf(word),
              length: word.length,
              suggestion: commonMisspellings[word],
              type: "spelling"
            });
          }
        }
      }

      return res.json({ errors });
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to check text" });
    }
  });

  // User preferences API
  app.get("/api/user/preferences", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // If user has no preferences yet, return default preferences
      const preferences = user.preferences || userPreferencesSchema.parse({});
      return res.json(preferences);
    } catch (error) {
      console.error("Error fetching user preferences:", error);
      return res.status(500).json({ message: "Failed to fetch user preferences" });
    }
  });

  app.patch("/api/user/preferences", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      
      // Validate preferences with the schema
      const preferences = userPreferencesSchema.parse(req.body);
      
      // Update user preferences
      const updatedUser = await storage.updateUserPreferences(userId, preferences);
      
      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }
      
      return res.json(updatedUser.preferences);
    } catch (error) {
      console.error("Error updating user preferences:", error);
      return res.status(500).json({ message: "Failed to update user preferences" });
    }
  });
  
  // Reference API endpoints
  app.get("/api/references", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const references = await storage.getUserReferences(userId);
      return res.json(references);
    } catch (error) {
      console.error("Error fetching references:", error);
      return res.status(500).json({ message: "Error fetching references" });
    }
  });
  
  app.post("/api/references", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const referenceData = referenceSchema.parse(req.body);
      
      // Make sure it has an ID
      if (!referenceData.id) {
        referenceData.id = nanoid();
      }
      
      // Set timestamps
      const now = new Date().toISOString();
      referenceData.createdAt = now;
      referenceData.updatedAt = now;
      
      const reference = await storage.createUserReference(userId, referenceData);
      return res.status(201).json(reference);
    } catch (error) {
      console.error("Error creating reference:", error);
      return res.status(400).json({ message: "Invalid reference data" });
    }
  });
  
  app.patch("/api/references/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const referenceId = req.params.id;
      
      // Get the existing reference first
      const existingReference = await storage.getUserReferenceById(userId, referenceId);
      
      if (!existingReference) {
        return res.status(404).json({ message: "Reference not found" });
      }
      
      // Parse and validate the updated fields
      const updatedFields = req.body;
      
      // Merge with existing data and update the timestamp
      const updatedReference = {
        ...existingReference,
        ...updatedFields,
        id: referenceId, // Ensure ID doesn't change
        updatedAt: new Date().toISOString()
      };
      
      const result = await storage.updateUserReference(userId, updatedReference);
      
      if (!result) {
        return res.status(404).json({ message: "Reference not found or not updated" });
      }
      
      return res.json(result);
    } catch (error) {
      console.error("Error updating reference:", error);
      return res.status(400).json({ message: "Invalid reference data" });
    }
  });
  
  app.delete("/api/references/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const referenceId = req.params.id;
      
      await storage.deleteUserReference(userId, referenceId);
      return res.status(204).end();
    } catch (error) {
      console.error("Error deleting reference:", error);
      return res.status(500).json({ message: "Error deleting reference" });
    }
  });
  
  // Reference Collections API endpoints
  app.get("/api/reference-collections", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const collections = await storage.getUserReferenceCollections(userId);
      return res.json(collections);
    } catch (error) {
      console.error("Error fetching reference collections:", error);
      return res.status(500).json({ message: "Error fetching reference collections" });
    }
  });
  
  app.post("/api/reference-collections", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const collectionData = referenceCollectionSchema.parse(req.body);
      
      // Make sure it has an ID
      if (!collectionData.id) {
        collectionData.id = nanoid();
      }
      
      // Set timestamps
      const now = new Date().toISOString();
      collectionData.createdAt = now;
      collectionData.updatedAt = now;
      
      const collection = await storage.createReferenceCollection(userId, collectionData);
      return res.status(201).json(collection);
    } catch (error) {
      console.error("Error creating reference collection:", error);
      return res.status(400).json({ message: "Invalid collection data" });
    }
  });
  
  app.patch("/api/reference-collections/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const collectionId = req.params.id;
      
      // Get the existing collection first
      const existingCollection = await storage.getUserReferenceCollectionById(userId, collectionId);
      
      if (!existingCollection) {
        return res.status(404).json({ message: "Collection not found" });
      }
      
      // Parse and validate the updated fields
      const updatedFields = req.body;
      
      // Merge with existing data and update the timestamp
      const updatedCollection = {
        ...existingCollection,
        ...updatedFields,
        id: collectionId, // Ensure ID doesn't change
        updatedAt: new Date().toISOString()
      };
      
      const result = await storage.updateReferenceCollection(userId, updatedCollection);
      
      if (!result) {
        return res.status(404).json({ message: "Collection not found or not updated" });
      }
      
      return res.json(result);
    } catch (error) {
      console.error("Error updating reference collection:", error);
      return res.status(400).json({ message: "Invalid collection data" });
    }
  });
  
  app.delete("/api/reference-collections/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const userId = req.user.id;
      const collectionId = req.params.id;
      
      await storage.deleteReferenceCollection(userId, collectionId);
      return res.status(204).end();
    } catch (error) {
      console.error("Error deleting reference collection:", error);
      return res.status(500).json({ message: "Error deleting reference collection" });
    }
  });

  // Document versioning APIs (Premium feature)
  
  // Check if user is premium - middleware
  function requirePremium(req: Request, res: Response, next: NextFunction) {
    if (!hasUser(req)) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    if (!req.user.isPremium) {
      return res.status(403).json({ message: "Premium subscription required for this feature" });
    }
    
    next();
  }
  
  // Create a document version manually
  app.post("/api/documents/:id/versions", requireAuth, async (req: Request, res: Response) => {
    try {
      const documentId = req.params.id;
      
      // Check document ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      if (document.userId !== req.user.id) {
        return res.status(403).json({ message: "Access denied" });
      }
      
      const { changeDescription } = req.body;
      
      // Get current document content
      const documentContent = await storage.getDocumentContent(documentId);
      if (!documentContent) {
        return res.status(404).json({ message: "Document content not found" });
      }
      
      // Create a new version
      const version = await storage.createDocumentVersion(
        documentId,
        req.user.id,
        documentContent.content,
        changeDescription || "Manual save"
      );
      
      return res.status(201).json(version);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to create document version" });
    }
  });

  // Add a collaborator to a document
  app.post("/api/documents/:documentId/shares", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const { documentId } = req.params;
      const currentUserId = req.user.id;
      const { targetUserId, permissionLevel } = req.body;

      if (!targetUserId || !permissionLevel) {
        return res.status(400).json({ message: "targetUserId and permissionLevel are required." });
      }

      // Validate permissionLevel
      if (permissionLevel !== 'view' && permissionLevel !== 'edit') {
        return res.status(400).json({ message: "Invalid permissionLevel. Must be 'view' or 'edit'." });
      }

      // Verify ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      if (document.userId !== currentUserId) {
        return res.status(403).json({ message: "Access denied: Only the document owner can add shares." });
      }

      // Prevent sharing with self
      if (targetUserId === currentUserId) {
        return res.status(400).json({ message: "Cannot share document with yourself." });
      }

      // Check if target user exists
      const targetUser = await storage.getUser(targetUserId);
      if (!targetUser) {
        return res.status(404).json({ message: "Target user not found." });
      }

      await storage.addShare(documentId, targetUserId, permissionLevel);
      // Return the newly created/updated share details
      const newShareInfo = {
        userId: targetUserId,
        username: targetUser.username, // Assuming getUser returns username
        permissionLevel: permissionLevel
      };
      return res.status(201).json(newShareInfo);
    } catch (error) {
      console.error(`Error adding share for document ${req.params.documentId}:`, error);
      return res.status(500).json({ message: "Failed to add document share" });
    }
  });

  // Update a document share (e.g., change permission level)
  app.put("/api/documents/:documentId/shares/:targetUserId", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const { documentId, targetUserId: targetUserIdStr } = req.params;
      const currentUserId = req.user.id;
      const { permissionLevel } = req.body;
      const targetUserId = parseInt(targetUserIdStr);

      if (isNaN(targetUserId)) {
        return res.status(400).json({ message: "Invalid targetUserId." });
      }

      if (!permissionLevel) {
        return res.status(400).json({ message: "permissionLevel is required." });
      }
      if (permissionLevel !== 'view' && permissionLevel !== 'edit') {
        return res.status(400).json({ message: "Invalid permissionLevel. Must be 'view' or 'edit'." });
      }

      // Verify ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      if (document.userId !== currentUserId) {
        return res.status(403).json({ message: "Access denied: Only the document owner can update shares." });
      }

      // Check if target user exists (optional, but good for consistency)
      const targetUser = await storage.getUser(targetUserId);
      if (!targetUser) {
        return res.status(404).json({ message: "Target user not found for share update." });
      }

      await storage.updateSharePermission(documentId, targetUserId, permissionLevel);
      // Return the updated share details
      const updatedShareInfo = {
        userId: targetUserId,
        username: targetUser.username,
        permissionLevel: permissionLevel
      };
      return res.json(updatedShareInfo);
    } catch (error) {
      console.error(`Error updating share for document ${req.params.documentId}, user ${req.params.targetUserId}:`, error);
      return res.status(500).json({ message: "Failed to update document share" });
    }
  });

  // Remove a collaborator from a document
  app.delete("/api/documents/:documentId/shares/:targetUserId", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!hasUser(req)) {
        return res.status(401).json({ message: "Authentication required" });
      }
      const { documentId, targetUserId: targetUserIdStr } = req.params;
      const currentUserId = req.user.id;
      const targetUserId = parseInt(targetUserIdStr);

      if (isNaN(targetUserId)) {
        return res.status(400).json({ message: "Invalid targetUserId." });
      }

      // Verify ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      if (document.userId !== currentUserId) {
        return res.status(403).json({ message: "Access denied: Only the document owner can remove shares." });
      }

      // It's okay if the target user or the share itself doesn't exist, the operation is idempotent.
      // No need to explicitly check if targetUser exists before trying to remove share.
      await storage.removeShare(documentId, targetUserId);
      return res.status(204).send(); // No content on successful deletion
    } catch (error) {
      console.error(`Error removing share for document ${req.params.documentId}, user ${req.params.targetUserId}:`, error);
      return res.status(500).json({ message: "Failed to remove document share" });
    }
  });
  
  // Get all versions of a document
  app.get("/api/documents/:id/versions", requireAuth, async (req: Request, res: Response) => {
    try {
      const documentId = req.params.id;
      
      // Check document ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      if (document.userId !== req.user.id) {
        return res.status(403).json({ message: "Access denied" });
      }
      
      // Check if user is premium for this feature
      if (!req.user.isPremium) {
        return res.status(403).json({ 
          message: "Premium subscription required for document history", 
          isPremiumFeature: true 
        });
      }
      
      console.log(`Fetching versions for document ${documentId}`);
      const versions = await storage.getDocumentVersions(documentId);
      console.log(`Found ${versions.length} versions for document ${documentId}`);
      
      // Get usernames for each version's userId
      const userIds = [...new Set(versions.map(v => v.userId))];
      const userPromises = userIds.map(id => storage.getUser(id));
      const users = await Promise.all(userPromises);
      
      // Map userId to username
      const userMap = users.reduce((map, user) => {
        if (user) map[user.id] = user.username;
        return map;
      }, {} as Record<number, string>);
      
      // Create a map to deduplicate versions by versionNumber
      // We'll keep the most recent entry for each version number
      const versionMap = new Map();
      
      // Process each version
      for (const version of versions) {
        // Add username to the version object
        const versionWithUser = {
          ...version,
          username: userMap[version.userId] || 'Unknown User'
        };
        
        // Only keep the most recent entry for each version number
        if (!versionMap.has(version.versionNumber) || 
            new Date(version.createdAt) > new Date(versionMap.get(version.versionNumber).createdAt)) {
          versionMap.set(version.versionNumber, versionWithUser);
        }
      }
      
      // Convert map values to array and sort by version number in descending order
      const processedVersions = Array.from(versionMap.values())
        .sort((a, b) => b.versionNumber - a.versionNumber);
      
      return res.json(processedVersions);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to fetch document versions" });
    }
  });
  
  // Get a specific version
  app.get("/api/documents/:id/versions/:versionId", requireAuth, requirePremium, async (req: Request, res: Response) => {
    try {
      const documentId = req.params.id;
      const versionId = parseInt(req.params.versionId);
      
      if (isNaN(versionId)) {
        return res.status(400).json({ message: "Invalid version ID" });
      }
      
      // Check document ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      if (document.userId !== req.user.id) {
        return res.status(403).json({ message: "Access denied" });
      }
      
      const version = await storage.getDocumentVersion(versionId);
      if (!version || version.documentId !== documentId) {
        return res.status(404).json({ message: "Version not found" });
      }
      
      return res.json(version);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to fetch document version" });
    }
  });
  
  // Restore a document to a specific version
  app.post("/api/documents/:id/restore/:versionId", requireAuth, requirePremium, async (req: Request, res: Response) => {
    try {
      const documentId = req.params.id;
      const versionId = parseInt(req.params.versionId);
      
      if (isNaN(versionId)) {
        return res.status(400).json({ message: "Invalid version ID" });
      }
      
      // Check document ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      if (document.userId !== req.user.id) {
        return res.status(403).json({ message: "Access denied" });
      }
      
      const restoredContent = await storage.restoreDocumentVersion(documentId, versionId, req.user.id);
      if (!restoredContent) {
        return res.status(404).json({ message: "Failed to restore document version" });
      }
      
      return res.json({ 
        message: "Document restored successfully", 
        content: restoredContent.content 
      });
    } catch (error) {
      console.error(error);
      return res.status(500).json({ message: "Failed to restore document version" });
    }
  });

  // Document export settings endpoints
  app.get("/api/documents/:id/export-settings", requireAuth, async (req: Request, res: Response) => {
    if (!hasUser(req)) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    const documentId = req.params.id;
    
    try {
      // Check document ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      if (document.userId !== req.user.id) {
        return res.status(403).json({ message: "Access denied" });
      }
      
      // Get document export settings
      const settings = await storage.getDocumentExportSettings(documentId);
      
      if (!settings) {
        return res.status(200).json({ 
          exists: false,
          message: "No export settings found for this document" 
        });
      }
      
      return res.status(200).json({ 
        exists: true,
        settings 
      });
    } catch (error) {
      console.error("Error fetching document export settings:", error);
      return res.status(500).json({ message: "Failed to get document export settings" });
    }
  });

  app.post("/api/documents/:id/export-settings", requireAuth, async (req: Request, res: Response) => {
    if (!hasUser(req)) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    const documentId = req.params.id;
    const { format, metadata } = req.body;
    
    if (!format || !metadata) {
      return res.status(400).json({ message: "Format and metadata are required" });
    }
    
    try {
      // Check document ownership
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      
      if (document.userId !== req.user.id) {
        return res.status(403).json({ message: "Access denied" });
      }
      
      // Save document export settings
      const settings = await storage.saveDocumentExportSettings(documentId, format, metadata);
      
      return res.status(200).json({ 
        message: "Export settings saved successfully", 
        settings 
      });
    } catch (error) {
      console.error("Error saving document export settings:", error);
      return res.status(500).json({ message: "Failed to save document export settings" });
    }
  });

  // Email subscription endpoint (public)
  app.post("/api/subscribe", async (req: Request, res: Response) => {
    try {
      const { email } = req.body;

      if (!email || !email.includes('@')) {
        return res.status(400).json({ message: "Valid email is required" });
      }

      // Call external API to save email
      const externalApiUrl = process.env.EMAIL_API_URL || 'http://***********:3000/rest/people';
      const apiToken = process.env.EMAIL_API_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJjMDcwM2RmOC1kZjMyLTRlZWQtYThlZi02OWQyNmQ5ODE4YTkiLCJ0eXBlIjoiQVBJX0tFWSIsIndvcmtzcGFjZUlkIjoiYzA3MDNkZjgtZGYzMi00ZWVkLWE4ZWYtNjlkMjZkOTgxOGE5IiwiaWF0IjoxNzUzMzgzNjI2LCJleHAiOjQ5MDY5ODM2MjYsImp0aSI6IjZkYmZlYjA2LTg4MjAtNDZjNy1hMmI0LTQ0NDIzYWY4OGVhYiJ9.-DJgDflY1_mcEuEyeu5PdO80V4evtLDSNl0yq35GWs4';

      const response = await fetch(externalApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiToken}`,
          'Accept': '*/*',
        },
        body: JSON.stringify({
          emails: {
            primaryEmail: email
          }
        })
      });

      if (response.ok) {
        console.log(`Email subscription saved: ${email}`);
        return res.status(200).json({ message: "Email saved successfully" });
      } else {
        console.error(`Failed to save email ${email}:`, response.status, response.statusText);
        return res.status(500).json({ message: "Failed to save email" });
      }
    } catch (error) {
      console.error("Error saving email subscription:", error);
      return res.status(500).json({ message: "Internal server error" });
    }
  });

  // Emergency admin login bypass (only works for user ID 1)
  app.post("/api/admin/emergency-login", async (req: Request, res: Response) => {
    try {
      const { username, password, emergency_key } = req.body;

      // Check emergency key (must be set in environment)
      if (!process.env.EMERGENCY_ADMIN_KEY || emergency_key !== process.env.EMERGENCY_ADMIN_KEY) {
        return res.status(401).json({ message: "Invalid emergency key" });
      }

      // Only allow for admin user (ID 1)
      const user = await storage.getUserByUsername(username);
      if (!user || user.id !== 1) {
        return res.status(401).json({ message: "Emergency login only available for admin user" });
      }

      // Verify password
      const scryptAsync = promisify(scrypt);
      const [hashed, salt] = user.password.split(".");
      const hashedBuf = Buffer.from(hashed, "hex");
      const suppliedBuf = (await scryptAsync(password, salt, 64)) as Buffer;
      const passwordValid = timingSafeEqual(hashedBuf, suppliedBuf);

      if (!passwordValid) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Log the user in (bypass login disabled check)
      req.login(user, (err) => {
        if (err) {
          console.error("Emergency login error:", err);
          return res.status(500).json({ message: "Login failed" });
        }

        console.log(`Emergency admin login used for user: ${username}`);
        res.status(200).json(user);
      });
    } catch (error) {
      console.error("Emergency login error:", error);
      return res.status(500).json({ message: "Emergency login failed" });
    }
  });

  // Admin API routes
  // Get admin analytics (admin only)
  app.get("/api/admin/analytics", requireAdmin, async (req: Request, res: Response) => {
    try {
      const analytics = await storage.getAdminAnalytics();
      return res.json(analytics);
    } catch (error) {
      console.error("Error fetching admin analytics:", error);
      return res.status(500).json({ message: "Failed to fetch analytics" });
    }
  });

  // Get all users (admin only)
  app.get("/api/admin/users", requireAdmin, async (req: Request, res: Response) => {
    try {
      const users = await storage.getAllUsers();
      // Remove password from response
      const safeUsers = users.map(user => {
        const { password, ...safeUser } = user;
        return safeUser;
      });
      return res.json(safeUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      return res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  // Create new user (admin only)
  app.post("/api/admin/users", requireAdmin, async (req: Request, res: Response) => {
    try {
      const { username, password, isPremium = false } = req.body;

      if (!username || !password) {
        return res.status(400).json({ message: "Username and password are required" });
      }

      // Check if username already exists
      const existingUser = await storage.getUserByUsername(username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      // Hash password
      const scryptAsync = promisify(scrypt);
      const salt = randomBytes(16).toString("hex");
      const buf = (await scryptAsync(password, salt, 64)) as Buffer;
      const hashedPassword = `${buf.toString("hex")}.${salt}`;

      const user = await storage.createUser({
        username,
        password: hashedPassword,
        isPremium,
        isActive: true
      });

      // Remove password from response
      const { password: _, ...safeUser } = user;
      return res.status(201).json(safeUser);
    } catch (error) {
      console.error("Error creating user:", error);
      return res.status(500).json({ message: "Failed to create user" });
    }
  });

  // Update user status (admin only)
  app.patch("/api/admin/users/:id/status", requireAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      const { isActive } = req.body;

      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      if (typeof isActive !== "boolean") {
        return res.status(400).json({ message: "isActive must be a boolean" });
      }

      const user = await storage.updateUserStatus(userId, isActive);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Remove password from response
      const { password, ...safeUser } = user;
      return res.json(safeUser);
    } catch (error) {
      console.error("Error updating user status:", error);
      return res.status(500).json({ message: "Failed to update user status" });
    }
  });

  // Update user premium status (admin only)
  app.patch("/api/admin/users/:id/premium", requireAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      const { isPremium } = req.body;

      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      if (typeof isPremium !== "boolean") {
        return res.status(400).json({ message: "isPremium must be a boolean" });
      }

      const user = await storage.updateUserPremiumStatus(userId, isPremium);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Remove password from response
      const { password, ...safeUser } = user;
      return res.json(safeUser);
    } catch (error) {
      console.error("Error updating user premium status:", error);
      return res.status(500).json({ message: "Failed to update user premium status" });
    }
  });

  // Get all system settings (admin only)
  app.get("/api/admin/settings", requireAdmin, async (req: Request, res: Response) => {
    try {
      const settings = await storage.getAllSystemSettings();
      return res.json(settings);
    } catch (error) {
      console.error("Error fetching system settings:", error);
      return res.status(500).json({ message: "Failed to fetch system settings" });
    }
  });

  // Update system setting (admin only)
  app.put("/api/admin/settings/:key", requireAdmin, async (req: Request, res: Response) => {
    try {
      const { key } = req.params;
      const { value, description } = req.body;

      if (!value) {
        return res.status(400).json({ message: "Value is required" });
      }

      const setting = await storage.setSystemSetting(key, value, description);
      return res.json(setting);
    } catch (error) {
      console.error("Error updating system setting:", error);
      return res.status(500).json({ message: "Failed to update system setting" });
    }
  });

  const httpServer = createServer(app);
  
  // The old WebSocket server code that was here (lines ~800-1050) has been removed.
  // The new WebSocket server is managed in server/index.ts and attached to this httpServer.
  
  return httpServer;
}

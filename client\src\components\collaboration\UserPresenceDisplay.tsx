import React from 'react';
import { UserPresenceInfo } from '@/lib/types'; // Assuming this is the correct path
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface UserPresenceDisplayProps {
  presentUsers: UserPresenceInfo[];
  currentUserId?: number | null; // Allow null for currentUserId
}

const UserPresenceDisplay: React.FC<UserPresenceDisplayProps> = ({ presentUsers, currentUserId }) => {
  const getInitials = (username: string): string => {
    if (!username) return '?';
    const parts = username.split(' ');
    if (parts.length > 1) {
      return parts[0][0].toUpperCase() + parts[parts.length - 1][0].toUpperCase();
    }
    return username[0].toUpperCase();
  };

  // Filter out the current user if currentUserId is provided
  const usersToDisplay = currentUserId
    ? presentUsers.filter(user => user.id !== currentUserId)
    : presentUsers;

  if (!usersToDisplay || usersToDisplay.length === 0) {
    return null; // Or some placeholder like <span className="text-sm text-muted-foreground">No other users present.</span>
  }

  return (
    <TooltipProvider delayDuration={300}>
      <div className="flex items-center space-x-2">
        <span className="text-sm text-muted-foreground mr-1">Present:</span>
        {usersToDisplay.map((user) => (
          <Tooltip key={user.id}>
            <TooltipTrigger asChild>
              <Avatar className="h-8 w-8 border-2 border-background group-hover:border-primary transition-colors">
                <AvatarImage src={user.avatarUrl || undefined} alt={user.username} />
                <AvatarFallback>{getInitials(user.username)}</AvatarFallback>
              </Avatar>
            </TooltipTrigger>
            <TooltipContent>
              <p>{user.username}</p>
            </TooltipContent>
          </Tooltip>
        ))}
      </div>
    </TooltipProvider>
  );
};

export { UserPresenceDisplay };

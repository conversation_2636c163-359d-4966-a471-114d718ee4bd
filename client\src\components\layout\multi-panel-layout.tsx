import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { PanelGroup, Panel, PanelResizeHandle } from "react-resizable-panels";
import debounce from 'lodash.debounce';
import { Button } from '@/components/ui/button';
import { OutlinePanel } from '@/components/outline';
import { WritingPanel } from '@/components/writing';
import { Note, OutlineItem, Citation } from '@/lib/types';
import { usePreferences } from '@/hooks/use-preferences';

export type PanelType = 'outline' | 'writing';

interface MultiPanelLayoutProps {
  outline: OutlineItem[];
  notes: Note[];
  activeOutlineItemId: string | null;
  content: string;
  citations: Citation[];
  notesType?: 'footnotes' | 'endnotes';
  onOutlineChange: (outline: OutlineItem[]) => void;
  onAddOutlineItem: (parentId?: string) => void;
  onImportOutline: () => void;
  onNoteCreate: (linkedOutlineId: string, options?: { type?: 'text' | 'image' | 'video' | 'file' }) => Note; // Updated signature
  onNoteUpdate: (note: Note, forceSaveNow?: boolean) => void; // Added forceSaveNow
  onNoteDelete: (noteId: string) => void;
  onLinkNoteToOutlineItem: (noteId: string, outlineItemId: string) => void; // Added
  onDeleteOutlineItemWithNotes: (outlineItemId: string) => void; // Added
  onFileUpload: (file: File) => Promise<string>;
  onOutlineItemSelect: (itemId: string) => void;
  onContentChange: (content: string) => void;
  onAddCitation: () => void;
  onUpdateCitation: (citation: Citation) => void;
  onDeleteCitation: (citationId: string) => void;
  onSpellCheck?: () => void;
  onGrammarCheck?: () => void;
  onNotesTypeChange?: (type: 'footnotes' | 'endnotes') => void;
  onNoteDuplicate?: (noteId: string) => void; // Added for current subtask
  visiblePanels: PanelType[];
  popOutPanel: (panel: PanelType) => void;
}

export function MultiPanelLayout({
  outline,
  notes,
  activeOutlineItemId,
  content,
  citations,
  notesType,
  onOutlineChange,
  onAddOutlineItem,
  onImportOutline,
  onNoteCreate,
  onNoteUpdate,
  onNoteDelete,
  // onImportNote, // Removed
  onFileUpload,
  onOutlineItemSelect,
  onContentChange,
  onAddCitation,
  onUpdateCitation,
  onDeleteCitation,
  onSpellCheck,
  onGrammarCheck,
  onNotesTypeChange,
  onLinkNoteToOutlineItem,
  onDeleteOutlineItemWithNotes,
  onNoteDuplicate, // Added for current subtask
  visiblePanels,
  popOutPanel,
}: MultiPanelLayoutProps) {

  // Clean up any global state when the component mounts to prevent
  // panel divider drag direction issues after popout/popin operations
  useEffect(() => {
    // Clear any lingering cursor styles or drag states
    document.body.style.cursor = '';
    document.documentElement.style.cursor = '';
    document.body.classList.remove('dragging-mode', 'disable-websocket-updates', 'minimap-dragging');
    document.documentElement.classList.remove('outline-dragging-active');
  }, [visiblePanels]); // Re-run when visible panels change (e.g., after popout/popin)


  const renderPanel = (panelType: PanelType) => {
    switch (panelType) {
      case 'outline':
        return (
          <OutlinePanel
            outline={outline}
            onOutlineChange={onOutlineChange}
            onAddItem={onAddOutlineItem}
            onImportOutline={onImportOutline}
            // Pass note-related props
            notes={notes}
            onNoteCreate={onNoteCreate}
            onNoteUpdate={onNoteUpdate}
            onNoteDelete={onNoteDelete}
            onFileUpload={onFileUpload}
            activeOutlineItemId={activeOutlineItemId} // Pass activeOutlineItemId
            // Pass down new props
            onLinkNoteToOutlineItem={onLinkNoteToOutlineItem}
            onDeleteOutlineItemWithNotes={onDeleteOutlineItemWithNotes}
            onNoteDuplicate={onNoteDuplicate} // ADD THIS LINE
            popOutPanel={() => popOutPanel('outline')}
          />
        );
      // 'notes' case removed
      case 'writing':
        return (
          <WritingPanel
            outline={outline}
            activeOutlineItemId={activeOutlineItemId}
            onOutlineItemSelect={onOutlineItemSelect}
            content={content}
            onContentChange={onContentChange}
            notes={notes.filter(note =>
              note.linkedOutlineId === activeOutlineItemId || !note.linkedOutlineId
            )}
            citations={citations}
            notesType={notesType}
            onNotesTypeChange={onNotesTypeChange}
            onAddCitation={onAddCitation}
            onUpdateCitation={onUpdateCitation}
            onDeleteCitation={onDeleteCitation}
            onSpellCheck={onSpellCheck}
            onGrammarCheck={onGrammarCheck}
            onNoteCreate={onNoteCreate}
            onNoteUpdate={onNoteUpdate}
            onNoteDelete={onNoteDelete}
            onFileUpload={onFileUpload}
            popOutPanel={() => popOutPanel('writing')}
          />
        );
      default:
        return null;
    }
  };

  const panelOrder: PanelType[] = ['outline', 'writing']; // 'notes' removed

  const visiblePanelElements = panelOrder
    .filter(panelType => visiblePanels.includes(panelType))
    .map((panelType, index, arr) => (
      <React.Fragment key={panelType}>
        <Panel
          id={panelType}
          minSize={10}
          className="overflow-y-auto overflow-x-hidden"
        >
          <div className="h-full w-full">
            {renderPanel(panelType)}
          </div>
        </Panel>
        {index < arr.length - 1 && (
          <PanelResizeHandle className="w-1 bg-border hover:bg-primary/20 focus:bg-primary/20 transition-colors duration-200 ease-in-out data-[resize-handle-state=drag]:bg-primary data-[resize-handle-state=hover]:bg-primary/50" />
        )}
      </React.Fragment>
    ));

  return (
    <div className="flex flex-col h-full w-full">
      <PanelGroup
        direction="horizontal"
        autoSaveId="multi-panel-layout"
        className="flex-grow"
        key={`panel-group-${visiblePanels.join('-')}`} // Force remount when panels change to prevent state issues
      >
        {visiblePanelElements}
      </PanelGroup>
    </div>
  );
}
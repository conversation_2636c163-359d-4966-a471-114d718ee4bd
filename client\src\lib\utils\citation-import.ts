import { Citation } from '@/lib/types';
import { nanoid } from 'nanoid';

/**
 * Extract DOI from various formats
 */
export function extractDOI(input: string): string | null {
  // Regular expression to match DOI patterns
  const doiRegex = /\b(10\.\d{4,}(?:\.\d+)*\/\S+(?:(?!["&'<>])\S)*)\b/i;
  const match = input.match(doiRegex);
  
  return match ? match[1] : null;
}

/**
 * Extract URL from text
 */
export function extractURL(input: string): string | null {
  // Regular expression to match URL patterns
  const urlRegex = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gi;
  const match = input.match(urlRegex);
  
  return match ? match[0] : null;
}

/**
 * Parse BibTeX format into Citation object array
 */
export function parseBibTeX(bibtex: string): Partial<Citation>[] {
  try {
    // Very basic BibTeX parser for simple entries
    const type = bibtex.match(/@(\w+)\s*{/i)?.[1]?.toLowerCase() || 'other';
    const key = bibtex.match(/@\w+\s*{\s*([^,]+)/i)?.[1] || '';
    
    // Extract fields
    const title = bibtex.match(/title\s*=\s*{([^}]*)}/i)?.[1] || '';
    const author = bibtex.match(/author\s*=\s*{([^}]*)}/i)?.[1] || '';
    const year = bibtex.match(/year\s*=\s*{([^}]*)}/i)?.[1] || bibtex.match(/year\s*=\s*(\d+)/i)?.[1] || '';
    const journal = bibtex.match(/journal\s*=\s*{([^}]*)}/i)?.[1] || '';
    const publisher = bibtex.match(/publisher\s*=\s*{([^}]*)}/i)?.[1] || '';
    const volume = bibtex.match(/volume\s*=\s*{([^}]*)}/i)?.[1] || bibtex.match(/volume\s*=\s*(\d+)/i)?.[1] || '';
    const number = bibtex.match(/number\s*=\s*{([^}]*)}/i)?.[1] || bibtex.match(/number\s*=\s*(\d+)/i)?.[1] || '';
    const pages = bibtex.match(/pages\s*=\s*{([^}]*)}/i)?.[1] || '';
    const doi = bibtex.match(/doi\s*=\s*{([^}]*)}/i)?.[1] || '';
    const url = bibtex.match(/url\s*=\s*{([^}]*)}/i)?.[1] || '';
    
    // Parse authors - split by 'and' and clean up
    const authors = author.split(/\s+and\s+/).map(a => a.trim());
    
    // Map BibTeX entry type to our citation type
    let citationType: 'book' | 'article' | 'website' | 'journal' | 'conference' | 'other' = 'other';
    switch (type.toLowerCase()) {
      case 'book':
      case 'inbook':
        citationType = 'book';
        break;
      case 'article':
        citationType = 'article';
        break;
      case 'inproceedings':
      case 'conference':
        citationType = 'conference';
        break;
      case 'misc':
        citationType = url ? 'website' : 'other';
        break;
      case 'techreport':
      case 'unpublished':
      case 'phdthesis':
      case 'mastersthesis':
        citationType = 'other';
        break;
      default:
        citationType = 'other';
    }
    
    // Create citation object
    const citation: Citation = {
      id: nanoid(),
      marker: '', // This will be set by the calling code
      reference: `${author} (${year}). ${title}.`,
      type: citationType,
      title,
      authors,
      year,
      url,
      doi,
      publisher,
      journal,
      volume,
      issue: number,
      pages
    };
    
    return [citation as Partial<Citation>];
  } catch (error) {
    console.error('Error parsing BibTeX:', error);
    return [];
  }
}

/**
 * Simple citation parser - attempts to guess citation information from plain text
 */
export function parseTextCitation(text: string): Partial<Citation>[] {
  // This is a very basic parser that makes some assumptions about the text format
  const result: Partial<Citation> = {};
  
  // Try to extract DOI and URL
  const doi = extractDOI(text);
  if (doi) result.doi = doi;
  
  const url = extractURL(text);
  if (url) result.url = url;
  
  // Try to extract year (4 digits in parentheses)
  const yearMatch = text.match(/\((\d{4})\)/);
  if (yearMatch) result.year = yearMatch[1];
  
  // If it's likely a journal article
  if (text.includes('vol.') || text.includes('pp.') || text.includes('journal')) {
    result.type = 'journal';
    
    // Try to extract volume
    const volMatch = text.match(/vol\.\s*(\d+)/i);
    if (volMatch) result.volume = volMatch[1];
    
    // Try to extract issue
    const issueMatch = text.match(/no\.\s*(\d+)/i);
    if (issueMatch) result.issue = issueMatch[1];
    
    // Try to extract pages
    const pagesMatch = text.match(/pp\.\s*(\d+(?:-\d+)?)/i);
    if (pagesMatch) result.pages = pagesMatch[1];
  }
  
  // If it's likely a book
  if (text.includes('edition') || text.includes('publisher') || text.includes('press')) {
    result.type = 'book';
  }
  
  // If it's likely a website
  if (url && (text.includes('retrieved from') || text.includes('accessed on'))) {
    result.type = 'website';
  }
  
  return [result as Partial<Citation>];
}

/**
 * Parse RIS format into Citation object array
 */
export function parseRIS(ris: string): Partial<Citation>[] {
  try {
    const lines = ris.split('\n').map(line => line.trim());
    
    // Initialize citation data
    const data: Record<string, string | string[]> = {};
    let currentTag = '';
    
    // Process each line
    for (const line of lines) {
      // Skip empty lines
      if (!line) continue;
      
      // If line starts with a tag (2 characters followed by space and dash)
      if (/^[A-Z]{2}\s+-\s+/.test(line)) {
        const [tag, ...valueParts] = line.split(/\s+-\s+/);
        currentTag = tag.trim();
        const value = valueParts.join(' - ').trim();
        
        // Handle tags that can appear multiple times
        if (currentTag === 'AU' || currentTag === 'A1' || currentTag === 'A2') {
          if (!data['authors']) data['authors'] = [];
          (data['authors'] as string[]).push(value);
        } else {
          data[currentTag] = value;
        }
      } 
      // Continuation of previous tag
      else if (currentTag) {
        if (typeof data[currentTag] === 'string') {
          data[currentTag] = (data[currentTag] as string) + ' ' + line;
        }
      }
    }
    
    // Map RIS type to our citation type
    let type: 'book' | 'article' | 'website' | 'journal' | 'conference' | 'other' = 'other';
    if (data['TY']) {
      switch (data['TY'].toString().toUpperCase()) {
        case 'BOOK':
          type = 'book';
          break;
        case 'JOUR':
          type = 'journal';
          break;
        case 'CONF':
          type = 'conference';
          break;
        case 'ELEC':
        case 'ICOMM':
          type = 'website';
          break;
        default:
          type = 'other';
      }
    }
    
    // Create authors array
    const authors = Array.isArray(data['authors']) 
      ? data['authors'] 
      : data['authors'] 
        ? [data['authors'].toString()] 
        : [];
    
    // Create citation object
    const citation: Citation = {
      id: nanoid(),
      marker: '', // This will be set by the calling code
      reference: `${authors.join(', ')} ${data['PY'] || ''}. ${data['TI'] || ''}`,
      type,
      title: data['TI']?.toString() || '',
      authors,
      year: data['PY']?.toString() || data['Y1']?.toString() || '',
      url: data['UR']?.toString() || '',
      doi: data['DO']?.toString() || '',
      publisher: data['PB']?.toString() || '',
      journal: data['JO']?.toString() || data['JF']?.toString() || '',
      volume: data['VL']?.toString() || '',
      issue: data['IS']?.toString() || '',
      pages: data['SP']?.toString() || ''
    };
    
    return [citation as Partial<Citation>];
  } catch (error) {
    console.error('Error parsing RIS:', error);
    return [];
  }
}
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
// useRef is no longer needed

// ChatButtonPosition type definition is removed as it's managed by the parent.

interface DraggableChatButtonProps {
  onClick: () => void; // For toggling chat panel
  hasUnreadMessages: boolean;
  isChatOpen: boolean;
  // onPositionChange prop is removed
  // No need for internal drag handlers or ref
}

export function DraggableChatButton({
  onClick,
  hasUnreadMessages,
  isChatOpen,
}: DraggableChatButtonProps) {
  // All drag-related logic (draggable attribute, onDragStart, onDragEnd, buttonRef)
  // has been moved to the parent component (DocumentPage.tsx) which will handle mouse events
  // on a wrapper div around this button.

  return (
    <Button
      // draggable, onDragStart, onDragEnd, ref are removed
      onClick={onClick}
      variant="secondary"
      size="icon"
      // cursor-grab and active:cursor-grabbing are now handled by the wrapper div in DocumentPage
      // This button itself no longer needs these cursor styles directly if the wrapper handles mousedown.
      // However, keeping them might be fine if the wrapper doesn't fully cover the button visually or for fallback.
      // For now, let's assume the wrapper's cursor styles are sufficient.
      className="h-14 w-14 rounded-full shadow-lg hover:bg-secondary/80"
      title={isChatOpen ? "Close Chat" : "Open Chat"}
    >
      <MessageCircle className="h-7 w-7" />
      {hasUnreadMessages && !isChatOpen && (
        <span className="absolute top-1 right-1 block h-3 w-3 rounded-full bg-red-500 border-2 border-background" />
      )}
      <span className="sr-only">{isChatOpen ? "Close chat panel" : "Open chat panel"}</span>
    </Button>
  );
}

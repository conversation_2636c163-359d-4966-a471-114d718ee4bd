import { nanoid } from "nanoid";
import diff from 'fast-diff';
import {
  users,
  documents,
  documentContents,
  documentVersions,
  documentExportSettings,
  userReferences,
  referenceCollections,
  systemSettings,
  type User,
  type InsertUser,
  type Document,
  type DocumentContent,
  type DocumentContentData,
  type DocumentVersion,
  type InsertDocumentVersion,
  type ChangedSection,
  type UserPreferences,
  getDefaultUserPreferences,
  type DocumentExportSettings,
  type DocumentExportMetadata,
  type SystemSetting,
  type InsertSystemSetting,
  documentContentSchema,
  OutlineItem,
  type Reference,
  type ReferenceCollection,
  type UserReference,
  type ReferenceCollectionDB,
  documentShares,
  documentPermissionEnum, // Import for permissionLevel
  type DocumentContentData as SharedDocumentContentData,
  type OutlineItem as SharedOutlineItem,
  type Note as SharedNote,
  type Citation as SharedCitation,
  type DocumentChatMessage, // Added for chat
  documentChatMessages, // Added for chat
} from "@shared/schema";
// We need the granular change types defined on the client for the server to understand them.
// Ideally, these would also be in a shared location. For now, assuming they might be duplicated
// or we'd need to create a shared types package.
// For this exercise, I'll assume the structure of client-side DocumentChange types is known
// and I will replicate parts of it or use 'any' if the full types aren't shareable directly.
// Let's define a placeholder for the client's DocumentChange structure for now.
// This should eventually come from a shared types definition.
// import type { DocumentChange, DocumentChangeType } from "../../client/src/lib/types"; // Old import
import { DocumentChangeType, type DocumentChange, shareTokens, type ShareToken, documentAccessRequests } from "../shared/schema"; // New import from shared

import { db } from "./db";
import { eq, and, SQL, gt } from "drizzle-orm"; // Added gt for token expiration check
import crypto from 'crypto'; // For generating secure random tokens

// Interface for storage operations
export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUserPreferences(userId: number, preferences: UserPreferences): Promise<User | undefined>;

  // Admin user operations
  getAllUsers(): Promise<User[]>;
  updateUserStatus(userId: number, isActive: boolean): Promise<User | undefined>;
  updateUserPremiumStatus(userId: number, isPremium: boolean): Promise<User | undefined>;

  // System settings operations
  getSystemSetting(key: string): Promise<SystemSetting | undefined>;
  setSystemSetting(key: string, value: string, description?: string): Promise<SystemSetting>;
  getAllSystemSettings(): Promise<SystemSetting[]>;

  // Analytics operations
  getAdminAnalytics(): Promise<{
    totalUsers: number;
    activeUsers: number;
    premiumUsers: number;
    newUsersToday: number;
    totalDocuments: number;
    documentsCreatedToday: number;
  }>;

  // Document operations
  createDocument(userId: number, title: string): Promise<Document>;
  getDocument(id: string): Promise<Document | undefined>;
  getDocumentsByUserId(userId: number): Promise<{ owned: Document[], shared: any[] }>;
  updateDocumentTitle(id: string, title: string): Promise<Document | undefined>;
  deleteDocument(id: string): Promise<void>;

  // Document content operations
  getDocumentContent(documentId: string): Promise<DocumentContent | undefined>;
  updateDocumentContent(documentId: string, content: DocumentContentData): Promise<DocumentContent | undefined>;
  applyDocumentChanges(documentId: string, changes: ChangedSection[], userId?: number): Promise<DocumentContent | undefined>; // userId is optional for now
  
  // Document version operations
  createDocumentVersion(documentId: string, userId: number, content: DocumentContentData, changeDescription?: string): Promise<DocumentVersion>;
  getDocumentVersions(documentId: string): Promise<DocumentVersion[]>;
  getDocumentVersion(versionId: number): Promise<DocumentVersion | undefined>;
  restoreDocumentVersion(documentId: string, versionId: number, userId: number): Promise<DocumentContent | undefined>;
  
  // Document export settings operations
  getDocumentExportSettings(documentId: string): Promise<DocumentExportSettings | undefined>;
  saveDocumentExportSettings(documentId: string, format: string, metadata: DocumentExportMetadata): Promise<DocumentExportSettings>;
  
  // User reference operations
  getUserReferences(userId: number): Promise<Reference[]>;
  getUserReferenceById(userId: number, referenceId: string): Promise<Reference | undefined>;
  createUserReference(userId: number, reference: Reference): Promise<Reference>;
  updateUserReference(userId: number, reference: Reference): Promise<Reference | undefined>;
  deleteUserReference(userId: number, referenceId: string): Promise<void>;
  
  // Reference collection operations
  getUserReferenceCollections(userId: number): Promise<ReferenceCollection[]>;
  getUserReferenceCollectionById(userId: number, collectionId: string): Promise<ReferenceCollection | undefined>;
  createReferenceCollection(userId: number, collection: ReferenceCollection): Promise<ReferenceCollection>;
  updateReferenceCollection(userId: number, collection: ReferenceCollection): Promise<ReferenceCollection | undefined>;
  deleteReferenceCollection(userId: number, collectionId: string): Promise<void>;

  // Document sharing operations
  getAuthorizedUserIdsForDocument(documentId: string, permissionLevel?: 'view' | 'edit'): Promise<number[]>;
  addShare(documentId: string, targetUserId: number, permissionLevel: 'view' | 'edit'): Promise<void>;
  updateSharePermission(documentId: string, targetUserId: number, newPermissionLevel: 'view' | 'edit'): Promise<void>;
  removeShare(documentId: string, targetUserId: number): Promise<void>;
  getDocumentShares(documentId: string): Promise<Array<{ userId: number; username: string; permissionLevel: 'view' | 'edit' }>>;
  getSharedDocumentsForUser(userId: number): Promise<Array<{
    documentId: string;
    title: string;
    ownerUserId: number;
    ownerUsername: string;
    permissionLevel: 'view' | 'edit';
    sharedAt: Date;
  }>>;
  getUserSharePermission(documentId: string, userId: number): Promise<'view' | 'edit' | null>;
  // getUserByUsername is already defined, will ensure its return type matches needs for user lookup.

  // Document Chat operations
  addChatMessage(documentId: string, userId: number, username: string, messageText: string): Promise<DocumentChatMessage>;
  getChatMessages(documentId: string): Promise<DocumentChatMessage[]>;

  // Share Token operations
  createShareToken(documentId: string, permissionLevel: 'view' | 'edit', creatorUserId: number, expiresInHours?: number): Promise<string>;
  validateShareToken(tokenValue: string): Promise<{ documentId: string; permissionLevel: 'view' | 'edit'; creatorUserId: number | null; } | null>;
  deleteShareToken(tokenValue: string): Promise<void>;

  // Document Access Request operations
  getPendingAccessRequests(ownerUserId: number): Promise<(DocumentAccessRequest & { documentTitle: string; requestingUsername: string; })[]>;
  updateAccessRequestStatus(requestId: number, ownerUserId: number, status: "approved" | "denied", permissionLevel?: "view" | "edit"): Promise<DocumentAccessRequest | undefined>;
}

// Database storage implementation
export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const results = await db.select().from(users).where(eq(users.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const results = await db.select().from(users).where(eq(users.username, username));
    return results.length > 0 ? results[0] : undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    // Set default preferences if not provided
    const userWithDefaults = {
      ...insertUser,
      preferences: insertUser.preferences || getDefaultUserPreferences()
    };

    const results = await db.insert(users).values(userWithDefaults).returning();
    return results[0];
  }

  async updateUserPreferences(userId: number, preferences: UserPreferences): Promise<User | undefined> {
    const results = await db.update(users)
      .set({ preferences })
      .where(eq(users.id, userId))
      .returning();

    return results.length > 0 ? results[0] : undefined;
  }

  // Admin user operations
  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users).orderBy(users.id);
  }

  async updateUserStatus(userId: number, isActive: boolean): Promise<User | undefined> {
    const results = await db.update(users)
      .set({ isActive, updatedAt: new Date() })
      .where(eq(users.id, userId))
      .returning();

    return results.length > 0 ? results[0] : undefined;
  }

  async updateUserPremiumStatus(userId: number, isPremium: boolean): Promise<User | undefined> {
    const results = await db.update(users)
      .set({ isPremium, updatedAt: new Date() })
      .where(eq(users.id, userId))
      .returning();

    return results.length > 0 ? results[0] : undefined;
  }

  // System settings operations
  async getSystemSetting(key: string): Promise<SystemSetting | undefined> {
    const results = await db.select().from(systemSettings).where(eq(systemSettings.key, key));
    return results.length > 0 ? results[0] : undefined;
  }

  async setSystemSetting(key: string, value: string, description?: string): Promise<SystemSetting> {
    const existing = await this.getSystemSetting(key);

    if (existing) {
      const results = await db.update(systemSettings)
        .set({ value, description, updatedAt: new Date() })
        .where(eq(systemSettings.key, key))
        .returning();
      return results[0];
    } else {
      const results = await db.insert(systemSettings)
        .values({ key, value, description })
        .returning();
      return results[0];
    }
  }

  async getAllSystemSettings(): Promise<SystemSetting[]> {
    return await db.select().from(systemSettings).orderBy(systemSettings.key);
  }

  // Analytics operations
  async getAdminAnalytics() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get user statistics
    const allUsers = await db.select().from(users);
    const totalUsers = allUsers.length;
    const activeUsers = allUsers.filter(user => user.isActive).length;
    const premiumUsers = allUsers.filter(user => user.isPremium).length;

    // Get new users today
    const newUsersToday = allUsers.filter(user => {
      if (!user.createdAt) return false;
      const userDate = new Date(user.createdAt);
      return userDate >= today && userDate < tomorrow;
    }).length;

    // Get document statistics
    const allDocuments = await db.select().from(documents).where(eq(documents.isDeleted, false));
    const totalDocuments = allDocuments.length;

    // Get documents created today
    const documentsCreatedToday = allDocuments.filter(doc => {
      const docDate = new Date(doc.createdAt);
      return docDate >= today && docDate < tomorrow;
    }).length;

    return {
      totalUsers,
      activeUsers,
      premiumUsers,
      newUsersToday,
      totalDocuments,
      documentsCreatedToday,
    };
  }

  // Document operations
  async createDocument(userId: number, title: string): Promise<Document> {
    // Create the document
    const [document] = await db.insert(documents)
      .values({ userId, title })
      .returning();

    // Create empty document content
    const initialContent = {
      outline: [{
        id: nanoid(),
        number: "1",
        title: "Introduction",
        children: []
      }],
      notes: [],
      writing: {},
    };

    await db.insert(documentContents)
      .values({ documentId: document.id, content: initialContent });

    return document;
  }

  async getDocument(id: string): Promise<Document | undefined> {
    const results = await db.select().from(documents).where(and(eq(documents.id, id), eq(documents.isDeleted, false)));
    return results.length > 0 ? results[0] : undefined;
  }

  async getDocumentsByUserId(userId: number): Promise<{ owned: Document[], shared: any[] }> {
    const ownedDocuments = await db.select().from(documents).where(and(eq(documents.userId, userId), eq(documents.isDeleted, false)));
    const sharedDocuments = await this.getSharedDocumentsForUser(userId);

    return {
      owned: ownedDocuments,
      shared: sharedDocuments,
    };
  }

  async updateDocumentTitle(id: string, title: string): Promise<Document | undefined> {
    const results = await db.update(documents)
      .set({ title, updatedAt: new Date() })
      .where(eq(documents.id, id))
      .returning();
    
    return results.length > 0 ? results[0] : undefined;
  }

  async deleteDocument(id: string): Promise<void> {
    // Soft delete the document and its related data
    await db.update(documents).set({ isDeleted: true }).where(eq(documents.id, id));
    await db.update(documentContents).set({ isDeleted: true }).where(eq(documentContents.documentId, id));
    await db.update(documentExportSettings).set({ isDeleted: true }).where(eq(documentExportSettings.documentId, id));
  }

  // Document content operations
  async getDocumentContent(documentId: string): Promise<DocumentContent | undefined> {
    const results = await db.select()
      .from(documentContents)
      .where(and(eq(documentContents.documentId, documentId), eq(documentContents.isDeleted, false)));
    
    return results.length > 0 ? results[0] : undefined;
  }

  async updateDocumentContent(documentId: string, content: DocumentContentData): Promise<DocumentContent | undefined> {
    console.log(`Updating document content for document ID: ${documentId}`);
    
    // First check if document content exists
    const existing = await this.getDocumentContent(documentId);
    
    if (!existing) {
      console.log("No existing document content found, creating new entry");
      // Create new document content if it doesn't exist
      const [result] = await db.insert(documentContents)
        .values({ documentId, content })
        .returning();
        
      // Update document's updatedAt timestamp
      await db.update(documents)
        .set({ updatedAt: new Date() })
        .where(eq(documents.id, documentId));
      
      // console.log("Created new document content:", result); // Verbose log removed
      return result;
    } else {
      console.log("Updating existing document content for document ID:", documentId); // Kept this, but removed content object
      // Update existing document content
      const results = await db.update(documentContents)
        .set({ content })
        .where(eq(documentContents.documentId, documentId))
        .returning();
      
      // Update document's updatedAt timestamp
      await db.update(documents)
        .set({ updatedAt: new Date() })
        .where(eq(documents.id, documentId));
      
      // console.log("Updated document content:", results[0]); // Verbose log removed
      return results.length > 0 ? results[0] : undefined;
    }
  }

  async applyDocumentChanges(documentId: string, changes: DocumentChange[], userIdPerformingChange?: number): Promise<DocumentContent | undefined> {
    // console.log(`Applying granular document changes for document ID: ${documentId}`, changes); // This can be very verbose with granular changes
    console.log(`Applying ${changes.length} granular change(s) for document ID: ${documentId}`);

    if (userIdPerformingChange) {
      const permission = await this.getUserSharePermission(documentId, userIdPerformingChange);
      const document = await this.getDocument(documentId);
      if (document?.userId !== userIdPerformingChange && permission !== "edit") {
        throw new Error("User does not have permission to edit this document.");
      }
    }

    const currentDocumentState = await this.getDocumentContent(documentId);
    if (!currentDocumentState) {
      console.error(`Document content not found for ID: ${documentId}. Cannot apply changes.`);
      return undefined;
    }

    // Deep copy the content to modify. Using shared type.
    let modifiableContent: SharedDocumentContentData = JSON.parse(JSON.stringify(currentDocumentState.content));

    // Helper function to find an outline item and its parent list recursively
    // Returns true if item was found and callback was executed
    const findAndModifyOutlineItem = (
      items: SharedOutlineItem[],
      itemId: string,
      callback: (item: SharedOutlineItem, parentList: SharedOutlineItem[], itemIndex: number) => void
    ): boolean => {
      for (let i = 0; i < items.length; i++) {
        if (items[i].id === itemId) {
          callback(items[i], items, i);
          return true;
        }
        if (items[i].children && findAndModifyOutlineItem(items[i].children!, itemId, callback)) {
          return true;
        }
      }
      return false;
    };

    // Helper function to find parent list for an outline item
    const findParentListAndIndex = (
        items: SharedOutlineItem[],
        targetId: string, // ID of the item whose parent list we want
        currentParentId?: string
      ): { parentList: SharedOutlineItem[] | null, parentId: string | undefined, itemIndex: number } => {
      for (let i = 0; i < items.length; i++) {
        if (items[i].id === targetId) {
          return { parentList: items, parentId: currentParentId, itemIndex: i };
        }
        if (items[i].children) {
          const foundInChild = findParentListAndIndex(items[i].children!, targetId, items[i].id);
          if (foundInChild.parentList) return foundInChild;
        }
      }
      return { parentList: null, parentId: undefined, itemIndex: -1 };
    };


    for (const change of changes) {
      console.log("Processing change:", change.elementType, change.elementId);
      switch (change.elementType) {
        // --- Outline Handlers ---
        case DocumentChangeType.OUTLINE_ITEM_ADDED: {
          const { item, parentItemId, index } = change.payload;
          const newItem = { ...item } as SharedOutlineItem; // Ensure type compatibility
          if (parentItemId) {
            findAndModifyOutlineItem(modifiableContent.outline, parentItemId, (parentItem) => {
              if (!parentItem.children) parentItem.children = [];
              parentItem.children.splice(index !== undefined ? index : parentItem.children.length, 0, newItem);
            });
          } else { // Adding to root
            modifiableContent.outline.splice(index !== undefined ? index : modifiableContent.outline.length, 0, newItem);
          }
          break;
        }
        case DocumentChangeType.OUTLINE_ITEM_REMOVED: {
          const { parentItemId } = change.payload || {}; // payload might be empty for simple remove
          if (parentItemId) {
            findAndModifyOutlineItem(modifiableContent.outline, parentItemId, (parentItem) => {
              if (parentItem.children) {
                parentItem.children = parentItem.children.filter(child => child.id !== change.elementId);
              }
            });
          } else { // Removing from root
            modifiableContent.outline = modifiableContent.outline.filter(item => item.id !== change.elementId);
          }
          // Also remove associated writing section if it exists
          if (modifiableContent.writing && modifiableContent.writing[change.elementId]) {
            delete modifiableContent.writing[change.elementId];
          }
          break;
        }
        case DocumentChangeType.OUTLINE_ITEM_MODIFIED: {
          findAndModifyOutlineItem(modifiableContent.outline, change.elementId, (itemToModify) => {
            if (change.payload.propertyName === 'title') {
              itemToModify.title = change.payload.newValue as string;
            } else if (change.payload.propertyName === 'number') {
              itemToModify.number = change.payload.newValue as string;
            }
            // Add other modifiable properties here
          });
          break;
        }
        case DocumentChangeType.OUTLINE_ITEM_MOVED: {
          const { oldParentItemId, newParentItemId, newIndex } = change.payload;
          const { items: tempOutline, removed: movedItem } = removeItem(modifiableContent.outline, change.elementId);
          if (movedItem) {
            if (newParentItemId) {
              findAndModifyOutlineItem(tempOutline, newParentItemId, (parentItem) => {
                if (!parentItem.children) parentItem.children = [];
                parentItem.children.splice(newIndex !== undefined ? newIndex : parentItem.children.length, 0, movedItem);
              });
            } else {
              tempOutline.splice(newIndex !== undefined ? newIndex : tempOutline.length, 0, movedItem);
            }
            modifiableContent.outline = tempOutline;
          }
          break;
        }

        // --- Note Handlers ---
        case DocumentChangeType.NOTE_ADDED: {
          modifiableContent.notes.push({ ...change.payload.note } as SharedNote);
          break;
        }
        case DocumentChangeType.NOTE_REMOVED: {
          modifiableContent.notes = modifiableContent.notes.filter(note => note.id !== change.elementId);
          break;
        }
        case DocumentChangeType.NOTE_MODIFIED: {
          const noteIndex = modifiableContent.notes.findIndex(note => note.id === change.elementId);
          if (noteIndex > -1) {
            const modifiableNote = modifiableContent.notes[noteIndex];
            const { propertyName, newValue } = change.payload;
            if (propertyName in modifiableNote) {
              (modifiableNote as any)[propertyName] = newValue;
            }
          }
          break;
        }

        // --- Writing Section & Citation Handlers ---
        case DocumentChangeType.WRITING_SECTION_MODIFIED: {
          const sectionId = change.elementId;
          if (!modifiableContent.writing) modifiableContent.writing = {};
          const patch = change.payload.patch;
          let newContent = '';
          let lastIndex = 0;
          for (const [type, text] of patch) {
            if (type === diff.EQUAL) {
              newContent += text;
              lastIndex += text.length;
            } else if (type === diff.INSERT) {
              newContent += text;
            } else if (type === diff.DELETE) {
              lastIndex += text.length;
            }
          }
          modifiableContent.writing[sectionId] = {
            ...(modifiableContent.writing[sectionId] || { citations: [] }), // Preserve citations if only content changes
            content: newContent,
          };
          break;
        }
        case DocumentChangeType.CITATION_ADDED: {
          const sectionId = change.elementId; // Here elementId is sectionId
          if (!modifiableContent.writing) modifiableContent.writing = {};
          if (!modifiableContent.writing[sectionId]) modifiableContent.writing[sectionId] = { content: '', citations: [] };
          if (!modifiableContent.writing[sectionId].citations) modifiableContent.writing[sectionId].citations = [];
          modifiableContent.writing[sectionId].citations!.push({ ...change.payload.citation } as SharedCitation);
          break;
        }
        case DocumentChangeType.CITATION_REMOVED: {
          const sectionId = change.elementId; // Here elementId is sectionId
          if (modifiableContent.writing?.[sectionId]?.citations) {
            modifiableContent.writing[sectionId].citations = modifiableContent.writing[sectionId].citations!.filter(
              c => c.id !== change.payload.citationId
            );
          }
          break;
        }
        case DocumentChangeType.CITATION_MODIFIED: {
          const sectionId = change.elementId; // Here elementId is sectionId
          if (modifiableContent.writing?.[sectionId]?.citations) {
            const citationIndex = modifiableContent.writing[sectionId].citations!.findIndex(
              c => c.id === change.payload.citation.id
            );
            if (citationIndex > -1) {
              modifiableContent.writing[sectionId].citations![citationIndex] = { ...change.payload.citation } as SharedCitation;
            }
          }
          break;
        }

        default:
          // Using `as any` for change to access elementType for logging, as it might be an unhandled type
          console.warn(`Unknown or unhandled change elementType: ${(change as any).elementType} for document ${documentId}`);
      }
    }

    const result = await this.updateDocumentContent(documentId, modifiableContent);

    // Optionally, create a new version after applying changes
    // if (result && userIdPerformingChange) {
    //   await this.createDocumentVersion(documentId, userIdPerformingChange, modifiableContent, "Applied granular delta changes");
    // }

    // console.log(`Document ${documentId} updated with changes. New content:`, result?.content); // Verbose log removed
    if (result) {
      console.log(`Successfully applied ${changes.length} granular change(s) to document ID: ${documentId}`);
    } else {
      console.error(`Failed to apply granular changes to document ID: ${documentId} after processing.`);
    }
    return result;
  }

  // Document version operations
  async createDocumentVersion(documentId: string, userId: number, content: DocumentContentData, changeDescription?: string): Promise<DocumentVersion> {
    // Get the latest version number for this document
    const versions = await this.getDocumentVersions(documentId);
    const versionNumber = versions.length > 0 
      ? Math.max(...versions.map(v => v.versionNumber)) + 1 
      : 1;
    
    // Calculate diff metrics for the new version
    let addedLines = 0;
    let removedLines = 0;
    let changedSections: ChangedSection[] = [];
    
    // Only calculate diffs if this isn't the first version
    if (versions.length > 0) {
      // Get the most recent version for comparison
      const previousVersion = versions.reduce((latest, current) => 
        latest.versionNumber > current.versionNumber ? latest : current
      );
      
      // Calculate diff metrics by comparing outlines
      if (previousVersion.content) {
        try {
          // Compare outline items
          const currentOutline = content.outline || [];
          const previousOutline = previousVersion.content.outline || [];
          
          // Get flat arrays of outline items for easier comparison
          const flattenOutline = (items: OutlineItem[]): OutlineItem[] => {
            return items.reduce((acc: OutlineItem[], item) => {
              acc.push(item);
              if (item.children && item.children.length > 0) {
                acc.push(...flattenOutline(item.children));
              }
              return acc;
            }, []);
          };
          
          const flatCurrentOutline = flattenOutline(currentOutline);
          const flatPreviousOutline = flattenOutline(previousOutline);
          
          // Find added or modified outline items
          flatCurrentOutline.forEach(current => {
            const previous = flatPreviousOutline.find(p => p.id === current.id);
            
            if (!previous) {
              // Added item
              addedLines += 1;
              changedSections.push({
                type: 'outline',
                id: current.id,
                title: current.title,
                changes: [`+ Added outline item: "${current.title}"`]
              });
            } else if (previous.title !== current.title) {
              // Modified item
              addedLines += 1;
              removedLines += 1;
              changedSections.push({
                type: 'outline',
                id: current.id,
                title: current.title,
                changes: [
                  `- ${previous.title}`,
                  `+ ${current.title}`
                ]
              });
            }
          });
          
          // Find removed outline items
          flatPreviousOutline.forEach(previous => {
            const exists = flatCurrentOutline.some(c => c.id === previous.id);
            if (!exists) {
              removedLines += 1;
              changedSections.push({
                type: 'outline',
                id: previous.id,
                title: previous.title,
                changes: [`- Removed outline item: "${previous.title}"`]
              });
            }
          });
          
          // Compare notes
          const currentNotes = content.notes || [];
          const previousNotes = previousVersion.content.notes || [];
          
          // Find added or modified notes
          currentNotes.forEach(current => {
            const previous = previousNotes.find(p => p.id === current.id);
            
            if (!previous) {
              // Added note
              const lineCount = current.content ? current.content.split(/<\/?p>|<\/?li>/).length : 1;
              addedLines += lineCount;
              changedSections.push({
                type: 'note',
                id: current.id,
                title: current.title || 'Untitled',
                changes: [`+ Added note: "${current.title || 'Untitled'}" (${lineCount} lines)`]
              });
            } else {
              // Check if title or content changed
              const changes: string[] = [];
              
              if (previous.title !== current.title) {
                changes.push(`- Title: ${previous.title || 'Untitled'}`);
                changes.push(`+ Title: ${current.title || 'Untitled'}`);
                addedLines += 1;
                removedLines += 1;
              }
              
              if (previous.content !== current.content) {
                // Estimate content changes based on rough line count
                const prevContent = previous.content || '';
                const currContent = current.content || '';
                const prevLineCount = prevContent.split(/<\/?p>|<\/?li>/).length;
                const currLineCount = currContent.split(/<\/?p>|<\/?li>/).length;
                
                if (prevLineCount < currLineCount) {
                  addedLines += (currLineCount - prevLineCount);
                  changes.push(`+ Added approximately ${currLineCount - prevLineCount} lines of content`);
                } else if (prevLineCount > currLineCount) {
                  removedLines += (prevLineCount - currLineCount);
                  changes.push(`- Removed approximately ${prevLineCount - currLineCount} lines of content`);
                } else {
                  addedLines += 1;
                  removedLines += 1;
                  changes.push(`~ Modified content (same line count)`);
                }
              }
              
              if (changes.length > 0) {
                changedSections.push({
                  type: 'note',
                  id: current.id,
                  title: current.title || 'Untitled',
                  changes
                });
              }
            }
          });
          
          // Find removed notes
          previousNotes.forEach(previous => {
            const exists = currentNotes.some(c => c.id === previous.id);
            if (!exists) {
              const prevContent = previous.content || '';
              const lineCount = prevContent.split(/<\/?p>|<\/?li>/).length;
              removedLines += lineCount;
              changedSections.push({
                type: 'note',
                id: previous.id,
                title: previous.title || 'Untitled',
                changes: [`- Removed note: "${previous.title || 'Untitled'}" (${lineCount} lines)`]
              });
            }
          });
        } catch (error) {
          console.error("Error calculating diff metrics:", error);
          // If there's an error, use default values
          addedLines = 1;
          changedSections = [{
            type: 'note',
            id: 'error',
            title: 'Error in Diff Calculation',
            changes: [
              '~ Error computing diff details',
              `+ Changes were made in version ${versionNumber}`
            ]
          }];
        }
      }
    } else {
      // First version, mark everything as added
      addedLines = content.outline.length + content.notes.length;
      changedSections = [{
        type: 'note',
        id: 'first-version',
        title: 'Initial Version',
        changes: ['+ First version of the document created']
      }];
    }
    
    // If no changes were detected but this isn't the first version,
    // add a default message
    if (changedSections.length === 0 && versions.length > 0) {
      addedLines = 1;
      changedSections = [{
        type: 'note',
        id: 'minor-changes',
        title: 'Minor Changes',
        changes: [
          '+ Minor formatting or metadata changes were made',
          '~ Document was saved without significant content changes'
        ]
      }];
    }
    
    // Create a new version with diff metrics
    const [version] = await db.insert(documentVersions)
      .values({
        documentId,
        userId,
        versionNumber,
        content,
        changeDescription: changeDescription || `Version ${versionNumber}`,
        addedLines,
        removedLines,
        changedSections
      })
      .returning();
    
    return version;
  }

  async getDocumentVersions(documentId: string): Promise<DocumentVersion[]> {
    return db.select()
      .from(documentVersions)
      .where(eq(documentVersions.documentId, documentId))
      .orderBy(documentVersions.versionNumber);
  }

  async getDocumentVersion(versionId: number): Promise<DocumentVersion | undefined> {
    const results = await db.select()
      .from(documentVersions)
      .where(eq(documentVersions.id, versionId));
    
    return results.length > 0 ? results[0] : undefined;
  }

  async restoreDocumentVersion(documentId: string, versionId: number, userId: number): Promise<DocumentContent | undefined> {
    // Get the version to restore
    const version = await this.getDocumentVersion(versionId);
    
    if (!version) {
      return undefined;
    }
    
    // First create a version of the current content
    const currentContent = await this.getDocumentContent(documentId);
    
    if (currentContent) {
      await this.createDocumentVersion(
        documentId, 
        userId, 
        currentContent.content, 
        "Automatic save before restoring previous version"
      );
    }
    
    // Now restore the old version
    return this.updateDocumentContent(documentId, version.content);
  }

  // Document export settings operations
  async getDocumentExportSettings(documentId: string): Promise<DocumentExportSettings | undefined> {
    const results = await db.select()
      .from(documentExportSettings)
      .where(and(eq(documentExportSettings.documentId, documentId), eq(documentExportSettings.isDeleted, false)));
    
    return results.length > 0 ? results[0] : undefined;
  }

  async saveDocumentExportSettings(documentId: string, format: string, metadata: DocumentExportMetadata): Promise<DocumentExportSettings> {
    // Check if export settings already exist for this document
    const existing = await this.getDocumentExportSettings(documentId);
    
    if (!existing) {
      // Create new export settings
      const [result] = await db.insert(documentExportSettings)
        .values({ documentId, format, metadata, updatedAt: new Date() })
        .returning();
      
      return result;
    } else {
      // Update existing export settings
      const [result] = await db.update(documentExportSettings)
        .set({ format, metadata, updatedAt: new Date() })
        .where(eq(documentExportSettings.documentId, documentId))
        .returning();
      
      return result;
    }
  }

  // User reference operations
  async getUserReferences(userId: number): Promise<Reference[]> {
    const results = await db.select()
      .from(userReferences)
      .where(eq(userReferences.userId, userId));
    
    return results.map(ref => ref.reference);
  }

  async getUserReferenceById(userId: number, referenceId: string): Promise<Reference | undefined> {
    const [result] = await db.select()
      .from(userReferences)
      .where(and(
        eq(userReferences.userId, userId),
        eq(userReferences.referenceId, referenceId)
      ));
    
    return result?.reference;
  }

  async createUserReference(userId: number, reference: Reference): Promise<Reference> {
    // Add or update timestamps
    const now = new Date().toISOString();
    const referenceWithTimestamps = {
      ...reference,
      createdAt: reference.createdAt || now,
      updatedAt: now
    };
    
    const [result] = await db.insert(userReferences)
      .values({
        userId,
        referenceId: reference.id,
        reference: referenceWithTimestamps
      })
      .returning();
    
    return result.reference;
  }

  async updateUserReference(userId: number, reference: Reference): Promise<Reference | undefined> {
    // Update timestamp
    const updatedReference = {
      ...reference,
      updatedAt: new Date().toISOString()
    };
    
    const results = await db.update(userReferences)
      .set({ 
        reference: updatedReference,
        updatedAt: new Date()
      })
      .where(and(
        eq(userReferences.userId, userId),
        eq(userReferences.referenceId, reference.id)
      ))
      .returning();
    
    return results.length > 0 ? results[0].reference : undefined;
  }

  async deleteUserReference(userId: number, referenceId: string): Promise<void> {
    await db.delete(userReferences)
      .where(and(
        eq(userReferences.userId, userId),
        eq(userReferences.referenceId, referenceId)
      ));
  }

  // Reference collection operations
  async getUserReferenceCollections(userId: number): Promise<ReferenceCollection[]> {
    const results = await db.select()
      .from(referenceCollections)
      .where(eq(referenceCollections.userId, userId));
    
    return results.map(ref => ref.collection);
  }

  async getUserReferenceCollectionById(userId: number, collectionId: string): Promise<ReferenceCollection | undefined> {
    const [result] = await db.select()
      .from(referenceCollections)
      .where(and(
        eq(referenceCollections.userId, userId),
        eq(referenceCollections.collectionId, collectionId)
      ));
    
    return result?.collection;
  }

  async createReferenceCollection(userId: number, collection: ReferenceCollection): Promise<ReferenceCollection> {
    // Add or update timestamps
    const now = new Date().toISOString();
    const collectionWithTimestamps = {
      ...collection,
      createdAt: collection.createdAt || now,
      updatedAt: now
    };
    
    const [result] = await db.insert(referenceCollections)
      .values({
        userId,
        collectionId: collection.id,
        collection: collectionWithTimestamps
      })
      .returning();
    
    return result.collection;
  }

  async updateReferenceCollection(userId: number, collection: ReferenceCollection): Promise<ReferenceCollection | undefined> {
    // Update timestamp
    const updatedCollection = {
      ...collection,
      updatedAt: new Date().toISOString()
    };
    
    const results = await db.update(referenceCollections)
      .set({ 
        collection: updatedCollection,
        updatedAt: new Date()
      })
      .where(and(
        eq(referenceCollections.userId, userId),
        eq(referenceCollections.collectionId, collection.id)
      ))
      .returning();
    
    return results.length > 0 ? results[0].collection : undefined;
  }

  async deleteReferenceCollection(userId: number, collectionId: string): Promise<void> {
    await db.delete(referenceCollections)
      .where(and(
        eq(referenceCollections.userId, userId),
        eq(referenceCollections.collectionId, collectionId)
      ));
  }

  // Document sharing operations
  async getAuthorizedUserIdsForDocument(documentId: string, permissionLevel?: 'view' | 'edit'): Promise<number[]> {
    // Fetch the document owner's ID
    const documentOwner = await db.select({ ownerId: documents.userId })
      .from(documents)
      .where(eq(documents.id, documentId))
      .limit(1);

    const ownerId = documentOwner.length > 0 ? documentOwner[0].ownerId : null;

    // Fetch users with explicit share permissions
    let sharedUsersQuery = db.select({ userId: documentShares.userId })
      .from(documentShares)
      .where(eq(documentShares.documentId, documentId));

    if (permissionLevel) {
      // If a specific permissionLevel is requested, filter by it.
      // Note: For broadcasts, typically all shared users (view or edit) should receive updates.
      // The `permissionLevel` parameter here might be more relevant for other authorization checks,
      // but for general broadcast authorization, we usually want all involved users.
      // If the intent for broadcasts is "anyone with at least 'view' access", this logic might need adjustment
      // if `permissionLevel` is passed as 'edit' (it would only return editors).
      // For now, adhering to the existing optional parameter.
      sharedUsersQuery = sharedUsersQuery.where(eq(documentShares.permissionLevel, permissionLevel));
    }

    const sharedUserResults = await sharedUsersQuery;
    const sharedUserIds = sharedUserResults.map(r => r.userId);

    // Combine owner ID with shared user IDs, ensuring uniqueness
    const authorizedUserIds = new Set<number>();
    if (ownerId !== null) {
      authorizedUserIds.add(ownerId);
    }
    sharedUserIds.forEach(id => authorizedUserIds.add(id));

    return Array.from(authorizedUserIds);
  }

  async addShare(documentId: string, targetUserId: number, permissionLevel: 'view' | 'edit'): Promise<void> {
    await db.insert(documentShares)
      .values({
        documentId,
        userId: targetUserId,
        permissionLevel,
      })
      .onConflictDoUpdate({ // If share exists, update permission and sharedAt
        target: [documentShares.documentId, documentShares.userId],
        set: {
          permissionLevel,
          sharedAt: new Date(),
        }
      });
  }

  async updateSharePermission(documentId: string, targetUserId: number, newPermissionLevel: 'view' | 'edit'): Promise<void> {
    await db.update(documentShares)
      .set({ permissionLevel: newPermissionLevel, sharedAt: new Date() })
      .where(and(eq(documentShares.documentId, documentId), eq(documentShares.userId, targetUserId)));
  }

  async removeShare(documentId: string, targetUserId: number): Promise<void> {
    await db.delete(documentShares)
      .where(and(eq(documentShares.documentId, documentId), eq(documentShares.userId, targetUserId)));
  }

  async getDocumentShares(documentId: string): Promise<Array<{ userId: number; username: string; permissionLevel: 'view' | 'edit' }>> {
    const results = await db.select({
        userId: users.id,
        username: users.username,
        permissionLevel: documentShares.permissionLevel,
      })
      .from(documentShares)
      .innerJoin(users, eq(documentShares.userId, users.id))
      .where(eq(documentShares.documentId, documentId));

    return results.map(r => ({
      ...r,
      // Drizzle returns enum values as strings, so direct use is fine if 'view'|'edit' matches enum
      permissionLevel: r.permissionLevel as 'view' | 'edit'
    }));
  }

  async getSharedDocumentsForUser(userId: number): Promise<Array<{
    documentId: string;
    title: string;
    ownerUserId: number;
    ownerUsername: string;
    permissionLevel: 'view' | 'edit';
    sharedAt: Date;
  }>> {
    const results = await db.select({
        documentId: documents.id,
        title: documents.title,
        ownerUserId: documents.userId, // This is the original owner's ID
        ownerUsername: users.username, // This is the original owner's username
        permissionLevel: documentShares.permissionLevel,
        sharedAt: documentShares.sharedAt,
      })
      .from(documentShares)
      .innerJoin(documents, eq(documentShares.documentId, documents.id))
      .innerJoin(users, eq(documents.userId, users.id)) // Join with users table for owner's username
      .where(eq(documentShares.userId, userId)) // Where the share is for the current user
      .orderBy(documentShares.sharedAt); // Optional: order by when it was shared

    return results.map(r => ({
      ...r,
      permissionLevel: r.permissionLevel as 'view' | 'edit'
    }));
  }

  async getUserSharePermission(documentId: string, userId: number): Promise<'view' | 'edit' | null> {
    const [share] = await db.select({ permissionLevel: documentShares.permissionLevel })
      .from(documentShares)
      .where(and(
        eq(documentShares.documentId, documentId),
        eq(documentShares.userId, userId)
      ))
      .limit(1);

    return share ? share.permissionLevel as 'view' | 'edit' : null;
  }
  // getUserByUsername is already defined. Its return type is Promise<User | undefined>
  // For sharing modal user lookup, we might want just { id, username }.
  // The existing one returns more, which is fine, the API can strip it down.

  // Document Chat operations
  async addChatMessage(documentId: string, userId: number, username: string, messageText: string): Promise<DocumentChatMessage> {
    const [newMessage] = await db.insert(documentChatMessages)
      .values({
        documentId,
        userId,
        username,
        messageText,
        // createdAt is handled by defaultNow() in schema
      })
      .returning();
    return newMessage;
  }

  async getChatMessages(documentId: string): Promise<DocumentChatMessage[]> {
    return await db.select()
      .from(documentChatMessages)
      .where(eq(documentChatMessages.documentId, documentId))
      .orderBy(documentChatMessages.createdAt); // Default order is ASC, which is good for chat history
  }

  // Share Token operations
  async createShareToken(documentId: string, permissionLevel: 'view' | 'edit', creatorUserId: number, expiresInHours?: number): Promise<string> {
    const tokenValue = crypto.randomBytes(32).toString('hex'); // Generate a 64-character hex token
    let expiresAt: Date | null = null;
    if (expiresInHours) {
      expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + expiresInHours);
    }

    await db.insert(shareTokens).values({
      token: tokenValue,
      documentId,
      permissionLevel,
      creatorUserId,
      expiresAt,
    });
    return tokenValue;
  }

  async validateShareToken(tokenValue: string): Promise<{ documentId: string; permissionLevel: 'view' | 'edit'; creatorUserId: number | null } | null> {
    const [tokenRecord] = await db.select()
      .from(shareTokens)
      .where(eq(shareTokens.token, tokenValue))
      .limit(1);

    if (!tokenRecord) {
      return null; // Token not found
    }

    // Check for expiration
    if (tokenRecord.expiresAt && tokenRecord.expiresAt < new Date()) {
      // Optionally, delete expired token here
      await this.deleteShareToken(tokenValue);
      return null; // Token expired
    }

    return {
      documentId: tokenRecord.documentId,
      permissionLevel: tokenRecord.permissionLevel,
      creatorUserId: tokenRecord.creatorUserId,
    };
  }

  async deleteShareToken(tokenValue: string): Promise<void> {
    await db.delete(shareTokens).where(eq(shareTokens.token, tokenValue));
  }

  // Document Access Request operations
  async getPendingAccessRequests(ownerUserId: number): Promise<(DocumentAccessRequest & { documentTitle: string; requestingUsername: string; })[]> {
    const results = await db.select({
      id: documentAccessRequests.id,
      documentId: documentAccessRequests.documentId,
      requestingUserId: documentAccessRequests.requestingUserId,
      ownerUserId: documentAccessRequests.ownerUserId,
      status: documentAccessRequests.status,
      createdAt: documentAccessRequests.createdAt,
      updatedAt: documentAccessRequests.updatedAt,
      documentTitle: documents.title,
      requestingUsername: users.username,
    })
      .from(documentAccessRequests)
      .innerJoin(documents, eq(documentAccessRequests.documentId, documents.id))
      .innerJoin(users, eq(documentAccessRequests.requestingUserId, users.id))
      .where(and(
        eq(documentAccessRequests.ownerUserId, ownerUserId),
        eq(documentAccessRequests.status, "pending")
      ));
    return results as (DocumentAccessRequest & { documentTitle: string; requestingUsername: string; })[];
  }

  async updateAccessRequestStatus(requestId: number, ownerUserId: number, status: "approved" | "denied", permissionLevel?: "view" | "edit"): Promise<DocumentAccessRequest | undefined> {
    const [request] = await db.select()
      .from(documentAccessRequests)
      .where(and(
        eq(documentAccessRequests.id, requestId),
        eq(documentAccessRequests.ownerUserId, ownerUserId)
      ));

    if (!request) {
      return undefined;
    }

    const [updatedRequest] = await db.update(documentAccessRequests)
      .set({ status, updatedAt: new Date() })
      .where(eq(documentAccessRequests.id, requestId))
      .returning();

    if (status === "approved" && permissionLevel) {
      await this.addShare(request.documentId, request.requestingUserId, permissionLevel);
    }

    return updatedRequest;
  }
}

export const storage = new DatabaseStorage();
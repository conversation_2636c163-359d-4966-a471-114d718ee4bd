import { useToast } from "@/hooks/use-toast"
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast 
            key={id} 
            {...props} 
            variant="default"
            className="!bg-background !border-border !text-foreground shadow-lg !opacity-100"
            style={{
              backgroundColor: "var(--background)",
              borderColor: "var(--border)",
              color: "var(--foreground)",
              opacity: 1
            }}
          >
            <div className="grid gap-1">
              {title && (
                <ToastTitle 
                  className="!text-foreground font-semibold"
                  style={{ color: "var(--foreground)" }}
                >
                  {title}
                </ToastTitle>
              )}
              {description && (
                <ToastDescription 
                  className="!text-muted-foreground"
                  style={{ color: "var(--muted-foreground)" }}
                >
                  {description}
                </ToastDescription>
              )}
            </div>
            {action}
            <ToastClose className="text-foreground hover:text-primary"/>
          </Toast>
        )
      })}
      <ToastViewport style={{ background: "none !important" }} />
    </ToastProvider>
  )
}

import { db } from '../db'; // Assuming db instance is exported from here
import { sql } from 'drizzle-orm';
import {
  documents as documentsSchema,
  documentContents as documentContentsSchema,
  documentExportSettings as documentExportSettingsSchema,
  documentVersions as documentVersionsSchema
} from '../../shared/schema';
import crypto from 'crypto';

// Define the old schema for documents to select existing data
// This is a simplified representation for the migration context
const oldDocumentsSchema = {
  tableName: 'documents',
  columns: {
    id: { type: 'serial', primaryKey: true }, // Old type
    userId: { type: 'integer' },
    title: { type: 'text' },
    createdAt: { type: 'timestamp' },
    updatedAt: { type: 'timestamp' },
  }
};

async function runMigration() {
  console.log('Starting document ID migration to UUIDs...');

  await db.execute(sql`BEGIN;`);
  console.log('Transaction started.');

  try {
    // 1. Disable Foreign Key Constraints (Conceptual - actual commands depend on DB)
    // For PostgreSQL, one might temporarily disable triggers or defer constraints.
    // However, since we are creating new tables and then renaming,
    // we will drop the old tables which implicitly drops their FKs.
    // If FKs were not set with ON DELETE CASCADE, they would need to be dropped manually from child tables first.
    // This script assumes that dropping the old 'documents' table will be sufficient
    // or that FKs on the *old* child tables pointing to the *old* documents table will be dropped
    // when those child tables themselves are dropped and recreated.

    // 2. Fetch all existing documents and generate UUIDs
    console.log('Fetching existing documents...');
    // We need to select from the old table structure.
    // Drizzle typically uses its schema objects. For a migration where the schema has ALREADY changed in code,
    // selecting from `documentsSchema` might try to select a text `id`.
    // A raw query is safer here to ensure we get the old integer IDs.
    const oldDocuments: Array<{ id: number; userId: number; title: string; createdAt: Date; updatedAt: Date; }> =
      await db.execute(sql`SELECT id, user_id, title, created_at, updated_at FROM documents;`);

    const idMap = new Map<number, string>();
    const newDocumentsData = oldDocuments.map(doc => {
      const newUuid = crypto.randomUUID();
      idMap.set(doc.id, newUuid);
      return {
        id: newUuid, // New UUID
        userId: doc.userId,
        title: doc.title,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
      };
    });
    console.log(`Generated ${idMap.size} new UUIDs for documents.`);

    // 3. Create new `documents_temp` table and insert data
    // Drizzle's `pgTable` with a different name (`documents_temp`) using the new schema.
    // For simplicity, we'll use raw SQL for table creation and renaming in this conceptual script.
    // The actual Drizzle migration tool would handle schema changes more gracefully.

    console.log('Creating temporary documents_temp table...');
    await db.execute(sql`
      CREATE TABLE documents_temp (
        id TEXT PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);

    if (newDocumentsData.length > 0) {
      // Drizzle's batch insert would be good here if directly using the ORM for inserts
      // For this conceptual script, let's imagine direct inserts or a helper
      for (const doc of newDocumentsData) {
        await db.execute(sql`INSERT INTO documents_temp (id, user_id, title, created_at, updated_at) VALUES (${doc.id}, ${doc.userId}, ${doc.title}, ${doc.createdAt}, ${doc.updatedAt});`);
      }
      console.log('Inserted data into documents_temp.');
    }


    // 4. Process related tables

    // DocumentContents
    console.log('Processing document_contents...');
    const oldDocContents: Array<{ id: number; document_id: number; content: any; }> =
      await db.execute(sql`SELECT id, document_id, content FROM document_contents;`);

    const newDocContentsData = oldDocContents.map(dc => {
      const newDocId = idMap.get(dc.document_id);
      if (!newDocId) {
        console.warn(`Orphaned document_content found: old document_id ${dc.document_id} not in map. Skipping.`);
        return null;
      }
      return { id: dc.id, documentId: newDocId, content: dc.content };
    }).filter(Boolean);

    console.log('Creating temporary document_contents_temp table...');
    await db.execute(sql`
      CREATE TABLE document_contents_temp (
        id SERIAL PRIMARY KEY,
        document_id TEXT NOT NULL, -- Will add FK after documents_temp is renamed
        content JSONB NOT NULL
      );
    `);
    if (newDocContentsData.length > 0) {
      for (const dc of newDocContentsData) {
        await db.execute(sql`INSERT INTO document_contents_temp (id, document_id, content) VALUES (${dc!.id}, ${dc!.documentId}, ${JSON.stringify(dc!.content)});`);
      }
      console.log('Inserted data into document_contents_temp.');
    }

    // DocumentExportSettings
    console.log('Processing document_export_settings...');
    const oldDocExportSettings: Array<{ id: number; document_id: number; format: string; metadata: any; updated_at: Date; }> =
      await db.execute(sql`SELECT id, document_id, format, metadata, updated_at FROM document_export_settings;`);

    const newDocExportSettingsData = oldDocExportSettings.map(des => {
      const newDocId = idMap.get(des.document_id);
      if (!newDocId) {
        console.warn(`Orphaned document_export_setting found: old document_id ${des.document_id} not in map. Skipping.`);
        return null;
      }
      return { id: des.id, documentId: newDocId, format: des.format, metadata: des.metadata, updatedAt: des.updated_at };
    }).filter(Boolean);
    
    console.log('Creating temporary document_export_settings_temp table...');
    await db.execute(sql`
      CREATE TABLE document_export_settings_temp (
        id SERIAL PRIMARY KEY,
        document_id TEXT NOT NULL, -- Will add FK after documents_temp is renamed
        format TEXT NOT NULL DEFAULT 'apa-student',
        metadata JSONB NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);
    if (newDocExportSettingsData.length > 0) {
      for (const des of newDocExportSettingsData) {
        await db.execute(sql`INSERT INTO document_export_settings_temp (id, document_id, format, metadata, updated_at) VALUES (${des!.id}, ${des!.documentId}, ${des!.format}, ${JSON.stringify(des!.metadata)}, ${des!.updatedAt});`);
      }
      console.log('Inserted data into document_export_settings_temp.');
    }

    // DocumentVersions
    console.log('Processing document_versions...');
    const oldDocVersions: Array<{ id: number; document_id: number; user_id: number; version_number: number; content: any; change_description: string | null; added_lines: number; removed_lines: number; changed_sections: any; created_at: Date; }> =
      await db.execute(sql`SELECT id, document_id, user_id, version_number, content, change_description, added_lines, removed_lines, changed_sections, created_at FROM document_versions;`);

    const newDocVersionsData = oldDocVersions.map(dv => {
      const newDocId = idMap.get(dv.document_id);
      if (!newDocId) {
        console.warn(`Orphaned document_version found: old document_id ${dv.document_id} not in map. Skipping.`);
        return null;
      }
      return { ...dv, document_id_new: newDocId };
    }).filter(Boolean);

    console.log('Creating temporary document_versions_temp table...');
    await db.execute(sql`
      CREATE TABLE document_versions_temp (
        id SERIAL PRIMARY KEY,
        document_id TEXT NOT NULL, -- Will add FK after documents_temp is renamed
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        version_number INTEGER NOT NULL,
        content JSONB NOT NULL,
        change_description VARCHAR(255),
        added_lines INTEGER DEFAULT 0,
        removed_lines INTEGER DEFAULT 0,
        changed_sections JSONB,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);
    if (newDocVersionsData.length > 0) {
      for (const dv of newDocVersionsData) {
        await db.execute(sql`INSERT INTO document_versions_temp (id, document_id, user_id, version_number, content, change_description, added_lines, removed_lines, changed_sections, created_at) VALUES (${dv!.id}, ${dv!.document_id_new}, ${dv!.user_id}, ${dv!.version_number}, ${JSON.stringify(dv!.content)}, ${dv!.change_description}, ${dv!.added_lines}, ${dv!.removed_lines}, ${JSON.stringify(dv!.changed_sections)}, ${dv!.created_at});`);
      }
      console.log('Inserted data into document_versions_temp.');
    }

    // 5. Drop old tables and rename new tables
    // Order matters: drop child tables first or use CASCADE if supported and desired.
    // Since we are replacing all relevant tables, we drop them.
    console.log('Dropping old tables...');
    await db.execute(sql`DROP TABLE document_contents;`);
    await db.execute(sql`DROP TABLE document_export_settings;`);
    await db.execute(sql`DROP TABLE document_versions;`);
    await db.execute(sql`DROP TABLE documents;`); // Drop old documents table last

    console.log('Renaming temporary tables...');
    await db.execute(sql`ALTER TABLE documents_temp RENAME TO documents;`);
    await db.execute(sql`ALTER TABLE document_contents_temp RENAME TO document_contents;`);
    await db.execute(sql`ALTER TABLE document_export_settings_temp RENAME TO document_export_settings;`);
    await db.execute(sql`ALTER TABLE document_versions_temp RENAME TO document_versions;`);

    // 6. Re-establish Foreign Key Constraints on new tables
    // These FKs point from the new child tables to the new documents table.
    console.log('Re-establishing foreign key constraints...');
    await db.execute(sql`ALTER TABLE document_contents ADD CONSTRAINT fk_document_contents_document_id FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE;`);
    await db.execute(sql`ALTER TABLE document_export_settings ADD CONSTRAINT fk_document_export_settings_document_id FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE;`);
    await db.execute(sql`ALTER TABLE document_versions ADD CONSTRAINT fk_document_versions_document_id FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE;`);

    await db.execute(sql`COMMIT;`);
    console.log('Migration completed successfully and transaction committed.');

  } catch (error) {
    await db.execute(sql`ROLLBACK;`);
    console.error('Migration failed, transaction rolled back:', error);
    throw error; // Re-throw error to indicate failure
  }
}

// This function would be called by a migration runner.
// For example: runMigration().catch(e => { console.error("Migration runner error:", e); process.exit(1); });

// For the purpose of this tool, I am just defining the function.
// To make it runnable, you'd need a db connection setup (`../db.ts`)
// and a way to execute this script (e.g., `ts-node server/migrations/0001_...ts`).
// The `db.execute(sql`...`)` is a placeholder for how Drizzle allows raw SQL execution.
// The actual db instance would be from your Drizzle setup.
// JSON stringification for content/metadata is a simplified way to handle JSONB inserts in raw SQL.
// Drizzle's ORM insert methods would handle this more cleanly.

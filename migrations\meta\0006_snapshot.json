{"id": "a91e863f-3c95-4309-a5ac-90d68fe2f0a6", "prevId": "1e6603ea-d2c5-4c0b-86ca-4a2e4b564260", "version": "7", "dialect": "postgresql", "tables": {"public.document_access_requests": {"name": "document_access_requests", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "requesting_user_id": {"name": "requesting_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "owner_user_id": {"name": "owner_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"document_access_requests_document_id_documents_id_fk": {"name": "document_access_requests_document_id_documents_id_fk", "tableFrom": "document_access_requests", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "document_access_requests_requesting_user_id_users_id_fk": {"name": "document_access_requests_requesting_user_id_users_id_fk", "tableFrom": "document_access_requests", "tableTo": "users", "columnsFrom": ["requesting_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "document_access_requests_owner_user_id_users_id_fk": {"name": "document_access_requests_owner_user_id_users_id_fk", "tableFrom": "document_access_requests", "tableTo": "users", "columnsFrom": ["owner_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_chat_messages": {"name": "document_chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "message_text": {"name": "message_text", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"document_chat_messages_document_id_documents_id_fk": {"name": "document_chat_messages_document_id_documents_id_fk", "tableFrom": "document_chat_messages", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "document_chat_messages_user_id_users_id_fk": {"name": "document_chat_messages_user_id_users_id_fk", "tableFrom": "document_chat_messages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_contents": {"name": "document_contents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": true}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"document_contents_document_id_documents_id_fk": {"name": "document_contents_document_id_documents_id_fk", "tableFrom": "document_contents", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_export_settings": {"name": "document_export_settings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "format": {"name": "format", "type": "text", "primaryKey": false, "notNull": true, "default": "'apa-student'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"document_export_settings_document_id_documents_id_fk": {"name": "document_export_settings_document_id_documents_id_fk", "tableFrom": "document_export_settings", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_shares": {"name": "document_shares", "schema": "", "columns": {"document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "permission_level": {"name": "permission_level", "type": "document_permission", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'view'"}, "shared_at": {"name": "shared_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"document_shares_document_id_documents_id_fk": {"name": "document_shares_document_id_documents_id_fk", "tableFrom": "document_shares", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "document_shares_user_id_users_id_fk": {"name": "document_shares_user_id_users_id_fk", "tableFrom": "document_shares", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"document_shares_document_id_user_id_pk": {"name": "document_shares_document_id_user_id_pk", "columns": ["document_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_versions": {"name": "document_versions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "version_number": {"name": "version_number", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": true}, "change_description": {"name": "change_description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "added_lines": {"name": "added_lines", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "removed_lines": {"name": "removed_lines", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "changed_sections": {"name": "changed_sections", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"document_versions_document_id_documents_id_fk": {"name": "document_versions_document_id_documents_id_fk", "tableFrom": "document_versions", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_versions_user_id_users_id_fk": {"name": "document_versions_user_id_users_id_fk", "tableFrom": "document_versions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"documents_user_id_users_id_fk": {"name": "documents_user_id_users_id_fk", "tableFrom": "documents", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inline_comments": {"name": "inline_comments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "selection": {"name": "selection", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"inline_comments_document_id_documents_id_fk": {"name": "inline_comments_document_id_documents_id_fk", "tableFrom": "inline_comments", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "inline_comments_user_id_users_id_fk": {"name": "inline_comments_user_id_users_id_fk", "tableFrom": "inline_comments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reference_collections": {"name": "reference_collections", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "collection_id": {"name": "collection_id", "type": "text", "primaryKey": false, "notNull": true}, "collection": {"name": "collection", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"reference_collections_user_id_users_id_fk": {"name": "reference_collections_user_id_users_id_fk", "tableFrom": "reference_collections", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.share_tokens": {"name": "share_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "permission_level": {"name": "permission_level", "type": "document_permission", "typeSchema": "public", "primaryKey": false, "notNull": true}, "creator_user_id": {"name": "creator_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"share_tokens_document_id_documents_id_fk": {"name": "share_tokens_document_id_documents_id_fk", "tableFrom": "share_tokens", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "share_tokens_creator_user_id_users_id_fk": {"name": "share_tokens_creator_user_id_users_id_fk", "tableFrom": "share_tokens", "tableTo": "users", "columnsFrom": ["creator_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"share_tokens_token_unique": {"name": "share_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_references": {"name": "user_references", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "reference_id": {"name": "reference_id", "type": "text", "primaryKey": false, "notNull": true}, "reference": {"name": "reference", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_references_user_id_users_id_fk": {"name": "user_references_user_id_users_id_fk", "tableFrom": "user_references", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "is_premium": {"name": "is_premium", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.document_permission": {"name": "document_permission", "schema": "public", "values": ["view", "edit"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
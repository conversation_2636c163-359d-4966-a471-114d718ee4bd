import React from 'react';
import { Users, Wifi, WifiOff, AlertCircle, FileEdit } from 'lucide-react';

interface CollaborationStatusProps {
  status: 'disconnected' | 'connecting' | 'connected';
  connectedUsers: number[];
  error: Error | null;
  fallbackMode?: boolean;
}

export function CollaborationStatus({ status, connectedUsers, error, fallbackMode = false }: CollaborationStatusProps) {
  // If in fallback mode, show the offline indicator
  if (fallbackMode) {
    return (
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1 cursor-help" title="Offline mode - collaboration disabled">
          <FileEdit className="h-4 w-4 text-muted-foreground" />
          <span className="text-xs font-medium text-muted-foreground">Offline</span>
        </div>
      </div>
    );
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return <Wifi className="h-4 w-4 text-icon-green" />;
      case 'connecting':
        return <Wifi className="h-4 w-4 text-icon-amber animate-pulse" />;
      case 'disconnected':
        return <WifiOff className="h-4 w-4 text-destructive" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return 'Real-time collaboration active';
      case 'connecting':
        return 'Connecting to collaboration server...';
      case 'disconnected':
        return error ? `Disconnected: ${error.message}` : 'Disconnected from collaboration server';
    }
  };

  // Get unique user count
  const uniqueUserCount = Array.from(new Set(connectedUsers)).length;

  return (
    <div className="flex items-center space-x-2">
      <div className="flex items-center space-x-1 cursor-help" title={getStatusText()}>
        {getStatusIcon()}
        {status === 'connected' && (
          <span className="text-xs font-medium text-icon-green">Live</span>
        )}
      </div>

      {error && (
        <div title={`Error: ${error.message}`}>
          <AlertCircle className="h-4 w-4 text-destructive" />
        </div>
      )}
    </div>
  );
}
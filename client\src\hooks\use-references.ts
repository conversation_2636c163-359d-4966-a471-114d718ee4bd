import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { type Reference, type ReferenceCollection } from "@shared/schema";
import { nanoid } from "nanoid";

// Fetch all global references for the current user
export function useUserReferences() {
  return useQuery({
    queryKey: ['/api/references'],
  });
}

// Fetch a specific reference by ID
export function useUserReference(referenceId: string) {
  return useQuery({
    queryKey: ['/api/references', referenceId],
    enabled: !!referenceId,
  });
}

// Add a new reference
export function useAddReference() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (referenceData: Partial<Reference>) => {
      // Make sure it has an ID
      if (!referenceData.id) {
        referenceData.id = nanoid();
      }
      
      // Set timestamps if not provided
      const now = new Date().toISOString();
      if (!referenceData.createdAt) {
        referenceData.createdAt = now;
      }
      referenceData.updatedAt = now;
      
      const response = await fetch('/api/references', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(referenceData),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to add reference');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate references query to refetch
      queryClient.invalidateQueries({ queryKey: ['/api/references'] });
    },
  });
}

// Update an existing reference
export function useUpdateReference() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (reference: Reference) => {
      const response = await fetch(`/api/references/${reference.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reference),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update reference');
      }
      
      return response.json();
    },
    onSuccess: (_data, variables) => {
      // Invalidate both the list query and the individual reference query
      queryClient.invalidateQueries({ queryKey: ['/api/references'] });
      queryClient.invalidateQueries({ queryKey: ['/api/references', variables.id] });
    },
  });
}

// Delete a reference
export function useDeleteReference() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (referenceId: string) => {
      const response = await fetch(`/api/references/${referenceId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete reference');
      }
      
      return true;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the references query
      queryClient.invalidateQueries({ queryKey: ['/api/references'] });
      // Also remove the individual reference from the cache
      queryClient.removeQueries({ queryKey: ['/api/references', variables] });
    },
  });
}

// Fetch all reference collections for the current user
export function useReferenceCollections() {
  return useQuery({
    queryKey: ['/api/reference-collections'],
  });
}

// Add a new collection
export function useAddCollection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (collection: Partial<ReferenceCollection>) => {
      // Make sure it has an ID
      if (!collection.id) {
        collection.id = nanoid();
      }
      
      // Set timestamps if not provided
      const now = new Date().toISOString();
      if (!collection.createdAt) {
        collection.createdAt = now;
      }
      collection.updatedAt = now;
      
      const response = await fetch('/api/reference-collections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(collection),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to add collection');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate collections query to refetch
      queryClient.invalidateQueries({ queryKey: ['/api/reference-collections'] });
    },
  });
}

// Update a collection
export function useUpdateCollection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (collection: ReferenceCollection) => {
      const response = await fetch(`/api/reference-collections/${collection.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(collection),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update collection');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate collections query to refetch
      queryClient.invalidateQueries({ queryKey: ['/api/reference-collections'] });
    },
  });
}

// Delete a collection
export function useDeleteCollection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (collectionId: string) => {
      const response = await fetch(`/api/reference-collections/${collectionId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete collection');
      }
      
      return true;
    },
    onSuccess: () => {
      // Invalidate collections query to refetch
      queryClient.invalidateQueries({ queryKey: ['/api/reference-collections'] });
    },
  });
}
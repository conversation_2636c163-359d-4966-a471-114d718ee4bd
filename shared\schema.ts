import { pgTable, text, serial, integer, boolean, jsonb, timestamp, varchar, pgEnum, primaryKey } from "drizzle-orm/pg-core";
import crypto from 'crypto';
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Export settings metadata type
export const documentExportMetadataSchema = z.object({
  // Author information
  firstName: z.string().default(""),
  middleName: z.string().default(""),
  lastName: z.string().default(""),
  coauthors: z.string().default(""),

  // Class information
  instructor: z.string().default(""),
  course: z.string().default(""),
  courseName: z.string().default(""),
  institution: z.string().default(""),
  submissionDate: z.string().default(""),

  // Title information
  shortTitle: z.string().default(""),
  subtitle: z.string().default(""),

  // Headings
  tocHeading: z.string().default("Table of Contents"),
  introductionHeading: z.string().default("Introduction"),
  conclusionHeading: z.string().default("Conclusion"),
  bibliographyHeading: z.string().default("Bibliography"),
  authorNote: z.string().default(""),
  dedication: z.string().default(""),
  abstract: z.string().default(""),
  keywords: z.string().default(""),
  footnotes: z.string().default(""),

  // Conferment information
  degreeTitle: z.string().default(""),
  college: z.string().default(""),
  copyrightYear: z.string().default(""),

  // Signers information
  signers: z.array(
    z.object({
      name: z.string().default(""),
      jobTitle: z.string().default(""),
      role: z.string().default(""),
    })
  ).default([])
});

// User preferences type
export const userPreferencesSchema = z.object({
  colorScheme: z.enum(["Light", "Dark"]).default("Light"),
  customThemeColor: z.string().default("#3b82f6"), // Default blue color for primary theme color
  fontFaceLabels: z.string().default("Aptos"),
  fontScaleLabels: z.string().default("100%"),
  fontFaceUserText: z.string().default("Aptos"),
  fontScaleUserText: z.string().default("100%"),
  textToSpeechVocalPitch: z.string().default("Baritone"),
  textToSpeechLocale: z.string().default("US Canada English"),
  textToSpeechStyle: z.string().default("Precise and articulate"),
  subscriberNameOptions: z.object({
    title: z.boolean().default(true),
    firstName: z.boolean().default(true),
    middleName: z.boolean().default(true),
    lastName: z.boolean().default(true),
    suffix: z.boolean().default(true),
  }),
  outlinePanelSize: z.number().optional(),
  notesPanelSize: z.number().optional(),
  writingPanelSize: z.number().optional(),
  visiblePanels: z.array(z.string()).optional(), // Added for persisting panel visibility
});
export type UserPreferences = z.infer<typeof userPreferencesSchema>;

// Reference type schema
export const referenceSchema = z.object({
  id: z.string(),
  type: z.string(),
  title: z.string(),
  authors: z.array(z.string()).default([]),
  year: z.string().optional(),
  journal: z.string().optional(),
  volume: z.string().optional(),
  issue: z.string().optional(),
  pages: z.string().optional(),
  publisher: z.string().optional(),
  doi: z.string().optional(),
  url: z.string().optional(),
  accessed: z.string().optional(),
  collectionId: z.string().optional(),
  // Additional fields for different reference types
  place: z.string().optional(),
  edition: z.string().optional(),
  conference: z.string().optional(),
  website: z.string().optional(),
  institution: z.string().optional(),
  number: z.string().optional(),
  university: z.string().optional(),
  newspaper: z.string().optional(),
  date: z.string().optional(),
  bookTitle: z.string().optional(),
  editors: z.array(z.string()).optional(),
  isbn: z.string().optional(),
  // Added to track creation and updates
  createdAt: z.string(),
  updatedAt: z.string()
});
export type Reference = z.infer<typeof referenceSchema>;

// Reference collection schema
export const referenceCollectionSchema = z.object({
  id: z.string(),
  name: z.string(),
  // Timestamps
  createdAt: z.string(),
  updatedAt: z.string()
});
export type ReferenceCollection = z.infer<typeof referenceCollectionSchema>;

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  isPremium: boolean("is_premium").default(false).notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  preferences: jsonb("preferences").$type<UserPreferences>(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// System Settings table
export const systemSettings = pgTable("system_settings", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: text("value").notNull(),
  description: text("description"),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User References table (for global references)
export const userReferences = pgTable("user_references", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  referenceId: text("reference_id").notNull(),
  reference: jsonb("reference").$type<Reference>().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Reference Collections table
export const referenceCollections = pgTable("reference_collections", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  collectionId: text("collection_id").notNull(),
  collection: jsonb("collection").$type<ReferenceCollection>().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Documents table
export const documents = pgTable("documents", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: integer("user_id").references(() => users.id).notNull(),
  title: text("title").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  isDeleted: boolean("is_deleted").default(false).notNull(),
});

// Defines the permissions a user has for a document
export const documentPermissionEnum = pgEnum('document_permission', ['view', 'edit']);

// Table to manage document sharing and permissions
export const documentShares = pgTable('document_shares', {
  documentId: text('document_id').notNull().references(() => documents.id, { onDelete: 'cascade' }),
  userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  permissionLevel: documentPermissionEnum('permission_level').notNull().default('view'),
  sharedAt: timestamp('shared_at').defaultNow().notNull(),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.documentId, table.userId] }),
  };
});

// Outline item type
export const outlineItemSchema: z.ZodType<{
  id: string;
  number: string;
  title: string;
  children?: Array<any>;
}> = z.object({
  id: z.string(),
  number: z.string(),
  title: z.string(),
  children: z.array(z.lazy(() => outlineItemSchema)).optional(),
});
export type OutlineItem = z.infer<typeof outlineItemSchema>;

// Document content type
// Changed section type for version history
export const changedSectionSchema = z.object({
  type: z.enum(['outline', 'note']),
  id: z.string(),
  title: z.string(),
  changes: z.array(z.string())
});
export type ChangedSection = z.infer<typeof changedSectionSchema>;

export const documentContentSchema = z.object({
  outline: z.array(outlineItemSchema),
  notes: z.array(z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    linkedOutlineId: z.string().optional(),
    // Note type for better organization and display
    type: z.enum(['text', 'image', 'video', 'file']).optional(),
    // Media URLs for different note types
    imageUrls: z.array(z.string()).optional(),
    videoUrls: z.array(z.string()).optional(),
    fileUrls: z.array(z.string()).optional(),
    // Primary asset for display priority
    primaryAssetUrl: z.string().optional(),
    // Timestamps
    createdAt: z.string(),
    updatedAt: z.string(),
  })),
  writing: z.record(z.string(), z.object({
    content: z.string(),
    citations: z.array(z.object({
      id: z.string(),
      reference: z.string(),
      marker: z.string(),
    })).optional(),
  })),
});
export type DocumentContentData = z.infer<typeof documentContentSchema>;
export type DocumentExportMetadata = z.infer<typeof documentExportMetadataSchema>;

// Document contents table
export const documentContents = pgTable("document_contents", {
  id: serial("id").primaryKey(),
  documentId: text("document_id").references(() => documents.id).notNull(),
  content: jsonb("content").notNull().$type<DocumentContentData>(),
  isDeleted: boolean("is_deleted").default(false).notNull(),
});

// Document export settings table
export const documentExportSettings = pgTable("document_export_settings", {
  id: serial("id").primaryKey(),
  documentId: text("document_id").references(() => documents.id).notNull(),
  format: text("format").notNull().default('apa-student'),
  metadata: jsonb("metadata").notNull().$type<DocumentExportMetadata>(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  isDeleted: boolean("is_deleted").default(false).notNull(),
});

// Document versions table for document history
export const documentVersions = pgTable("document_versions", {
  id: serial("id").primaryKey(),
  documentId: text("document_id").references(() => documents.id).notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  versionNumber: integer("version_number").notNull(),
  content: jsonb("content").notNull().$type<DocumentContentData>(),
  changeDescription: varchar("change_description", { length: 255 }),
  addedLines: integer("added_lines").default(0),
  removedLines: integer("removed_lines").default(0),
  changedSections: jsonb("changed_sections").default([]),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Insert schemas
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  isPremium: true,
  isActive: true,
});

export const insertDocumentSchema = createInsertSchema(documents).pick({
  userId: true,
  title: true,
});

export const insertDocumentContentSchema = createInsertSchema(documentContents).pick({
  documentId: true,
  content: true,
});

export const insertDocumentVersionSchema = createInsertSchema(documentVersions).pick({
  documentId: true,
  userId: true,
  versionNumber: true,
  content: true,
  changeDescription: true,
  addedLines: true,
  removedLines: true,
  changedSections: true,
});

export const insertDocumentExportSettingsSchema = createInsertSchema(documentExportSettings).pick({
  documentId: true,
  format: true,
  metadata: true,
});

export const insertUserReferenceSchema = createInsertSchema(userReferences).pick({
  userId: true,
  referenceId: true,
  reference: true,
});

export const insertReferenceCollectionSchema = createInsertSchema(referenceCollections).pick({
  userId: true,
  collectionId: true,
  collection: true,
});

// Types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type InsertDocumentVersion = z.infer<typeof insertDocumentVersionSchema>;
export type InsertDocumentExportSettings = z.infer<typeof insertDocumentExportSettingsSchema>;
export type InsertUserReference = z.infer<typeof insertUserReferenceSchema>;
export type InsertReferenceCollection = z.infer<typeof insertReferenceCollectionSchema>;
export type User = typeof users.$inferSelect;
export type SystemSetting = typeof systemSettings.$inferSelect;
export type InsertSystemSetting = typeof systemSettings.$inferInsert;
export type Document = typeof documents.$inferSelect;
export type DocumentContent = typeof documentContents.$inferSelect;
export type DocumentVersion = typeof documentVersions.$inferSelect;
export type DocumentExportSettings = typeof documentExportSettings.$inferSelect;
export type UserReference = typeof userReferences.$inferSelect;
export type ReferenceCollectionDB = typeof referenceCollections.$inferSelect;

// Document Chat Messages table
export const documentChatMessages = pgTable("document_chat_messages", {
  id: serial("id").primaryKey(),
  documentId: text("document_id").notNull().references(() => documents.id, { onDelete: 'cascade' }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  username: text("username").notNull(), // Denormalized for quick display
  messageText: text("message_text").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});
export type DocumentChatMessage = typeof documentChatMessages.$inferSelect;


// Moved from client/src/lib/types.ts to be shared
export enum DocumentChangeType {
  // Outline Changes
  OUTLINE_ITEM_ADDED = "outlineItemAdded",
  OUTLINE_ITEM_REMOVED = "outlineItemRemoved",
  OUTLINE_ITEM_MODIFIED = "outlineItemModified",
  OUTLINE_ITEM_MOVED = "outlineItemMoved",

  // Note Changes
  NOTE_ADDED = "noteAdded",
  NOTE_REMOVED = "noteRemoved",
  NOTE_MODIFIED = "noteModified",

  // Writing Section Changes
  WRITING_SECTION_MODIFIED = "writingSectionModified",

  // Citation Changes
  CITATION_ADDED = "citationAdded",
  CITATION_REMOVED = "citationRemoved",
  CITATION_MODIFIED = "citationModified",

  // Presence Event Types
  USER_JOINED = "userJoined",
  USER_LEFT = "userLeft",
  PRESENCE_LIST = "presenceList",

  // Chat Event Types
  CHAT_MESSAGE_SENT = "chatMessageSent", // Client -> Server
  NEW_CHAT_MESSAGE = "newChatMessage",   // Server -> Client

  // Inline Comment Event Types
  INLINE_COMMENT_ADDED = "inlineCommentAdded",
  INLINE_COMMENT_DELETED = "inlineCommentDeleted",
  INLINE_COMMENT_UPDATED = "inlineCommentUpdated",
}

// User Presence Information
export interface UserPresenceInfo {
  id: number;
  username: string;
  // Add other relevant info like avatar, color, etc., if needed later
}

// Base interface for all document changes (needs OutlineItem, Note, Citation from client types or shared)
// For now, these will be `any` or we need to ensure client types are resolvable or also shared.
// To avoid circular dependencies if these interfaces use types defined above in this file (like users, documents),
// it's better to define these interfaces using basic types or import specific types if needed.
// The client-side types OutlineItem, Note, Citation are fairly complex.
// Let's use `any` for payloads for now to keep shared/schema.ts simple, or define simplified versions.
// For a robust solution, these detailed payload types should also be in shared.

export interface BaseDocumentChange {
  elementType: DocumentChangeType;
  elementId: string;
  parentId?: string;
}

export interface OutlineItemPayload {
  item?: any; // Simplified: OutlineItem;
  parentItemId?: string;
  index?: number;
  propertyName?: string; // keyof Pick<OutlineItem, 'title' | 'number'>;
  oldValue?: any;
  newValue?: any;
  oldParentItemId?: string;
  newParentItemId?: string;
  newIndex?: number;
}

export interface NotePayload {
  note?: any; // Simplified: Note;
  propertyName?: string; // keyof Pick<Note, 'title' | 'content' | 'linkedOutlineId'>;
  oldValue?: any;
  newValue?: any;
}

export interface WritingSectionPayload {
  newContent?: string;
  oldContent?: string;
}

export interface CitationPayload {
  citation?: any; // Simplified: Citation;
  citationId?: string;
}

export interface DocumentChange extends BaseDocumentChange {
  // Payloads are simplified here. Client-side can have more specific types for its own use.
  // The server will rely on elementType and the structure of the payload.
  payload?: OutlineItemPayload | NotePayload | WritingSectionPayload | CitationPayload | { [key: string]: any };
}

// Example specific types (server might not need them this granularly if payload is flexible)
export interface OutlineItemAddedChange extends DocumentChange {
  elementType: DocumentChangeType.OUTLINE_ITEM_ADDED;
  payload: OutlineItemPayload;
}
export interface OutlineItemRemovedChange extends DocumentChange {
  elementType: DocumentChangeType.OUTLINE_ITEM_REMOVED;
  payload?: OutlineItemPayload; // parentItemId might be here
}
export interface OutlineItemModifiedChange extends DocumentChange {
  elementType: DocumentChangeType.OUTLINE_ITEM_MODIFIED;
  payload: OutlineItemPayload & { propertyName: string; oldValue: any; newValue: any; };
}
// ... and so on for all specific change types defined on client.
// For simplicity in shared/schema.ts, often just DocumentChange with a flexible payload is enough,
// and the client can have the more specific types for its own intellisense and type checking when creating changes.
// The server logic will then cast/interpret `payload` based on `elementType`.

// Define Presence Payloads (these were duplicated, ensure they are defined once)
export interface UserJoinedPayload {
  user: UserPresenceInfo;
}

export interface UserLeftPayload {
  userId: number;
}

export interface PresenceListPayload {
  users: UserPresenceInfo[];
}

// Original DocumentChange payload union:
// payload?: OutlineItemPayload | NotePayload | WritingSectionPayload | CitationPayload | { [key: string]: any };

// Re-define DocumentChange to include new presence payloads in its payload union type
// This replaces the previous DocumentChange definition.
export interface DocumentChange extends BaseDocumentChange {
  payload?: OutlineItemPayload | NotePayload | WritingSectionPayload | CitationPayload
          | UserJoinedPayload | UserLeftPayload | PresenceListPayload
          | { [key: string]: any };
}


// Specific interfaces for presence changes, ensuring they use the updated DocumentChange structure
// and that their elementType matches the enum.
export interface UserJoinedChange extends BaseDocumentChange { // Extend BaseDocumentChange
  elementType: DocumentChangeType.USER_JOINED;
  payload: UserJoinedPayload;
}

export interface UserLeftChange extends BaseDocumentChange { // Extend BaseDocumentChange
  elementType: DocumentChangeType.USER_LEFT;
  payload: UserLeftPayload;
}

export interface PresenceListChange extends BaseDocumentChange { // Extend BaseDocumentChange
  elementType: DocumentChangeType.PRESENCE_LIST;
  payload: PresenceListPayload;
  elementId: string; // e.g., documentId, or could be made optional if not always relevant
}


// The client (`client/lib/types.ts`) should now re-export this richer `DocumentChange` (and `DocumentChangeType`)
// from `shared/schema.ts`.
// The specific granular change types (OutlineItemAddedChange, etc.) if defined in client/lib/types.ts
// should also conform to this new DocumentChange structure by extending BaseDocumentChange and having the correct payload type.
// For a fully shared system, all those specific change types would also move to shared/schema.ts.
// For now, focusing on making DocumentChange in shared/schema.ts capable of carrying presence payloads.

// Update DocumentChange to include these new specific change types with their payloads
// We'll need to define specific interfaces for each presence event type that extends BaseDocumentChange
// and then add them to the DocumentChange union.

export interface UserJoinedChange extends BaseDocumentChange {
  elementType: DocumentChangeType.USER_JOINED;
  payload: UserJoinedPayload;
}

export interface UserLeftChange extends BaseDocumentChange {
  elementType: DocumentChangeType.USER_LEFT;
  payload: UserLeftPayload;
}

export interface PresenceListChange extends BaseDocumentChange {
  elementType: DocumentChangeType.PRESENCE_LIST;
  payload: PresenceListPayload;
  // elementId and parentId might not be strictly necessary for PRESENCE_LIST,
  // but BaseDocumentChange requires elementId. We can make it optional or use a convention.
  // For now, let's assume elementId could be the documentId for context, or it could be optional.
  elementId: string; // Could be documentId or a conventional value
}


// Extend the main DocumentChange union type
// This requires all previous specific change types (OutlineItemAddedChange etc.) to be defined here too.
// For now, I will just show how to add the new ones.
// A more complete refactor would ensure all constituents of DocumentChange are defined here or imported.

// Placeholder for existing specific change types for brevity in this diff
export interface OutlineItemAddedChange extends DocumentChange { elementType: DocumentChangeType.OUTLINE_ITEM_ADDED; payload: OutlineItemPayload;}
export interface OutlineItemRemovedChange extends DocumentChange { elementType: DocumentChangeType.OUTLINE_ITEM_REMOVED; payload?: OutlineItemPayload;}
export interface OutlineItemModifiedChange extends DocumentChange { elementType: DocumentChangeType.OUTLINE_ITEM_MODIFIED; payload: OutlineItemPayload & { propertyName: string; oldValue: any; newValue: any; };}
export interface OutlineItemMovedChange extends DocumentChange { elementType: DocumentChangeType.OUTLINE_ITEM_MOVED; payload: OutlineItemPayload & { oldParentItemId?: string; newParentItemId?: string; newIndex: number; };}
export interface NoteAddedChange extends DocumentChange { elementType: DocumentChangeType.NOTE_ADDED; payload: NotePayload & {note: any};}
export interface NoteRemovedChange extends DocumentChange { elementType: DocumentChangeType.NOTE_REMOVED; }
export interface NoteModifiedChange extends DocumentChange { elementType: DocumentChangeType.NOTE_MODIFIED; payload: NotePayload & { propertyName: string; oldValue: any; newValue: any; };}
export interface WritingSectionModifiedChange extends DocumentChange { elementType: DocumentChangeType.WRITING_SECTION_MODIFIED; payload: WritingSectionPayload;}
export interface CitationAddedChange extends DocumentChange { elementType: DocumentChangeType.CITATION_ADDED; payload: CitationPayload & {citation: any};}
export interface CitationRemovedChange extends DocumentChange { elementType: DocumentChangeType.CITATION_REMOVED; payload: CitationPayload & {citationId: string};}
export interface CitationModifiedChange extends DocumentChange { elementType: DocumentChangeType.CITATION_MODIFIED; payload: CitationPayload & {citation: any};}


// New DocumentChange union type including presence events
export type ExtendedDocumentChange =
  | OutlineItemAddedChange
  | OutlineItemRemovedChange
  | OutlineItemModifiedChange
  | OutlineItemMovedChange // Assuming this was defined or will be
  | NoteAddedChange
  | NoteRemovedChange
  | NoteModifiedChange
  | WritingSectionModifiedChange
  | CitationAddedChange
  | CitationRemovedChange
  | CitationModifiedChange
  | UserJoinedChange
  | UserLeftChange
  | PresenceListChange
  | ChatMessageSentChange
  | NewChatMessageChange;

// It might be cleaner to have a top-level WebSocket message type
// export type WebSocketMessage =
//   | { type: 'documentEvent', payload: ExtendedDocumentChange }
//   | { type: 'anotherTypeOfEvent', payload: SomeOtherPayload };
// For now, we'll extend DocumentChange. The server and client will need to handle this.
// The existing `DocumentChange` interface with a flexible payload might already cover this
// if the server casts `change.payload` based on `elementType`.
// Let's refine the existing `DocumentChange` to be more accommodating or ensure specific types are used.

// The `DocumentChange` defined earlier was:
// export interface DocumentChange extends BaseDocumentChange {
//   payload?: OutlineItemPayload | NotePayload | WritingSectionPayload | CitationPayload | { [key: string]: any };
// }
// We need to ensure this union can hold the new presence payloads.
// Let's redefine it to be more explicit with the new types.

// Redefining DocumentChange to include all specific types explicitly
// This makes the shared type definition more robust.
export type DocumentChangeUnion = // Renaming to avoid conflict with the previous simpler DocumentChange
  | OutlineItemAddedChange
  | OutlineItemRemovedChange
  | OutlineItemModifiedChange
  | OutlineItemMovedChange
  | NoteAddedChange
  | NoteRemovedChange
  | NoteModifiedChange
  | WritingSectionModifiedChange
  | CitationAddedChange
  | CitationRemovedChange
  | CitationModifiedChange
  | UserJoinedChange
  | UserLeftChange
  | PresenceListChange;

// The server-side `applyDocumentChanges` and client-side `onmessage` handler
// will need to be updated to use `DocumentChangeUnion` as the type for the `changes` array elements.
// The `DocumentChange` interface with the generic payload is still useful for the server if it doesn't need
// to discriminate the exact payload type at the top level of `applyDocumentChanges` before the switch.
// However, for type safety, using the union is better.
// Let's assume the existing `DocumentChange` will be replaced by this union, or the server will handle `any` payload.
// For the purpose of this step, defining the specific interfaces and adding to enum is key.
// The server will use the `elementType` to determine how to interpret `payload`.

// Chat Payloads
export interface ChatMessageSentPayload {
  messageText: string;
}

// NewChatMessagePayload is essentially DocumentChatMessage, but let's define it for clarity if needed,
// or directly use DocumentChatMessage for the payload. For NEW_CHAT_MESSAGE, the payload will be the full message.
export type NewChatMessagePayload = DocumentChatMessage;

// Share Tokens table
export const shareTokens = pgTable("share_tokens", {
  id: serial("id").primaryKey(),
  token: text("token").notNull().unique(),
  documentId: text("document_id").notNull().references(() => documents.id, { onDelete: 'cascade' }),
  permissionLevel: documentPermissionEnum("permission_level").notNull(), // Re-use existing enum
  creatorUserId: integer("creator_user_id").references(() => users.id, { onDelete: 'set null' }), // Creator, null if creator deleted
  createdAt: timestamp("created_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at"), // Nullable, for tokens that don't expire or manual expiry management
});
export type ShareToken = typeof shareTokens.$inferSelect;
export const insertShareTokenSchema = createInsertSchema(shareTokens);
export type InsertShareToken = z.infer<typeof insertShareTokenSchema>;

// Document Access Requests table
export const documentAccessRequests = pgTable("document_access_requests", {
  id: serial("id").primaryKey(),
  documentId: text("document_id").notNull().references(() => documents.id, { onDelete: 'cascade' }),
  requestingUserId: integer("requesting_user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  ownerUserId: integer("owner_user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  status: text("status", { enum: ["pending", "approved", "denied"] }).notNull().default("pending"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
export type DocumentAccessRequest = typeof documentAccessRequests.$inferSelect;
export const insertDocumentAccessRequestSchema = createInsertSchema(documentAccessRequests);
export type InsertDocumentAccessRequest = z.infer<typeof insertDocumentAccessRequestSchema>;

// Specific interfaces for chat changes
export interface ChatMessageSentChange extends BaseDocumentChange {
  elementType: DocumentChangeType.CHAT_MESSAGE_SENT;
  payload: ChatMessageSentPayload;
}

export interface NewChatMessageChange extends BaseDocumentChange {
  elementType: DocumentChangeType.NEW_CHAT_MESSAGE;
  payload: NewChatMessagePayload; // This will be the full DocumentChatMessage object
}

// Inline Comments table
export const inlineComments = pgTable("inline_comments", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  documentId: text("document_id").notNull().references(() => documents.id, { onDelete: 'cascade' }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  username: text("username").notNull(),
  text: text("text").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  selection: jsonb("selection").notNull(),
});
export type InlineComment = typeof inlineComments.$inferSelect;

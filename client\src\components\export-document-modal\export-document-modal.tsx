import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>Content, 
  Di<PERSON>Header, 
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { DocumentExportMetadata, Signer } from '@/lib/types';
import { Loader2 } from 'lucide-react';
import { FormatPreview } from '@/components/format-preview';

// Define DocumentFormat here if not imported from a shared location
export type DocumentFormat = 'apa-pro' | 'apa-student' | 'mla-cover' | 'mla' | 'turabian' | 'turabian-dissertation';

interface ExportDocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (format: DocumentFormat, metadata: DocumentExportMetadata) => void; // metadata is now DocumentExportMetadata
  documentId?: number; // Keep if used for anything, else remove
  currentDocumentFormat: DocumentFormat;
  currentMetadata: DocumentExportMetadata;
  isSaving?: boolean;
}

// Remove: export type DocumentMetadata = DocumentExportMetadata; (if not used elsewhere)

export function ExportDocumentModal({ 
  isOpen, 
  onClose, 
  onExport, 
  // documentTitle, // Removed
  documentId, // Keep if used, e.g. for a fetch specific to export, otherwise remove
  currentDocumentFormat,
  currentMetadata,
  isSaving = false
}: ExportDocumentModalProps) {
  // currentTab can be 'settings' or 'preview'
  const [currentTab, setCurrentTab] = useState<'settings' | 'preview'>('settings');
  const [selectedFormat, setSelectedFormat] = useState<DocumentFormat>(
    currentDocumentFormat || 'apa-student' // Initialize with current document's format
  );

  // Effect to update selectedFormat if currentDocumentFormat prop changes while modal is open
  useEffect(() => {
    setSelectedFormat(currentDocumentFormat || 'apa-student');
  }, [currentDocumentFormat]);

  // REMOVE: getDefaultMetadata function
  // REMOVE: useState for internal metadata
  // REMOVE: useEffect for initialMetadata / initialFormat

  // REMOVE: handleInputChange function
  // REMOVE: handleSignerChange function
  
  const handleFormatSelect = (format: DocumentFormat) => {
    setSelectedFormat(format);
  };

  const getFormattedAuthorName = () => {
    if (!currentMetadata) return 'Author Name'; // Guard
    const firstName = currentMetadata.firstName || '';
    const middleName = currentMetadata.middleName || '';
    const lastName = currentMetadata.lastName || '';
    if (!firstName && !lastName) return 'Author Name';
    return `${firstName}${middleName ? ` ${middleName} ` : ' '}${lastName}`;
  };

  const handleExportClick = () => {
    if (!currentMetadata) {
        console.error("Cannot export: currentMetadata is missing.");
        onClose(); // Close modal to prevent further issues
        return;
    }
    onExport(selectedFormat, currentMetadata); // Use selectedFormat and props.currentMetadata
    onClose();
  };

  if (!currentMetadata) { // If essential data is missing, don't render the modal content
    return (
        <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Export Document</DialogTitle>
                </DialogHeader>
                <p className="p-4 text-center">Metadata not available. Cannot prepare export.</p>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>Close</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Export Document</DialogTitle>
        </DialogHeader>
        
        <Tabs value={currentTab} onValueChange={(value) => setCurrentTab(value as any)}>
          <TabsList className="grid grid-cols-2"> {/* Changed from grid-cols-3 */}
            <TabsTrigger value="settings">Format</TabsTrigger>
            {/* REMOVED: Metadata Tab Trigger */}
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
          
          {/* Format Selection Tab (settings) - Content remains similar */}
          <TabsContent value="settings" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormatPreview
                format="apa-pro"
                selected={selectedFormat === 'apa-pro'}
                onClick={() => handleFormatSelect('apa-pro')}
                title="APA 7th Edition"
                subtitle="Professional Paper"
                pages={[
                  // Cover page
                  <div key="cover" className="h-full flex flex-col">
                    <div className="text-[8px] text-center mb-2">Running head: SHORT TITLE</div>
                    <div className="text-[8px] text-center font-bold mb-2">Full Title of the Paper</div>
                    <div className="text-[6px] text-center mb-2">Author Name</div>
                    <div className="text-[6px] text-center mb-2">Institutional Affiliation</div>
                    <div className="text-[6px] text-center mb-2">Course</div>
                    <div className="text-[6px] text-center mb-2">Instructor</div>
                    <div className="text-[6px] text-center mb-2">Due Date</div>
                  </div>,
                  // Abstract page
                  <div key="abstract" className="h-full flex flex-col">
                    <div className="text-[8px] text-center mb-2">SHORT TITLE</div>
                    <div className="text-[8px] text-center font-bold mb-4">Abstract</div>
                    <div className="text-[6px] mb-4">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed et sem eget magna sodales imperdiet.
                    </div>
                    <div className="text-[8px] text-center font-bold mb-2">Keywords</div>
                    <div className="text-[6px]">
                      keyword1, keyword2, keyword3, keyword4
                    </div>
                  </div>,
                  // Content page
                  <div key="content" className="h-full flex flex-col">
                    <div className="text-[8px] text-center mb-2">SHORT TITLE</div>
                    <div className="text-[8px] text-center font-bold mb-4">Full Title of the Paper</div>
                    <div className="text-[6px] mb-2">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed et sem eget magna sodales imperdiet.  
                    </div>
                  </div>
                ]}
              />
              
              <FormatPreview
                format="apa-student"
                selected={selectedFormat === 'apa-student'}
                onClick={() => handleFormatSelect('apa-student')}
                title="APA 7th Edition"
                subtitle="Student Paper"
                pages={[
                  // Cover page
                  <div key="cover" className="h-full flex flex-col">
                    <div className="text-[8px] text-center font-bold mb-2">Full Title of the Paper</div>
                    <div className="text-[6px] text-center mb-2">Author Name</div>
                    <div className="text-[6px] text-center mb-2">Institutional Affiliation</div>
                    <div className="text-[6px] text-center mb-2">Course</div>
                    <div className="text-[6px] text-center mb-2">Instructor</div>
                    <div className="text-[6px] text-center mb-2">Due Date</div>
                  </div>,
                  // Content page
                  <div key="content" className="h-full flex flex-col">
                    <div className="text-[8px] text-center font-bold mb-4">Full Title of the Paper</div>
                    <div className="text-[6px] mb-2">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed et sem eget magna sodales imperdiet.
                    </div>
                  </div>
                ]}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <FormatPreview
                format="mla-cover"
                selected={selectedFormat === 'mla-cover'}
                onClick={() => handleFormatSelect('mla-cover')}
                title="MLA 9th Edition"
                subtitle="With Cover Page"
                pages={[
                  // Cover page
                  <div key="cover" className="h-full flex flex-col justify-center items-center">
                    <div className="text-[8px] text-center font-bold mb-auto mt-10">Full Title of the Paper</div>
                    <div className="text-[6px] text-center mb-2">Author Name</div>
                    <div className="text-[6px] text-center mb-2">Instructor</div>
                    <div className="text-[6px] text-center mb-2">Course</div>
                    <div className="text-[6px] text-center mb-10">Date</div>
                  </div>,
                  // Content page
                  <div key="content" className="h-full flex flex-col">
                    <div className="text-[8px] text-center font-bold mb-2">Full Title of the Paper</div>
                    <div className="text-[6px] mb-1">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed et sem eget magna sodales imperdiet.
                    </div>
                  </div>
                ]}
              />
              
              <FormatPreview
                format="mla"
                selected={selectedFormat === 'mla'}
                onClick={() => handleFormatSelect('mla')}
                title="MLA 9th Edition"
                subtitle="Standard"
                pages={[
                  // First page
                  <div key="first" className="h-full flex flex-col">
                    <div className="text-[6px] mb-2">Author Name</div>
                    <div className="text-[6px] mb-2">Instructor</div>
                    <div className="text-[6px] mb-2">Course</div>
                    <div className="text-[6px] mb-4">Date</div>
                    <div className="text-[8px] text-center font-bold mb-4">Full Title of the Paper</div>
                    <div className="text-[6px] mb-1">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed et sem eget magna sodales imperdiet.
                    </div>
                  </div>,
                  // Content page
                  <div key="content" className="h-full flex flex-col">
                    <div className="text-[6px] mb-1">
                      Nullam vitae eros at risus posuere auctor. Nulla facilisi. Suspendisse potenti.
                    </div>
                  </div>
                ]}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <FormatPreview
                format="turabian"
                selected={selectedFormat === 'turabian'}
                onClick={() => handleFormatSelect('turabian')}
                title="Turabian"
                subtitle="Standard Format"
                pages={[
                  // Title page
                  <div key="title" className="h-full flex flex-col justify-center items-center">
                    <div className="text-[8px] text-center font-bold mb-2">Full Title of the Paper</div>
                    <div className="text-[6px] text-center mb-auto">Subtitle</div>
                    <div className="text-[6px] text-center mb-2">Author Name</div>
                    <div className="text-[6px] text-center mb-2">Course</div>
                    <div className="text-[6px] text-center mb-2">Instructor</div>
                    <div className="text-[6px] text-center mb-2">Institution</div>
                    <div className="text-[6px] text-center mb-2">Date</div>
                  </div>,
                  // Table of Contents
                  <div key="toc" className="h-full flex flex-col">
                    <div className="text-[8px] text-center font-bold mb-4">Table of Contents</div>
                    <div className="text-[6px] flex justify-between mb-1">
                      <span>Introduction</span>
                      <span>1</span>
                    </div>
                    <div className="text-[6px] flex justify-between mb-1">
                      <span>Chapter 1. Title</span>
                      <span>2</span>
                    </div>
                    <div className="text-[6px] flex justify-between mb-1">
                      <span>Chapter 2. Title</span>
                      <span>15</span>
                    </div>
                    <div className="text-[6px] flex justify-between mb-1">
                      <span>Conclusion</span>
                      <span>30</span>
                    </div>
                    <div className="text-[6px] flex justify-between mb-1">
                      <span>Bibliography</span>
                      <span>35</span>
                    </div>
                  </div>,
                  // Content page
                  <div key="content" className="h-full flex flex-col">
                    <div className="text-[8px] text-center font-bold mb-4">Introduction</div>
                    <div className="text-[6px] mb-1">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed et sem eget magna sodales imperdiet.¹
                    </div>
                    <div className="text-[5px] absolute bottom-2 left-2">
                      ¹ Citation details here.
                    </div>
                  </div>
                ]}
              />
              
              <FormatPreview
                format="turabian-dissertation"
                selected={selectedFormat === 'turabian-dissertation'}
                onClick={() => handleFormatSelect('turabian-dissertation')}
                title="Turabian"
                subtitle="Dissertation Format"
                pages={[
                  // Title page
                  <div key="title" className="h-full flex flex-col justify-center items-center">
                    <div className="text-[8px] text-center font-bold mb-2">Full Title of the Dissertation</div>
                    <div className="text-[6px] text-center mb-auto">Subtitle</div>
                    <div className="text-[6px] text-center mb-2">A Dissertation</div>
                    <div className="text-[6px] text-center mb-2">Presented to</div>
                    <div className="text-[6px] text-center mb-2">The Faculty of the Department</div>
                    <div className="text-[6px] text-center mb-2">University Name</div>
                    <div className="text-[6px] text-center mb-2">In Partial Fulfillment</div>
                    <div className="text-[6px] text-center mb-2">of the Requirements for the Degree</div>
                    <div className="text-[6px] text-center mb-2">Doctor of Philosophy</div>
                    <div className="text-[6px] text-center mb-2">by</div>
                    <div className="text-[6px] text-center mb-2">Author Name</div>
                    <div className="text-[6px] text-center mb-2">Month Year</div>
                  </div>,
                  // Approval Sheet
                  <div key="approval" className="h-full flex flex-col">
                    <div className="text-[8px] text-center font-bold mb-4">Approval Sheet</div>
                    <div className="text-[7px] text-center mb-2">This dissertation, entitled</div>
                    <div className="text-[7px] text-center font-bold mb-1">Full Title of the Dissertation</div>
                    <div className="text-[7px] text-center mb-4">presented by Author Name</div>
                    <div className="text-[7px] text-center mb-2">has been accepted towards fulfillment</div>
                    <div className="text-[7px] text-center mb-4">of the requirements for the degree of Doctor of Philosophy</div>
                    <div className="text-[6px] mb-2 border-t pt-1">Committee Chair</div>
                    <div className="text-[6px] mb-2 border-t pt-1">Committee Member</div>
                    <div className="text-[6px] mb-2 border-t pt-1">Committee Member</div>
                  </div>,
                  // Abstract
                  <div key="abstract" className="h-full flex flex-col">
                    <div className="text-[8px] text-center font-bold mb-4">Abstract</div>
                    <div className="text-[6px] mb-1">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed et sem eget magna sodales imperdiet.
                      Aliquam at quam vel magna ultrices placerat vel vel purus. Nullam vitae eros at risus posuere auctor.
                      Nulla facilisi. Suspendisse potenti. Cras at justo in ligula luctus posuere.
                    </div>
                  </div>,
                  // Dedication
                  <div key="dedication" className="h-full flex flex-col justify-center items-center">
                    <div className="text-[8px] text-center font-bold mb-4">Dedication</div>
                    <div className="text-[6px] text-center">
                      To my family and mentors
                    </div>
                  </div>
                ]}
              />
            </div>
            
            <div className="flex justify-end mt-4">
              <Button onClick={() => setCurrentTab('preview')}> {/* Changed text and target tab */}
                Continue to Preview
              </Button>
            </div>
          </TabsContent>
          
          {/* REMOVED: Metadata Tab (layout) */}
          
          {/* Preview Tab - Update to use props.currentMetadata */}
          <TabsContent value="preview" className="space-y-4">
            <ScrollArea className="h-[400px] border border-border rounded-md p-4">
              <div className="w-full mx-auto max-w-[320px]">
                <div className="aspect-[0.77] border border-border bg-card rounded-md overflow-hidden shadow-md mb-4">
                  {/* Example for APA Pro Preview: */}
                  {selectedFormat === 'apa-pro' && (
                    <div className="p-6 h-full flex flex-col">
                      <div className="text-[10px] mb-2">Running head: {currentMetadata.shortTitle || (currentMetadata.mainTitle ? currentMetadata.mainTitle.substring(0, 50) : 'SHORT TITLE')}</div>
                      <div className="flex-grow flex flex-col items-center justify-center">
                        <div className="text-[14px] font-bold mb-2 text-center">{currentMetadata.mainTitle || 'Document Title'}</div>
                        {currentMetadata.subtitle && <div className="text-[12px] mb-4 text-center">{currentMetadata.subtitle}</div>}
                        <div className="text-[12px] mb-1">{getFormattedAuthorName()}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.institution}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.course}: {currentMetadata.courseName}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.instructor}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.submissionDate}</div>
                      </div>
                    </div>
                  )}
                  
                  {selectedFormat === 'apa-student' && (
                    <div className="p-6 h-full flex flex-col">
                      <div className="flex-grow flex flex-col items-center justify-center">
                        <div className="text-[14px] font-bold mb-2 text-center">{currentMetadata.mainTitle}</div>
                        {currentMetadata.subtitle && <div className="text-[12px] mb-4 text-center">{currentMetadata.subtitle}</div>}
                        <div className="text-[12px] mb-1">{getFormattedAuthorName()}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.institution}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.course}: {currentMetadata.courseName}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.instructor}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.submissionDate}</div>
                      </div>
                    </div>
                  )}
                  
                  {selectedFormat === 'mla-cover' && (
                    <div className="p-6 h-full flex flex-col">
                      <div className="flex-grow flex flex-col items-center justify-center">
                        <div className="text-[14px] font-bold mb-2 text-center">{currentMetadata.mainTitle}</div>
                        {currentMetadata.subtitle && <div className="text-[12px] mb-auto text-center">{currentMetadata.subtitle}</div>}
                        <div className="text-[12px] mb-1">{getFormattedAuthorName()}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.instructor}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.course}: {currentMetadata.courseName}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.submissionDate}</div>
                      </div>
                    </div>
                  )}
                  
                  {selectedFormat === 'mla' && (
                    <div className="p-6 h-full flex flex-col">
                      <div>
                        <div className="text-[10px] mb-1">{getFormattedAuthorName()}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.instructor}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.course}</div>
                        <div className="text-[10px] mb-4">{currentMetadata.submissionDate}</div>
                        <div className="text-[12px] font-bold mb-4 text-center">{currentMetadata.mainTitle}</div>
                        <div className="text-[8px] mb-1">
                          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam sollicitudin velit sed blandit sagittis. 
                          Nam ac venenatis turpis, at imperdiet lorem. Duis porttitor metus ac sagittis tincidunt.
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {selectedFormat === 'turabian' && (
                    <div className="p-6 h-full flex flex-col">
                      <div className="flex-grow flex flex-col items-center justify-center">
                        <div className="text-[14px] font-bold mb-2 text-center">{currentMetadata.mainTitle}</div>
                        {currentMetadata.subtitle && <div className="text-[12px] mb-auto text-center">{currentMetadata.subtitle}</div>}
                        <div className="text-[12px] mb-1">{getFormattedAuthorName()}</div>
                        <div className="text-[10px] mb-1">A Paper</div>
                        <div className="text-[10px] mb-1">Submitted to {currentMetadata.instructor}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.course}: {currentMetadata.courseName}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.institution}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.submissionDate}</div>
                      </div>
                    </div>
                  )}
                  
                  {selectedFormat === 'turabian-dissertation' && (
                    <div className="p-6 h-full flex flex-col">
                      <div className="flex-grow flex flex-col items-center justify-center">
                        <div className="text-[14px] font-bold mb-2 text-center">{currentMetadata.mainTitle}</div>
                        {currentMetadata.subtitle && <div className="text-[12px] mb-6 text-center">{currentMetadata.subtitle}</div>}
                        <div className="text-[10px] mb-1">A Dissertation</div>
                        <div className="text-[10px] mb-1">Presented to</div>
                        <div className="text-[10px] mb-1">The Faculty of {currentMetadata.college || 'the Department'}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.institution}</div>
                        <div className="text-[10px] mb-3">In Partial Fulfillment</div>
                        <div className="text-[10px] mb-1">of the Requirements for the Degree</div>
                        <div className="text-[10px] mb-3">{currentMetadata.degreeTitle || 'Doctor of Philosophy'}</div>
                        <div className="text-[10px] mb-1">by</div>
                        <div className="text-[12px] mb-1">{getFormattedAuthorName()}</div>
                        <div className="text-[10px] mb-1">{currentMetadata.submissionDate}</div>
                      </div>
                    </div>
                  )}
                  {/* ... Other format previews updated similarly to use currentMetadata ... */}
                </div>
              </div>
            </ScrollArea>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setCurrentTab('settings')}> {/* Changed target tab */}
                Back to Format
              </Button>
              <Button 
                onClick={handleExportClick}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  'Export Document'
                )}
              </Button>
            </DialogFooter>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
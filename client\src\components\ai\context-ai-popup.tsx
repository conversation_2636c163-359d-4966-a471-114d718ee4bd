import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';


export interface AiSuggestion {
  id: string;
  icon: string; // Remixicon class name
  label: string;
  description?: string;
  isPremium?: boolean;
}

interface ContextAiPopupProps {
  suggestions: AiSuggestion[];
  triggerContent?: React.ReactNode;
  showTriggerIcon?: boolean;
  position?: 'top' | 'bottom' | 'left' | 'right';
  onSuggestionClick: (suggestion: AiSuggestion) => void;
  onShowSubscriptionModal?: () => void;
}

export function ContextAiPopup({
  suggestions,
  triggerContent,
  showTriggerIcon = true,
  position = 'bottom',
  onSuggestionClick,
  onShowSubscriptionModal
}: ContextAiPopupProps) {
  const [isOpen, setIsOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  
  const handleMouseEnter = () => {};
  const handleMouseLeave = () => {};
  

  
  // Handle clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      const target = event.target as Node;
      const clickedOutsidePopover = 
        popoverRef.current && 
        !popoverRef.current.contains(target) &&
        buttonRef.current && 
        !buttonRef.current.contains(target);
        
      if (clickedOutsidePopover) {
        setIsOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Handle suggestion click
  const handleSuggestionClick = (suggestion: AiSuggestion) => {
    if (suggestion.isPremium && onShowSubscriptionModal) {
      onShowSubscriptionModal();
    } else {
      onSuggestionClick(suggestion);
    }
    setIsOpen(false);
  };
  
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          ref={buttonRef}
          className="h-7 p-1 hover:bg-primary/5 rounded-full"
          onClick={() => setIsOpen(true)}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {triggerContent ? (
            triggerContent
          ) : (
            showTriggerIcon && (
              <i className="ri-robot-line text-primary"></i>
            )
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        ref={popoverRef}
        className="w-72 p-2 shadow-lg border border-[hsl(var(--icon-purple-hsl))]/20 z-50 bg-gradient-to-br from-background to-[hsl(var(--icon-purple-hsl))]/5" 
        side={position}
        sideOffset={5}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className="flex flex-col gap-1.5">
          <div className="flex items-center justify-between mb-1 px-1.5">
            <h3 className="text-sm font-medium flex items-center gap-1">
              <i className="ri-robot-line text-primary"></i>
              <span>AI Assistant</span>
            </h3>
            <span className="text-xs text-muted-foreground">
              Select an option
            </span>
          </div>
          
          {suggestions.map((suggestion) => (
            <Button
              key={suggestion.id}
              variant="ghost"
              className="flex items-center justify-start gap-2 text-left py-2 px-3 h-auto hover:bg-muted"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <i className={`${suggestion.icon} text-base ${suggestion.isPremium ? 'text-icon-purple' : 'text-primary'}`}></i>
              <div className="flex flex-col items-start">
                <span className="text-sm font-medium flex items-center gap-1">
                  {suggestion.label}
                  {suggestion.isPremium && (
                    <span className="text-[0.65rem] px-1.5 py-0.25 bg-[hsl(var(--icon-purple-hsl))] text-foreground rounded-full font-medium ml-1">
                      Pro
                    </span>
                  )}
                </span>
                {suggestion.description && (
                  <span className="text-xs text-muted-foreground">
                    {suggestion.description}
                  </span>
                )}
              </div>
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
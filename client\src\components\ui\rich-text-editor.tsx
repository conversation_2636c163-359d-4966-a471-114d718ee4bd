
import React, { useRef, useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Citation } from "@/lib/types";

// Helper function to position cursor more precisely at a specific point in the editor
function positionCursorFromPoint(
  x: number,
  y: number,
  editorElement: HTMLElement,
): Range | null {
  let range: Range | null = null;

  try {
    // First try using the native caretRangeFromPoint method if available
    // This gives the most precise cursor positioning at exact coordinates
    const docAny = document as any;
    if (typeof docAny.caretRangeFromPoint === "function") {
      range = docAny.caretRangeFromPoint(x, y);

      // Verify the range is inside our editor
      if (range && !editorElement.contains(range.commonAncestorContainer)) {
        console.log(
          "Range from caretRangeFromPoint is outside editor, trying alternative",
        );
        range = null;
      } else if (range) {
        console.log("Successfully positioned cursor using caretRangeFromPoint");
        return range;
      }
    }

    // Second approach: Try to find the deepest text node at the position
    const elements = document.elementsFromPoint(x, y);

    // First look for a text node at or near that position
    for (const element of elements) {
      if (editorElement.contains(element)) {
        // Function to find the deepest text node in an element
        const findDeepestTextNode = (node: Node): Node => {
          if (node.nodeType === Node.TEXT_NODE) {
            return node;
          }

          // If it's an element with children, recurse into its children
          if (node.hasChildNodes()) {
            for (const child of Array.from(node.childNodes)) {
              const textNode = findDeepestTextNode(child);
              if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                return textNode;
              }
            }
          }

          // No text node found, return the node itself
          return node;
        };

        // Try to find a text node within this element
        const textNode = findDeepestTextNode(element);

        // Create a range at this text node
        range = document.createRange();

        if (textNode.nodeType === Node.TEXT_NODE) {
          // If it's a text node, try to position within the text
          const textLength = textNode.textContent?.length || 0;
          // Use the middle of the text as a reasonable position
          const offset = Math.floor(textLength / 2);
          range.setStart(textNode, offset);
        } else {
          // If not a text node, position at the end of the node
          range.selectNodeContents(textNode);
          range.collapse(false); // Position at end
        }

        console.log("Positioned cursor at text node");
        return range;
      }
    }

    // Third approach: Just use the first element we found inside the editor
    for (const element of elements) {
      if (editorElement.contains(element)) {
        range = document.createRange();
        range.selectNodeContents(element);
        range.collapse(false); // position at end
        console.log("Positioned cursor at element end");
        return range;
      }
    }

    // Last resort - place cursor at the end of the editor
    range = document.createRange();
    range.selectNodeContents(editorElement);
    range.collapse(false); // position at end
    console.log("Positioned cursor at editor end");
  } catch (e) {
    console.error("Error positioning cursor:", e);

    // Fallback to end of editor
    try {
      range = document.createRange();
      range.selectNodeContents(editorElement);
      range.collapse(false);
    } catch (err) {
      console.error("Fallback positioning failed:", err);
      return null;
    }
  }

  return range;
}

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  minHeight?: string;
  maxHeight?: string;
  onBlur?: () => void;
  placeholder?: string;
  className?: string;
}

export function RichTextEditor({
  value,
  onChange,
  minHeight = "300px",
  maxHeight,
  onBlur,
  placeholder,
  className,
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [internalValue, setInternalValue] = useState(value);
  const isUserTyping = useRef(false);
  const [isDraggingOver, setIsDraggingOver] = useState(false);

  // Set initial content on mount
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = value || "";
    }
  }, []);

  // Sync editor content with props when changed externally
  useEffect(() => {
    if (!isUserTyping.current && editorRef.current && value !== internalValue) {
      editorRef.current.innerHTML = value || "";
      setInternalValue(value);
    }
  }, [value, internalValue]);

  // Simple content change handler that doesn't mess with selection
  const handleInput = () => {
    if (editorRef.current) {
      isUserTyping.current = true;
      const newContent = editorRef.current.innerHTML;
      setInternalValue(newContent);
      onChange(newContent);

      // Allow external updates after a short delay
      setTimeout(() => {
        isUserTyping.current = false;
      }, 100);
    }
  };

  // Handle common commands
  const execCommand = (command: string, value: string = "") => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setInternalValue(newContent);
      onChange(newContent);
    }
  };

  // Handle drag over event
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // Check if the dragged data is a citation
    if (e.dataTransfer.types.includes("application/json")) {
      setIsDraggingOver(true);
    }
  };

  // Handle drag leave event
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);
  };

  // Handle drop event
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);

    try {
      // First focus the editor to ensure it's ready to receive content
      if (editorRef.current) {
        editorRef.current.focus();
      }

      // Check if the dropped data is a citation
      if (e.dataTransfer.types.includes("application/json")) {
        const citationData = e.dataTransfer.getData("application/json");
        const citation: Citation = JSON.parse(citationData);

        // Validate the citation object first
        if (!citation || typeof citation !== "object") {
          throw new Error("Invalid citation data format");
        }

        // Check if citation data has a specific format (footnote or endnote)
        const hasFootnote =
          typeof citation.isFootnote === "boolean"
            ? citation.isFootnote
            : false;

        let formattedCitation = "";

        // Get the editor element
        const editorElement = editorRef.current;
        if (!editorElement) {
          console.error("Editor element not found");
          return;
        }

        // Try to get current selection or create one at the drop point
        const selection = window.getSelection();
        let range: Range | null = null;

        try {
          // Check if we have a valid selection range
          if (selection && selection.rangeCount > 0) {
            range = selection.getRangeAt(0);

            // Verify the range is within our editor
            if (!editorElement.contains(range.commonAncestorContainer)) {
              throw new Error("Selection is outside editor");
            }
          } else {
            throw new Error("No selection range");
          }
        } catch (error) {
          console.log(
            "Selection not found, setting cursor position at drop point",
          );

          // Get coordinates from the original drag event
          const dropX = e.clientX;
          const dropY = e.clientY;

          // Create a new range at the drop position
          range = positionCursorFromPoint(dropX, dropY, editorElement);

          if (range) {
            // Set the selection to this range
            selection?.removeAllRanges();
            selection?.addRange(range);
          } else {
            // Fall back to end of editor
            range = document.createRange();
            range.selectNodeContents(editorElement);
            range.collapse(false); // collapse to end
            selection?.removeAllRanges();
            selection?.addRange(range);
          }
        }

        if (!range) {
          console.error("Could not establish a valid insertion point");
          return;
        }

        // Format and insert citation based on type
        if (hasFootnote || (citation.marker && !citation.year)) {
          // Footnote: Create an actual HTML superscript element
          const supElement = document.createElement("sup");
          supElement.textContent = citation.marker;

          // Insert the superscript element at the cursor position
          range.deleteContents();
          range.insertNode(supElement);

          // Position cursor after the inserted element
          range.setStartAfter(supElement);
          range.setEndAfter(supElement);
          selection?.removeAllRanges();
          selection?.addRange(range);

          console.log("Inserted footnote citation with DOM element");
        } else {
          // Endnote: Create a text node for the parenthetical citation
          // Extract author or use marker if no clear author available
          let authorText = "Citation";

          if (citation.authors && citation.authors.length > 0) {
            // Use last name of first author if available
            authorText =
              citation.authors[0].split(" ").pop() || citation.marker;
          } else if (
            citation.reference &&
            typeof citation.reference === "string"
          ) {
            // Try to extract author from reference
            const parts = citation.reference.split(".");
            authorText = parts[0] || citation.marker || "Citation";
          } else if (citation.marker) {
            // Use marker as fallback
            authorText = citation.marker;
          }

          // Get year from citation or use current year
          const yearText = citation.year || new Date().getFullYear().toString();

          // Create text node with parenthetical citation
          const textNode = document.createTextNode(
            `(${authorText}, ${yearText})`,
          );

          // Insert the citation at the cursor position
          range.deleteContents();
          range.insertNode(textNode);

          // Position cursor after the inserted citation
          range.setStartAfter(textNode);
          range.setEndAfter(textNode);
          selection?.removeAllRanges();
          selection?.addRange(range);

          console.log("Inserted endnote citation with DOM node");
        }

        // Update the editor content after insertion
        const newContent = editorElement.innerHTML;
        setInternalValue(newContent);
        onChange(newContent);

        // Ensure editor has focus after insertion
        editorElement.focus();

        // Add a log to help debug the citations detection
        console.log(
          "Updated editor content:",
          newContent.substring(0, 100) + "...",
        );
      }
    } catch (error) {
      console.error("Error handling citation drop:", error);
    }
  };

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-background text-foreground relative",
        className,
      )}
    >
      <div className="sticky top-0 left-0 right-0 z-10 flex p-1 gap-1 bg-muted shadow-sm border-b border-border" style={{ backgroundColor: "var(--muted)" }}>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("bold")}
        >
          <i className="ri-bold text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("italic")}
        >
          <i className="ri-italic text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("underline")}
        >
          <i className="ri-underline text-lg" />
        </Button>
        <div className="w-px h-8 bg-gray-300 dark:bg-gray-600 mx-1" />
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("justifyLeft")}
        >
          <i className="ri-align-left text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("justifyCenter")}
        >
          <i className="ri-align-center text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("justifyRight")}
        >
          <i className="ri-align-right text-lg" />
        </Button>
        <div className="w-px h-8 bg-gray-300 dark:bg-gray-600 mx-1" />
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("insertUnorderedList")}
        >
          <i className="ri-list-unordered text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("insertOrderedList")}
        >
          <i className="ri-list-ordered text-lg" />
        </Button>
      </div>

      {/* This parent div will now have a fixed height matching the contentEditable area's fixed height. */}
      {/* It no longer needs to scroll, as the contentEditable div will scroll internally. */}
      <div style={{ height: minHeight }}>
        <div
          ref={editorRef}
          contentEditable
          className={cn(
            "p-4 focus:outline-none prose prose-sm max-w-none border-0 outline-none ring-0 text-foreground", // Removed h-full from here if it was added by mistake in thought process
            isDraggingOver &&
              "bg-[var(--icon-purple)]/5 border-2 border-dashed border-[var(--icon-purple)]/20",
          )}
          style={{
            minHeight: minHeight, // Use the prop value (e.g., "300px")
            height: minHeight,    // Set fixed height
            maxHeight: minHeight, // Enforce max height to be the same
            overflowY: "auto",  // Enable vertical scrolling on contentEditable itself
            border: isDraggingOver ? undefined : "none",
            outline: "none",
            boxShadow: "none",
            backgroundColor: "var(--background)",
          }}
          onInput={handleInput}
          onBlur={onBlur}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          data-placeholder={placeholder}
        />
      </div>
    </div>
  );
}

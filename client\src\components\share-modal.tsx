import React, { useState, useEffect, useCallback } from 'react'; // Added useEffect, useCallback
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; // Added Select
import { Copy, Link2, Share2, Users, UserCog, Trash2, ShieldCheck, ShieldAlert, Loader2 as SpinnerIcon, UserPlus } from 'lucide-react'; // Added UserPlus
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface SharedUser {
  userId: number;
  username: string;
  permissionLevel: 'view' | 'edit';
}

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
  documentTitle: string;
}

export function ShareModal({ isOpen, onClose, documentId, documentTitle }: ShareModalProps) {
  const [activeTab, setActiveTab] = useState<'link' | 'collaborate'>('link');
  // const [allowEditing, setAllowEditing] = useState(false); // Removed: For general link sharing
  const { toast } = useToast();

  const [generatedViewLink, setGeneratedViewLink] = useState<string>('');
  const [generatedEditLink, setGeneratedEditLink] = useState<string>('');
  const [isGeneratingLink, setIsGeneratingLink] = useState<boolean>(false);

  const [currentShares, setCurrentShares] = useState<SharedUser[]>([]);
  const [isLoadingShares, setIsLoadingShares] = useState(false);
  const [errorShares, setErrorShares] = useState<string | null>(null);

  const [newUserUsername, setNewUserUsername] = useState('');
  const [newUserPermission, setNewUserPermission] = useState<'view' | 'edit'>('view');
  const [isSharing, setIsSharing] = useState(false);

  const fetchShares = useCallback(async () => {
    if (!isOpen || !documentId || activeTab !== 'collaborate') return;
    setIsLoadingShares(true);
    setErrorShares(null);
    try {
      const response = await apiRequest('GET', `/api/documents/${documentId}/shares`);
      if (!response.ok) {
        // Try to get error message from response body if possible, else use statusText
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) { /* ignore json parsing error if any */ }
        const message = errorData?.message || response.statusText || `HTTP error ${response.status}`;
        throw new Error(`Failed to fetch shares: ${message}`);
      }
      const data = await response.json();
      setCurrentShares(data as SharedUser[]);
    } catch (err) {
      console.error("Failed to fetch shares:", err);
      setErrorShares("Failed to load sharing information.");
      toast({ title: "Error", description: "Could not load sharing information.", variant: "destructive" });
    } finally {
      setIsLoadingShares(false);
    }
  }, [isOpen, documentId, activeTab, toast]);

  // Fetch current shares when modal opens, documentId, or activeTab changes
  useEffect(() => {
    fetchShares();
  }, [fetchShares]);

  const handleAddCollaborator = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newUserUsername.trim()) {
      toast({ title: "Username required", description: "Please enter a username to share with.", variant: "destructive" });
      return;
    }
    setIsSharing(true);
    try {
      // 1. Find user by username
      const searchResponse = await apiRequest('GET', `/api/users/search?username=${encodeURIComponent(newUserUsername.trim())}`);

      if (!searchResponse.ok) {
        // If API returned an error status (e.g. 400, 500), treat as user not found or server error
        // The current backend returns 200 with null for not found, this handles other server errors.
        let errorData;
        try {
            errorData = await searchResponse.json();
        } catch (e) { /* ignore if response body is not json or empty */ }
        const message = errorData?.message || searchResponse.statusText || `Error searching user (${searchResponse.status})`;
        toast({ title: "Search Error", description: message, variant: "destructive" });
        setIsSharing(false);
        return;
      }

      const targetUser = await searchResponse.json() as { id: number; username: string } | null; // Parse JSON

      if (!targetUser || !targetUser.id) { // This check is now on the parsed object
        toast({ title: "User not found", description: `User "${newUserUsername}" not found.`, variant: "destructive" });
        setIsSharing(false);
        return;
      }

      // 2. Add share
      await apiRequest('POST', `/api/documents/${documentId}/shares`, {
        targetUserId: targetUser.id,
        permissionLevel: newUserPermission,
      });
      toast({ title: "Success", description: `Document shared with ${targetUser.username} (${newUserPermission}).` });
      setNewUserUsername(''); // Reset form
      fetchShares(); // Refresh the list of shares
    } catch (err: any) {
      console.error("Failed to add collaborator:", err);
      const errorMessage = err.response?.data?.message || err.message || "Could not share document.";
      toast({ title: "Sharing Error", description: errorMessage, variant: "destructive" });
    } finally {
      setIsSharing(false);
    }
  };

  const handleGenerateLink = async (permissionLevel: 'view' | 'edit') => {
    setIsGeneratingLink(true);
    setGeneratedViewLink(''); // Clear previous links
    setGeneratedEditLink(''); // Clear previous links
    try {
      const response = await apiRequest('POST', `/api/documents/${documentId}/share-links`, { permissionLevel });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
        throw new Error(errorData.message || `Failed to generate link`);
      }
      const data = await response.json();
      if (permissionLevel === 'view') {
        setGeneratedViewLink(data.shareLink);
      } else {
        setGeneratedEditLink(data.shareLink);
      }
      toast({ title: "Link Generated", description: `${permissionLevel === 'view' ? 'View-only' : 'Editable'} link created successfully.` });
    } catch (err: any) {
      console.error("Failed to generate share link:", err);
      toast({ title: "Error Generating Link", description: err.message || "Could not generate share link.", variant: "destructive" });
    } finally {
      setIsGeneratingLink(false);
    }
  };
  
  const handleCopyLink = (link: string) => {
    if (!link) {
      toast({ title: "Nothing to copy", description: "Please generate a link first.", variant: "destructive" });
      return;
    }
    navigator.clipboard.writeText(link).then(() => {
      toast({
        title: "Link copied",
        description: "Share link copied to clipboard",
      });
    }).catch(err => {
      toast({
        title: "Failed to copy",
        description: "Could not copy link to clipboard",
        variant: "destructive",
      });
      console.error('Failed to copy link:', err);
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share "{documentTitle}"
          </DialogTitle>
          <DialogDescription>
            Choose how you want to share this document with others.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="link" value={activeTab} onValueChange={(value) => setActiveTab(value as 'link' | 'collaborate')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="link" className="flex items-center gap-2">
              <Link2 className="h-4 w-4" />
              Share Link
            </TabsTrigger>
            <TabsTrigger value="collaborate" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Collaboration
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="link" className="space-y-6 py-4">
            <div>
              <p className="text-sm text-muted-foreground mb-2">
                Generate a secure link to share your document. Links grant access to anyone who uses them.
              </p>
              <div className="flex space-x-2 mb-4">
                <Button onClick={() => handleGenerateLink('view')} disabled={isGeneratingLink} className="flex-1">
                  {isGeneratingLink ? <SpinnerIcon className="h-4 w-4 animate-spin mr-2" /> : <ShieldCheck className="h-4 w-4 mr-2" />}
                  Create View-Only Link
                </Button>
                <Button onClick={() => handleGenerateLink('edit')} disabled={isGeneratingLink} className="flex-1">
                  {isGeneratingLink ? <SpinnerIcon className="h-4 w-4 animate-spin mr-2" /> : <ShieldAlert className="h-4 w-4 mr-2" />}
                  Create Edit Link
                </Button>
              </div>
            </div>

            {generatedViewLink && (
              <div className="space-y-2">
                <Label htmlFor="view-link" className="text-sm font-medium">View-Only Link</Label>
                <div className="flex gap-2">
                  <Input
                    id="view-link"
                    value={generatedViewLink}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handleCopyLink(generatedViewLink)}
                    title="Copy view-only link"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {generatedEditLink && (
              <div className="space-y-2">
                <Label htmlFor="edit-link" className="text-sm font-medium">Editable Link</Label>
                <div className="flex gap-2">
                  <Input
                    id="edit-link"
                    value={generatedEditLink}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handleCopyLink(generatedEditLink)}
                    title="Copy editable link"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="collaborate" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>Manage Collaborators</Label>
              <p className="text-xs text-muted-foreground">
                Invite specific people to view or edit this document. Users you invite will see this document on their dashboard.
              </p>
            </div>

            <form onSubmit={handleAddCollaborator} className="space-y-3 border-b pb-4 mb-4">
              <div className="flex items-end gap-2">
                <div className="flex-grow space-y-1">
                  <Label htmlFor="username-input" className="text-xs">Username or Email</Label>
                  <Input
                    id="username-input"
                    placeholder="Enter username..."
                    value={newUserUsername}
                    onChange={(e) => setNewUserUsername(e.target.value)}
                    disabled={isSharing}
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="permission-select" className="text-xs">Permission</Label>
                  <Select
                    value={newUserPermission}
                    onValueChange={(value: 'view' | 'edit') => setNewUserPermission(value)}
                    disabled={isSharing}
                  >
                    <SelectTrigger id="permission-select" className="w-[100px]">
                      <SelectValue placeholder="Permission" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="view">Can view</SelectItem>
                      <SelectItem value="edit">Can edit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button type="submit" disabled={isSharing || !newUserUsername.trim()} className="h-9">
                  {isSharing ? <SpinnerIcon className="h-4 w-4 animate-spin" /> : <UserPlus className="h-4 w-4 mr-1" />}
                  Add
                </Button>
              </div>
            </form>

            <div className="space-y-1">
              <Label className="text-sm">Shared with</Label>
            </div>
            <div className="mt-1 space-y-3 max-h-48 overflow-y-auto pr-2"> {/* Adjusted max-h */}
              {isLoadingShares && (
                <div className="flex items-center justify-center py-4">
                  <SpinnerIcon className="h-6 w-6 animate-spin text-muted-foreground" />
                  <p className="ml-2 text-sm text-muted-foreground">Loading collaborators...</p>
                </div>
              )}
              {errorShares && (
                <p className="text-sm text-destructive text-center py-4">{errorShares}</p>
              )}
              {!isLoadingShares && !errorShares && (!currentShares || currentShares.length === 0) && (
                <p className="text-sm text-muted-foreground text-center py-4">Not shared with anyone yet.</p>
              )}
              {!isLoadingShares && !errorShares && Array.isArray(currentShares) && currentShares.map(share => (
                <div key={share.userId} className="flex items-center justify-between p-2 border rounded-md bg-background hover:bg-muted/50 transition-colors">
                  <div className="flex items-center gap-2">
                    <UserCog className="h-5 w-5 text-muted-foreground" /> {/* Placeholder icon */}
                    <div>
                      <p className="text-sm font-medium">{share.username}</p>
                      <p className="text-xs text-muted-foreground">
                        {share.permissionLevel === 'edit' ? 'Can edit' : 'Can view'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Select
                      value={share.permissionLevel}
                      onValueChange={async (newLevel: 'view' | 'edit') => {
                        // Optimistically update UI? Or wait for API call?
                        // For now, let's make API call then refresh.
                        setIsSharing(true); // Reuse isSharing for general loading state on this tab
                        try {
                          await apiRequest('PUT', `/api/documents/${documentId}/shares/${share.userId}`, {
                            permissionLevel: newLevel,
                          });
                          toast({ title: "Permission Updated", description: `Permission for ${share.username} changed to ${newLevel}.` });
                          fetchShares(); // Refresh list
                        } catch (err: any) {
                          console.error("Failed to update permission:", err);
                          const errorMessage = err.response?.data?.message || err.message || "Could not update permission.";
                          toast({ title: "Update Error", description: errorMessage, variant: "destructive" });
                        } finally {
                          setIsSharing(false);
                        }
                      }}
                      disabled={isSharing} // Disable while any share operation is in progress
                    >
                      <SelectTrigger className="w-[100px] h-7 text-xs">
                        <SelectValue placeholder="Permission" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="view" className="text-xs">Can view</SelectItem>
                        <SelectItem value="edit" className="text-xs">Can edit</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 text-destructive hover:text-destructive/80"
                      title={`Remove ${share.username}`}
                      onClick={async () => {
                        setIsSharing(true); // Reuse for loading state
                        try {
                          await apiRequest('DELETE', `/api/documents/${documentId}/shares/${share.userId}`);
                          toast({ title: "Share Removed", description: `${share.username} no longer has access.` });
                          fetchShares(); // Refresh list
                        } catch (err: any) {
                          console.error("Failed to remove share:", err);
                          const errorMessage = err.response?.data?.message || err.message || "Could not remove share.";
                          toast({ title: "Removal Error", description: errorMessage, variant: "destructive" });
                        } finally {
                          setIsSharing(false);
                        }
                      }}
                      disabled={isSharing}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            
            {/* The old "Collaboration Link" section has been removed as it relied on ?mode= links. */}
            {/* If a persistent "anyone with link can edit" type of link is desired with the new token system, a new UI/logic would be needed here. */}
            {/* For now, this section is removed to align with deprecating ?mode= links. */}
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {/* The generic "Copy Link" button in footer is removed to avoid confusion. Users should use copy buttons next to generated links. */}
          {/* If a primary link copy action is desired, it needs to be decided which link to copy (e.g., the most recently generated one). */}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
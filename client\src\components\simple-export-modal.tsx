import React, { useState } from 'react';

interface SimpleExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (format: string, metadata: any) => void;
  documentTitle: string;
}

export function SimpleExportModal({ isOpen, onClose, onExport, documentTitle }: SimpleExportModalProps) {
  const [selectedFormat, setSelectedFormat] = useState('apa-pro');
  
  if (!isOpen) return null;
  
  const formatOptions = [
    { id: 'apa-pro', label: 'APA Professional' },
    { id: 'apa-student', label: 'APA Student' },
    { id: 'mla-cover', label: 'MLA + Cover Page' },
    { id: 'mla', label: 'MLA' },
    { id: 'turabian', label: 'Turabian' },
    { id: 'turabian-dissertation', label: 'Turabian Dissertation' },
  ];
  
  const handleExport = () => {
    onExport(selectedFormat, {
      firstName: '<PERSON>',
      lastName: 'Doe',
      mainTitle: documentTitle,
      submissionDate: new Date().toISOString(),
    });
    onClose();
  };
  
  return (
    <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg shadow-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-card-foreground">Export Document</h2>
          <button 
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground"
          >
            ✕
          </button>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2 text-card-foreground">
            Document Format
          </label>
          <select 
            value={selectedFormat}
            onChange={(e) => setSelectedFormat(e.target.value)}
            className="w-full p-2 border rounded-md border-input bg-background text-foreground"
          >
            {formatOptions.map(option => (
              <option key={option.id} value={option.id}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        
        <div className="flex justify-end space-x-2">
          <button 
            onClick={onClose}
            className="px-4 py-2 border rounded-md text-accent-foreground hover:bg-accent"
          >
            Cancel
          </button>
          <button 
            onClick={handleExport}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Export
          </button>
        </div>
      </div>
    </div>
  );
}
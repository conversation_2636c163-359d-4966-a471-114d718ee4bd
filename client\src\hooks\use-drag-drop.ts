import { useState } from 'react';
import { OutlineItem } from '@/lib/types';
import { nanoid } from 'nanoid';
import { calculateOutlineNumbers } from '@/lib/utils/outline';

export function useDragDrop() {
  const [outline, setOutline] = useState<OutlineItem[]>([
    {
      id: nanoid(),
      number: '1',
      title: 'Introduction',
      children: [
        {
          id: nanoid(),
          number: '1.1',
          title: 'Background',
          children: []
        },
        {
          id: nanoid(),
          number: '1.2',
          title: 'Problem Statement',
          children: []
        }
      ]
    },
    {
      id: nanoid(),
      number: '2',
      title: 'Literature Review',
      children: [
        {
          id: nanoid(),
          number: '2.1',
          title: 'Current Renewable Technologies',
          children: [],
          linkedNoteCount: 2
        },
        {
          id: nanoid(),
          number: '2.2',
          title: 'Implementation Challenges',
          children: []
        }
      ]
    },
    {
      id: nanoid(),
      number: '3',
      title: 'Methodology',
      children: []
    }
  ]);

  // Add a new outline item at the root level
  const addOutlineItem = (parentId?: string) => {
    // If no parentId is provided, add at the root level
    if (!parentId) {
      const newItem: OutlineItem = {
        id: nanoid(),
        number: '', // This will be calculated later
        title: 'New Section',
        children: []
      };
      
      const newOutline = [...outline, newItem];
      setOutline(calculateOutlineNumbers(newOutline));
      return;
    }
    
    // Add as a child to a specific parent
    const addChildToParent = (items: OutlineItem[], parentId: string): OutlineItem[] => {
      return items.map(item => {
        if (item.id === parentId) {
          const children = item.children || [];
          const newChildren = [
            ...children,
            {
              id: nanoid(),
              number: '', // This will be calculated later
              title: 'New Subsection',
              children: []
            }
          ];
          
          return {
            ...item,
            children: newChildren
          };
        }
        
        if (item.children && item.children.length > 0) {
          return {
            ...item,
            children: addChildToParent(item.children, parentId)
          };
        }
        
        return item;
      });
    };
    
    const newOutline = addChildToParent(outline, parentId);
    setOutline(calculateOutlineNumbers(newOutline));
  };

  // Find an outline item by ID (recursive search)
  const findOutlineItemById = (
    items: OutlineItem[],
    id: string
  ): OutlineItem | null => {
    for (const item of items) {
      if (item.id === id) {
        return item;
      }
      
      if (item.children && item.children.length > 0) {
        const found = findOutlineItemById(item.children, id);
        if (found) {
          return found;
        }
      }
    }
    
    return null;
  };

  // Update an outline item by ID
  const updateOutlineItem = (
    items: OutlineItem[],
    id: string,
    updates: Partial<OutlineItem>
  ): OutlineItem[] => {
    return items.map(item => {
      if (item.id === id) {
        return {
          ...item,
          ...updates
        };
      }
      
      if (item.children && item.children.length > 0) {
        return {
          ...item,
          children: updateOutlineItem(item.children, id, updates)
        };
      }
      
      return item;
    });
  };

  // Delete an outline item by ID
  const deleteOutlineItem = (items: OutlineItem[], id: string): OutlineItem[] => {
    const filtered = items.filter(item => item.id !== id);
    
    return filtered.map(item => {
      if (item.children && item.children.length > 0) {
        return {
          ...item,
          children: deleteOutlineItem(item.children, id)
        };
      }
      
      return item;
    });
  };

  return {
    outline,
    setOutline,
    addOutlineItem,
    findOutlineItemById,
    updateOutlineItem,
    deleteOutlineItem
  };
}

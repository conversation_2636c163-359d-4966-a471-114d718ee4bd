import React, { useState, useEffect } from 'react';

interface DebugConsoleProps {
  show?: boolean;
}

/**
 * A debugging console that shows in the UI - useful when console.log
 * doesn't work as expected or when testing on mobile
 */
export function DebugConsole({ show = true }: DebugConsoleProps) {
  const [logs, setLogs] = useState<string[]>([]);
  
  useEffect(() => {
    if (!show) return;
    
    const originalConsoleLog = console.log;
    
    console.log = (...args: any[]) => {
      const message = args.map(arg => {
        if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg, null, 2);
          } catch (e) {
            return String(arg);
          }
        }
        return String(arg);
      }).join(' ');
      
      setLogs(prevLogs => [...prevLogs, message]);
      originalConsoleLog(...args);
    };
    
    // Restore original on cleanup
    return () => {
      console.log = originalConsoleLog;
    };
  }, [show]);
  
  if (!show) return null;
  
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-black/90 text-green-400 font-mono text-xs max-h-32 overflow-y-auto">
      <div className="flex justify-between items-center p-1 border-b border-green-800">
        <div>Debug Console</div>
        <button 
          className="text-xs bg-red-900 text-white px-2 py-0.5 rounded"
          onClick={() => setLogs([])}
        >
          Clear
        </button>
      </div>
      <div className="p-2 space-y-1">
        {logs.map((log, i) => (
          <div key={i}>&gt; {log}</div>
        ))}
        {logs.length === 0 && <div className="text-gray-500">No logs yet...</div>}
      </div>
    </div>
  );
}
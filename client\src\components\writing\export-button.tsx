import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Note, OutlineItem, Citation, DocumentExportMetadata } from '@/lib/types';
import { exportDocument, downloadBlob } from '@/lib/services/document-export';
import { ExportDocumentModal } from '@/components/export-document-modal';
import { useDocumentExportSettings } from '@/hooks/use-document-export-settings';

interface ExportButtonProps {
  content: string;
  outline: OutlineItem[];
  notes: Note[];
  citations: Citation[];
  documentId?: string;
  notesType: 'footnotes' | 'endnotes';
  disabled?: boolean;
}

export function ExportButton({ content, outline, notes, citations, documentId, notesType, disabled = false }: ExportButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const documentTitle = outline[0]?.title || 'Document Title';
  
  // Use the document export settings hook
  const { 
    format, 
    metadata, 
    saveSettings,
    isLoading,
    isSaving
  } = useDocumentExportSettings(documentId, documentTitle);

  // Handle document export
  const handleExport = async (newFormat: string, newMetadata: DocumentExportMetadata) => {
    try {
      console.log('Exporting document with format:', newFormat);
      
      // Save the export settings if we have a document ID
      if (documentId) {
        await saveSettings(newFormat, newMetadata);
      }
      
      // Generate the document
      const blob = await exportDocument({
        format: newFormat as any,
        metadata: newMetadata,
        content,
        outline,
        notes,
        citations
      });
      
      // Download the file
      downloadBlob(blob, `${documentTitle || 'document'}.docx`);
      
      console.log('Document exported successfully');
    } catch (error) {
      console.error('Error exporting document:', error);
    }
  };
  
  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="shadow-sm flex items-center gap-2 border-border disabled:opacity-50 disabled:cursor-not-allowed"
        onClick={() => {
          console.log('Opening export modal');
          setIsModalOpen(true);
        }}
        disabled={isLoading || disabled}
      >
        {isLoading ? (
          <>
            <i className="ri-loader-2-line animate-spin"></i>
            <span>Loading...</span>
          </>
        ) : (
          <>
            <i className="ri-upload-2-line"></i>
            <span>Export</span>
          </>
        )}
      </Button>
      
      {isModalOpen && !isLoading && (
        <ExportDocumentModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onExport={handleExport}
          documentTitle={documentTitle}
          documentId={documentId}
          initialFormat={format}
          initialMetadata={{
            ...metadata,
            footnotes: notesType === 'footnotes' ? 'page' : 'end'
          }}
          isSaving={isSaving}
        />
      )}
    </>
  );
}
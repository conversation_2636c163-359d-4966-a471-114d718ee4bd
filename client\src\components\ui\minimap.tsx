import React, { useState, useEffect, useRef, RefObject } from "react";

interface MinimapProps {
  contentRef: RefObject<HTMLElement>;
}

export function Minimap({ contentRef }: MinimapProps) {
  // Drag state for the viewport indicator
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartY, setDragStartY] = useState(0);
  const [initialScrollPosition, setInitialScrollPosition] = useState(0);

  const [viewportRatio, setViewportRatio] = useState<{
    top: number;
    height: number;
  }>({ top: 0, height: 0.2 });
  const [contentSnapshot, setContentSnapshot] = useState<string>("");
  const [shouldUpdateContent, setShouldUpdateContent] = useState(true);
  const [updateCounter, setUpdateCounter] = useState(0); // Counter to force updates
  const minimapRef = useRef<HTMLDivElement>(null);
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const indicatorRef = useRef<HTMLDivElement>(null);
  const scrollTimeout = useRef<NodeJS.Timeout>();

  // Function to force an update of the minimap content
  const forceUpdate = React.useCallback(() => {
    // Skip updates during drag operations
    if (
      document.body.classList.contains("dragging-mode") ||
      document.body.classList.contains("disable-websocket-updates")
    ) {
      return;
    }
    setShouldUpdateContent(true);
    setUpdateCounter((prev) => prev + 1); // Increment counter to force effect to run
  }, []);

  // Create a content snapshot for the minimap
  useEffect(() => {
    if (!contentRef.current || !shouldUpdateContent) return;

    // Clone content for minimap representation
    try {
      // Create a clone of the content
      const clonedContent = contentRef.current.cloneNode(true) as HTMLElement;

      // Remove interactive elements and simplify content
      const buttons = clonedContent.querySelectorAll(
        "button, input, .drag-handle",
      );
      buttons.forEach((button) => button.remove());

      // Simplify other complex elements
      const complexElements = clonedContent.querySelectorAll(
        ".ri-add-line, .ri-delete-bin-line, .ri-more-fill, .ri-sticky-note-add-line", // Added add note icon just in case
      );
      complexElements.forEach((el) => el.remove());

      // Extract outline structure but simplify content
      const outlineItems = clonedContent.querySelectorAll(".outline-item");
      outlineItems.forEach((item) => {
        // Keep structure but simplify text content
        const titleElement = item.querySelector(".outline-item-title");
        if (titleElement) {
          // Keep only first 2-3 words with an ellipsis for long titles
          const title = titleElement.textContent || "";
          const words = title.trim().split(/\s+/);
          if (words.length > 2) {
            titleElement.textContent = words.slice(0, 2).join(" ") + "...";
          }
        }

        // Style note previews and hide text
        const notePreviewWrapper = item.querySelector('.outline-item-notes-preview-wrapper');
        if (notePreviewWrapper) {
          notePreviewWrapper.classList.add('minimap-note-preview-wrapper');
          const notes = notePreviewWrapper.querySelectorAll('.outline-item-note');
          notes.forEach(note => {
            note.classList.add('minimap-note');
            const noteText = note.querySelector('.outline-item-note-text');
            if (noteText) {
              noteText.textContent = '';
            }
          });
        }

        // Remove more elements to simplify the view
        const actionButtons = item.querySelectorAll(
          ".outline-item-actions, .hover-actions",
        );
        actionButtons.forEach((btn) => btn.remove());
      });

      // Remove event handlers and IDs
      const allElements = clonedContent.querySelectorAll("*");
      allElements.forEach((el) => {
        // Remove all event handlers
        el.replaceWith(el.cloneNode(true));
        // Remove ids to avoid duplicate IDs in DOM
        if (el instanceof HTMLElement) {
          el.removeAttribute("id");

          // Add classes to improve minimap appearance
          if (el.classList.contains("outline-item")) {
            el.classList.add("minimap-outline-item");
          }
          // Add styling for note previews in the minimap
          if (el.classList.contains("outline-item-notes-preview-wrapper")) {
            el.classList.add("minimap-note-preview-wrapper");
          }
          if (el.classList.contains("outline-item-note")) {
            el.classList.add("minimap-note");
          }
        }
      });

      // Set the content snapshot
      setContentSnapshot(clonedContent.outerHTML);
      setShouldUpdateContent(false);

      // Apply VS Code scaling in next tick after React has rendered the content
      setTimeout(() => {
        if (
          contentRef.current &&
          minimapRef.current &&
          contentContainerRef.current
        ) {
          const minimapHeight = minimapRef.current.clientHeight;
          const totalHeight = contentRef.current.scrollHeight;

          // Get the minimap content element
          const minimapContent =
            contentContainerRef.current.querySelector(".minimap-content");
          if (minimapContent && minimapContent instanceof HTMLElement) {
            // First remove any previous scale transformations
            minimapContent.style.transform = "";
            minimapContent.style.width = "";
            minimapContent.style.height = "";

            // Get natural content height after resetting styles
            const contentHeight = minimapContent.scrollHeight;

            // Calculate true scaling factor - VS Code style
            let scaleToFit = minimapHeight / contentHeight;

            // Limit maximum and minimum scale for better usability with all document sizes
            const maxScale = 0.4; // Maximum scale factor to prevent items becoming too large
            const minScale = 0.1; // Minimum scale factor to prevent items becoming too small
            scaleToFit = Math.min(Math.max(scaleToFit, minScale), maxScale);

            // Apply proper scale transform to fit in minimap
            minimapContent.style.transform = `scale(${scaleToFit})`;
            minimapContent.style.transformOrigin = "top left";

            // Enable all content to be visible after scaling
            minimapContent.style.width = `${100 / scaleToFit}%`;

            // Important: Set the content container max-height to ensure scrollability
            contentContainerRef.current.style.maxHeight = `${minimapHeight}px`;

            // Debug output
            if (false) {
              // Set to true to enable
              console.log(
                `[Minimap] Applied VS Code scaling: ${scaleToFit.toFixed(3)}`,
              );
            }
          }
        }
      }, 0);

      // Schedule less frequent updates to keep the minimap content fresh
      // but not interfere with drag operations
      const updateInterval = setInterval(() => {
        // Only update if not in dragging mode
        if (
          !document.body.classList.contains("dragging-mode") &&
          !document.body.classList.contains("disable-websocket-updates")
        ) {
          setShouldUpdateContent(true);
        }
      }, 1000); // Update every 1000ms (less frequent to avoid drag conflicts)

      return () => clearInterval(updateInterval);
    } catch (error) {
      console.error("Error creating minimap content:", error);
    }
  }, [contentRef, shouldUpdateContent, updateCounter]);

  // Add a MutationObserver to watch for content changes and update the minimap
  useEffect(() => {
    if (!contentRef.current) return;

    // Create MutationObserver
    const observer = new MutationObserver((mutations) => {
      // Skip all observation during drag operations
      if (
        document.body.classList.contains("dragging-mode") ||
        document.body.classList.contains("disable-websocket-updates")
      ) {
        console.log("Minimap update skipped during drag operation");
        return;
      }

      // Check if any mutations directly affect outline items
      const outlineChanges = mutations.some((mutation) => {
        const target = mutation.target;
        // Check if target is an HTMLElement before calling DOM methods
        if (target instanceof HTMLElement) {
          return (
            target.closest(".outline-item") !== null ||
            target.classList?.contains("outline-item")
          );
        }
        // For text nodes and other non-element nodes, check mutation type
        return mutation.type === "childList";
      });

      // If outline items were modified, immediately update
      if (outlineChanges) {
        // Immediately recalculate viewport ratio
        setTimeout(() => {
          if (contentRef.current && minimapRef.current) {
            // Trigger both content update and viewport ratio update
            forceUpdate();

            // Force viewport calculation to run on next tick after the DOM has been updated
            setTimeout(() => {
              const event = new Event("resize");
              window.dispatchEvent(event);
            }, 50);
          }
        }, 0);
      } else if (
        !document.body.classList.contains("dragging-mode") &&
        !document.body.classList.contains("disable-websocket-updates")
      ) {
        // Only queue updates when not in drag mode
        setShouldUpdateContent(true);
      }
    });

    // Function to enable/disable observation based on drag state
    const updateObserverStatus = () => {
      if (
        document.body.classList.contains("dragging-mode") ||
        document.body.classList.contains("disable-websocket-updates")
      ) {
        observer.disconnect();
        console.log("Minimap observer disconnected during drag operation");
      } else {
        if (contentRef.current) {
          observer.observe(contentRef.current, {
            childList: true, // Watch for added/removed elements
            subtree: true, // Watch all descendants
            attributes: true, // Watch attributes
            characterData: true, // Watch for text changes
          });
          console.log("Minimap observer reconnected after drag operation");
        }
      }
    };

    // Initial observer setup
    updateObserverStatus();

    // Listen for custom outline changed events
    const handleOutlineChanged = () => {
      console.log("Outline changed event detected!");
      // Only update if not dragging
      if (
        !document.body.classList.contains("dragging-mode") &&
        !document.body.classList.contains("disable-websocket-updates")
      ) {
        forceUpdate();

        // Also force viewport recalculation
        setTimeout(() => {
          const event = new Event("resize");
          window.dispatchEvent(event);
        }, 50);
      }
    };

    // Listen for drag state changes
    const dragStartHandler = () => {
      updateObserverStatus();
    };

    const dragEndHandler = () => {
      // Delay reconnection slightly to ensure drag operation is fully complete
      setTimeout(updateObserverStatus, 100);
    };

    // Add custom event listener for explicit outline changes
    contentRef.current.addEventListener("outlineChanged", handleOutlineChanged);

    // Setup listeners for drag operations
    document.body.addEventListener("dragstart", dragStartHandler);
    document.body.addEventListener("dragend", dragEndHandler);

    // Also listen for our custom classes
    const bodyObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === "class") {
          updateObserverStatus();
        }
      });
    });

    bodyObserver.observe(document.body, { attributes: true });

    // Clean up observer when component unmounts
    return () => {
      observer.disconnect();
      bodyObserver.disconnect();
      contentRef.current?.removeEventListener(
        "outlineChanged",
        handleOutlineChanged,
      );
      document.body.removeEventListener("dragstart", dragStartHandler);
      document.body.removeEventListener("dragend", dragEndHandler);
    };
  }, [contentRef, forceUpdate]);

  useEffect(() => {
    if (!contentRef.current) return;

    // Debug log function
    const debug = (message: string) => {
      if (false) {
        // Set to true to enable debugging
        console.log(`[Minimap] ${message}`);
      }
    };

    const calculateViewportRatio = () => {
      if (!contentRef.current || !minimapRef.current || !contentContainerRef.current) return;

      const contentEl = contentRef.current;
      const minimapEl = minimapRef.current;
      const minimapContentContainer = contentContainerRef.current;

      // Get the minimap content element
      const minimapContent = minimapContentContainer.querySelector('.minimap-content');
      if (!minimapContent) return;

      // Get the actual dimensions
      const scrollTop = contentEl.scrollTop;
      const viewportHeight = contentEl.clientHeight;
      const totalHeight = contentEl.scrollHeight;

      // First, handle special case where content fits entirely in viewport
      if (totalHeight <= viewportHeight) {
        setViewportRatio({ top: 0, height: 1.0 });
        return;
      }

      // Get the computed scale of the minimap content
      const computedStyle = window.getComputedStyle(minimapContent);
      const transformValue = computedStyle.transform;

      // Extract scale from the transform matrix
      let scale = 0.05; // Default to minimum scale
      if (transformValue && transformValue !== 'none') {
        const match = transformValue.match(/matrix\(([^,]+),/);
        if (match && match[1]) {
          scale = parseFloat(match[1]);
        }
      }

      // Get dimensions of the scaled content in the minimap
      const minimapContentHeight = minimapContent.scrollHeight * scale;
      const minimapHeight = minimapEl.clientHeight;

      debug(`Minimap content height: ${minimapContentHeight}, Scale: ${scale}`);

      // Calculate viewport indicator size as a direct ratio of viewable content
      const viewportRatio = viewportHeight / totalHeight;
      const indicatorHeight = minimapContentHeight * viewportRatio;

      // Apply minimum and maximum constraints for usability
      const minIndicatorSize = Math.min(minimapHeight * 0.1, 30); // 10% of minimap or 30px max
      const maxIndicatorSize = minimapHeight * 0.8; // 80% of minimap at most

      const finalIndicatorHeight = Math.max(
        minIndicatorSize,
        Math.min(maxIndicatorSize, indicatorHeight)
      );

      // Directly calculate position in the minimap based on content scroll position
      // If we're at the top of the document (scrollTop = 0), indicator should be at top
      // If we're at the bottom, indicator should be at the bottom of the minimap

      // Calculate what percentage of the scrollable area we've scrolled through
      // The scrollable area is (totalHeight - viewportHeight)
      // When we're fully scrolled to bottom, scrollTop = (totalHeight - viewportHeight)
      const maxScrollTop = totalHeight - viewportHeight;
      const scrollPercentage = maxScrollTop <= 0
        ? 0
        : scrollTop / maxScrollTop;

      // Adjust percentage to account for viewport size
      // This ensures the indicator doesn't go below the last visible item
      const adjustedPercentage = scrollPercentage * (1 - (viewportHeight / totalHeight));

      // The indicator should be positioned at that adjusted percentage of the minimap height
      // Using maxVisiblePosition ensures the indicator never goes past the visible content
      const maxVisiblePosition = minimapHeight - finalIndicatorHeight;
      const indicatorPosition = maxVisiblePosition * scrollPercentage;

      // Convert to ratio values (0-1) for component positioning
      const topRatio = indicatorPosition / minimapHeight;
      const heightRatio = finalIndicatorHeight / minimapHeight;

      debug(`Direct ratio calculation - Top: ${topRatio}, Height: ${heightRatio}`);

      setViewportRatio({
        top: topRatio,
        height: heightRatio,
      });
    };

    // Calculate initially and on scroll
    calculateViewportRatio();

    const handleScroll = () => {
      // Skip during drag operations
      if (
        document.body.classList.contains("dragging-mode") ||
        document.body.classList.contains("disable-websocket-updates")
      ) {
        return;
      }

      // Update viewport position
      calculateViewportRatio();

      // Update content on scroll stop (more efficient than on every scroll event)
      clearTimeout(scrollTimeout.current);
      scrollTimeout.current = setTimeout(() => {
        // Check again before scheduling the update
        if (
          !document.body.classList.contains("dragging-mode") &&
          !document.body.classList.contains("disable-websocket-updates")
        ) {
          setShouldUpdateContent(true);
        }
      }, 100);
    };

    contentRef.current.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", calculateViewportRatio);

    return () => {
      contentRef.current?.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", calculateViewportRatio);
    };
  }, [contentRef]);

  const handleMinimapClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!contentRef.current || !minimapRef.current || !contentContainerRef.current) return;

    const minimapEl = minimapRef.current;
    const contentEl = contentRef.current;
    const minimapContentContainer = contentContainerRef.current;

    // Get the minimap content element
    const minimapContent = minimapContentContainer.querySelector('.minimap-content');
    if (!minimapContent) return;

    const minimapRect = minimapEl.getBoundingClientRect();
    const minimapHeight = minimapRect.height;

    // Get document dimensions
    const totalHeight = contentEl.scrollHeight;
    const viewportHeight = contentEl.clientHeight;

    // Get the computed scale of the minimap content
    const computedStyle = window.getComputedStyle(minimapContent);
    const transformValue = computedStyle.transform;

    // Extract scale from the transform matrix
    let scale = 0.05; // Default to minimum scale
    if (transformValue && transformValue !== 'none') {
      const match = transformValue.match(/matrix\(([^,]+),/);
      if (match && match[1]) {
        scale = parseFloat(match[1]);
      }
    }

    // Get dimensions of the scaled content in the minimap
    const minimapContentHeight = minimapContent.scrollHeight * scale;

    // Get click position relative to minimap
    const clickY = e.clientY - minimapRect.top;

    // Calculate the scroll percentage based on click position
    // If click is at the bottom of the minimap, we want to scroll to the bottom
    const clickRatio = Math.min(clickY / minimapHeight, 1.0);

    // Convert click position to scroll percentage
    // Need to account for the fact that max scroll is (totalHeight - viewportHeight)
    const maxScrollOffset = totalHeight - viewportHeight;

    // Calculate target scroll position
    const targetScrollPosition = maxScrollOffset * clickRatio;

    // Center the viewport on the clicked position when possible
    // This matches VS Code behavior of centering the viewport on the clicked position
    const halfViewportHeight = viewportHeight / 2;
    let centeredPosition = targetScrollPosition - halfViewportHeight;

    // Ensure we don't scroll past the document boundaries
    const clampedPosition = Math.max(0, Math.min(maxScrollOffset, centeredPosition));

    // Apply scroll with smooth animation
    contentEl.scrollTo({
      top: clampedPosition,
      behavior: "smooth",
    });
  };

  // Handle drag start for the viewport indicator
  const handleIndicatorDragStart = (e: React.MouseEvent<HTMLDivElement>) => {
    // Only allow dragging when not in outline dragging mode
    if (
      document.body.classList.contains("dragging-mode") ||
      document.body.classList.contains("disable-websocket-updates")
    ) {
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    if (!contentRef.current || !minimapRef.current) return;

    // Disable text selection during drag
    document.body.classList.add("minimap-dragging");

    setIsDragging(true);
    setDragStartY(e.clientY);
    setInitialScrollPosition(contentRef.current.scrollTop);

    // Set up global mouse event handlers
    document.addEventListener("mousemove", handleIndicatorDragMove);
    document.addEventListener("mouseup", handleIndicatorDragEnd);
  };

  // Handle drag move for the viewport indicator
  const handleIndicatorDragMove = (e: MouseEvent) => {
    if (!isDragging || !contentRef.current || !minimapRef.current || !indicatorRef.current) return;

    e.preventDefault();

    const minimapRect = minimapRef.current.getBoundingClientRect();
    const minimapHeight = minimapRect.height;

    // Calculate how far we've dragged in pixels
    const deltaY = e.clientY - dragStartY;

    // Convert to percentage of minimap height
    const deltaRatio = deltaY / minimapHeight;

    // Calculate scroll position based on document height
    const totalHeight = contentRef.current.scrollHeight;
    const viewportHeight = contentRef.current.clientHeight;
    const maxScroll = totalHeight - viewportHeight;

    // Calculate new scroll position
    const newScrollPosition = initialScrollPosition + (deltaRatio * maxScroll);
    const clampedScrollPosition = Math.max(0, Math.min(maxScroll, newScrollPosition));

    // Apply the scroll position
    contentRef.current.scrollTop = clampedScrollPosition;

    // DIRECT DOM APPROACH - Update indicator position in real-time
    // This bypasses React state updates for smoother dragging
    if (indicatorRef.current && minimapRef.current) {
      const scrollTop = contentRef.current.scrollTop;
      const maxScrollOffset = totalHeight - viewportHeight;
      const scrollPercentage = maxScrollOffset <= 0 ? 0 : scrollTop / maxScrollOffset;

      // Calculate indicator height
      const viewportHeightRatio = viewportHeight / totalHeight;
      const indicatorHeight = minimapHeight * viewportHeightRatio;

      // Apply constraints
      const minIndicatorSize = Math.min(minimapHeight * 0.1, 30);
      const maxIndicatorSize = minimapHeight * 0.8;
      const finalIndicatorHeight = Math.max(
        minIndicatorSize,
        Math.min(maxIndicatorSize, indicatorHeight)
      );

      // Calculate position the exact same way as in the non-dragging case
      const maxVisiblePosition = minimapHeight - finalIndicatorHeight;
      const indicatorPosition = maxVisiblePosition * scrollPercentage;

      // Directly update the DOM element style for immediate visual feedback
      // Use percentage for the top position to match how React renders it
      const topRatio = indicatorPosition / minimapHeight;
      const heightRatio = finalIndicatorHeight / minimapHeight;

      // Apply both position and size in real-time
      indicatorRef.current.style.top = `${topRatio * 100}%`;
      indicatorRef.current.style.height = `${heightRatio * 100}%`;
    }
  };

  // Handle drag end for the viewport indicator
  const handleIndicatorDragEnd = (e: MouseEvent) => {
    e.preventDefault();

    if (!contentRef.current || !minimapRef.current || !indicatorRef.current) {
      setIsDragging(false);
      return;
    }

    // Calculate final position to sync React state
    const scrollTop = contentRef.current.scrollTop;
    const totalHeight = contentRef.current.scrollHeight;
    const viewportHeight = contentRef.current.clientHeight;
    const maxScrollOffset = totalHeight - viewportHeight;
    const scrollPercentage = maxScrollOffset <= 0 ? 0 : scrollTop / maxScrollOffset;

    // Get minimap dimensions
    const minimapHeight = minimapRef.current.clientHeight;

    // Calculate indicator height
    const viewportHeightRatio = viewportHeight / totalHeight;
    const indicatorHeight = minimapHeight * viewportHeightRatio;

    // Apply constraints
    const minIndicatorSize = Math.min(minimapHeight * 0.1, 30);
    const maxIndicatorSize = minimapHeight * 0.8;
    const finalIndicatorHeight = Math.max(
      minIndicatorSize,
      Math.min(maxIndicatorSize, indicatorHeight)
    );

    // Calculate position
    const maxVisiblePosition = minimapHeight - finalIndicatorHeight;
    const indicatorPosition = maxVisiblePosition * scrollPercentage;

    // Convert to ratio values for React state
    const topRatio = indicatorPosition / minimapHeight;
    const heightRatio = finalIndicatorHeight / minimapHeight;

    // Sync the React state with the final position
    setViewportRatio({
      top: topRatio,
      height: heightRatio
    });

    setIsDragging(false);

    // Remove global event listeners
    document.removeEventListener("mousemove", handleIndicatorDragMove);
    document.removeEventListener("mouseup", handleIndicatorDragEnd);

    // Re-enable text selection
    document.body.classList.remove("minimap-dragging");
  };

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Skip if outline panel is in dragging mode or if we are dragging the indicator
    if (
      document.body.classList.contains("dragging-mode") ||
      document.body.classList.contains("disable-websocket-updates") ||
      isDragging
    ) {
      return;
    }

    // Otherwise proceed with normal click handler
    handleMinimapClick(e);
  };

  // Always show the minimap, even when content fits the viewport
  // This ensures consistent UI with the scrollbar always visible

  // Always visible minimap
  return (
    <div
      ref={minimapRef}
      className="absolute left-0 right-0 top-0 bottom-0 w-full bg-gray-50/95 overflow-hidden"
      onClick={handleClick}
      // Drag handlers removed to prevent interference with outline panel drag operations
    >
      {/* Content representation - always fit content to minimap height */}
      <div
        ref={contentContainerRef}
        className="absolute top-0 left-0 right-0 bottom-0 overflow-hidden"
        style={{
          fontSize: "5px",
          height: "100%", // Ensure container takes full height
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-start", // Align content to the top
          alignItems: "flex-start", // Align content to the left
        }}
      >
        {contentSnapshot && (
          <div
            className="minimap-content"
            style={{
              // VS Code approach: always display the entire document content
              transformOrigin: "top left",
              // Don't set a fixed height - let content determine it
              position: "relative",
              // Prevent the browser from wrapping the content
              whiteSpace: "pre",
            }}
            dangerouslySetInnerHTML={{ __html: contentSnapshot }}
          />
        )}
      </div>

      {/* Viewport indicator - dynamic size based on visible portion */}
      <div
        ref={indicatorRef}
        className={`absolute z-10 rounded minimap-viewport-indicator ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
        style={{
          top: `${viewportRatio.top * 100}%`,
          height: `${viewportRatio.height * 100}%`,
          // Ensure bounds for extreme cases
          minHeight: "20px",
          maxHeight: "60%",
          left: "2px",
          right: "2px",
          backgroundColor: "rgba(37, 99, 235, 0.2)",
          border: "1px solid rgba(37, 99, 235, 0.7)",
          boxShadow: "inset 0 0 3px 1px rgba(255, 255, 255, 0.6)",
          pointerEvents: "auto", // Allow pointer events for dragging
          transition: isDragging ? 'none' : 'top 0.1s ease-out', // Only apply transition when not dragging
        }}
        onMouseDown={handleIndicatorDragStart}
      />
    </div>
  );
}

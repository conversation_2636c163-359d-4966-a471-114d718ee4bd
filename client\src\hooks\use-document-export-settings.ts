import { useState, useEffect } from 'react';
import { DocumentExportMetadata } from '@/lib/types';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Initial metadata with default values
const getDefaultMetadata = (documentTitle: string): DocumentExportMetadata => ({
  // Author information
  firstName: '',
  middleName: '',
  lastName: '',
  coauthors: '',

  // Class information
  instructor: '',
  course: '',
  courseName: '',
  institution: '',
  submissionDate: new Date().toLocaleDateString('en-US'),

  // Title information
  shortTitle: '',
  mainTitle: documentTitle || 'Document Title',
  subtitle: '',

  // Headings
  tocHeading: 'Table of Contents',
  introductionHeading: 'Introduction',
  conclusionHeading: 'Conclusion',
  bibliographyHeading: 'Bibliography',
  authorNote: '',
  dedication: '',
  abstract: '',
  keywords: '',
  footnotes: '',

  // Conferment information
  degreeTitle: '',
  college: '',
  copyrightYear: new Date().getFullYear().toString(),

  // Signers information
  signers: [
    { name: '', jobTitle: '', role: '' },
    { name: '', jobTitle: '', role: '' },
    { name: '', jobTitle: '', role: '' },
    { name: '', jobTitle: '', role: '' }
  ]
});

export interface ExportSettings {
  format: string;
  metadata: DocumentExportMetadata;
  isLoading: boolean;
  isSaving: boolean;
  error: Error | null;
  saveSettings: (format: string, metadata: DocumentExportMetadata) => Promise<void>;
}

export function useDocumentExportSettings(documentId?: number, documentTitle = 'Document Title'): ExportSettings {
  const [format, setFormat] = useState<string>('apa-student');
  const [metadata, setMetadata] = useState<DocumentExportMetadata>(getDefaultMetadata(documentTitle));
  const queryClient = useQueryClient();
  
  // Query for fetching export settings
  const { 
    data: exportSettings,
    isLoading,
    error
  } = useQuery({
    queryKey: ['/api/documents', documentId, 'export-settings'],
    queryFn: async () => {
      if (!documentId) return null;
      
      const response = await fetch(`/api/documents/${documentId}/export-settings`);
      if (!response.ok) {
        throw new Error('Failed to fetch export settings');
      }
      return response.json();
    },
    enabled: !!documentId
  });
  
  // Update local state when export settings are loaded
  useEffect(() => {
    if (exportSettings) {
      setFormat(exportSettings.format);
      setMetadata(exportSettings.metadata);
    } else {
      setFormat('apa-student');
      setMetadata(getDefaultMetadata(documentTitle));
    }
  }, [exportSettings, documentTitle]);

  // Mutation for saving export settings
  const { mutateAsync: saveSettingsMutation, isPending: isSaving } = useMutation({
    mutationFn: async (data: { format: string, metadata: DocumentExportMetadata }) => {
      if (!documentId) return null;
      
      const response = await fetch(`/api/documents/${documentId}/export-settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        throw new Error('Failed to save export settings');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate the export settings query to refetch the latest data
      queryClient.invalidateQueries({ queryKey: ['/api/documents', documentId, 'export-settings'] });
    }
  });

  // Save settings function
  const saveSettings = async (newFormat: string, newMetadata: DocumentExportMetadata) => {
    if (!documentId) return;
    
    try {
      await saveSettingsMutation({ format: newFormat, metadata: newMetadata });
      setFormat(newFormat);
      setMetadata(newMetadata);
    } catch (e) {
      console.error('Error saving export settings:', e);
      throw e;
    }
  };

  return {
    format,
    metadata,
    isLoading,
    isSaving,
    error: error as Error | null,
    saveSettings
  };
}
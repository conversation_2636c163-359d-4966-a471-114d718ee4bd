@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reference Manager Styles */
/* Only hide inactive content in reference manager, not tab buttons */
.reference-manager-content [data-state="inactive"],
.reference-collection-content [data-state="inactive"] {
  display: none !important;
}

/* Icon color definitions and utilities */
:root {
  /* Base HSL icon colors (Light Mode) */
  --icon-blue-hsl: 214 100% 60%; /* from #3b82f6 */
  --icon-green-hsl: 158 82% 45%; /* from #10b981 */
  --icon-purple-hsl: 258 90% 67%; /* from #8b5cf6 */
  --icon-amber-hsl: 38 96% 51%; /* from #f59e0b */

  /* Dark Mode HSL icon colors (can be overridden by use-preferences if needed for further customization) */
  --icon-blue-dark-hsl: 207 83% 33%;   /* Default dark primary, e.g. VS Code blue button */
  --icon-green-dark-hsl: 145 63% 49%;  /* from #34d399 */
  --icon-purple-dark-hsl: 255 79% 76%; /* from #a78bfa */
  --icon-amber-dark-hsl: 45 93% 58%;   /* from #fbbf24 */
}

/* Icon color utility classes */
.text-icon-blue { color: hsl(var(--icon-blue-hsl)); }
.text-icon-green { color: hsl(var(--icon-green-hsl)); }
.text-icon-purple { color: hsl(var(--icon-purple-hsl)); }
.text-icon-amber { color: hsl(var(--icon-amber-hsl)); }

.dark .text-icon-blue { color: hsl(var(--icon-blue-dark-hsl, var(--icon-blue-hsl))); }
.dark .text-icon-green { color: hsl(var(--icon-green-dark-hsl, var(--icon-green-hsl))); }
.dark .text-icon-purple { color: hsl(var(--icon-purple-dark-hsl, var(--icon-purple-hsl))); }
.dark .text-icon-amber { color: hsl(var(--icon-amber-dark-hsl, var(--icon-amber-hsl))); }

/* Remove borders from content-editable elements */
[contenteditable="true"] {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Custom dialog styling */
.DialogOverlay {
  background-color: hsl(var(--foreground) / 0.5); 
  position: fixed;
  inset: 0;
  z-index: 49;
  animation: fadeIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

.DialogContent {
  background-color: hsl(var(--card)); 
  color: hsl(var(--card-foreground)); 
  border: 1px solid hsl(var(--border)); 
  border-radius: var(--radius); 
  box-shadow: 0 10px 25px hsl(var(--foreground) / 0.1); 
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90vw;
  max-height: 85vh;
  overflow-y: auto;
  z-index: 50;
  animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes contentShow {
  from { 
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to { 
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@layer base {
  :root {
    /* Default theme variables in HSL for Tailwind and general use */
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;
    --primary: 214 100% 60%; 
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 9%;
    --border: 0 0% 90%;
    --input: 0 0% 90%; 
    --ring: 214 100% 60%; 
    --radius: 0.5rem;
    --radius-sm: calc(var(--radius) - 4px); /* Small radius */
    --radius-md: calc(var(--radius) - 2px); /* Medium radius, if needed */
    --popover: 0 0% 100%; 
    --popover-foreground: 0 0% 9%; 
    --destructive: 0 84% 60%; 
    --destructive-foreground: 0 0% 100%; 

    --outline-item-background-even: var(--background);
    --outline-item-background-odd: hsl(var(--muted));
    
    /* Font family and scale variables */
    --font-face-labels: 'Aptos';
    --font-face-user-text: 'Aptos';
    --font-scale-labels: 1;
    --font-scale-user-text: 1;
    --font-family-labels: var(--font-face-labels), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    --font-family-user-text: var(--font-face-user-text), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    --font-size-base-labels: calc(1rem * var(--font-scale-labels));
    --font-size-base-user-text: calc(1rem * var(--font-scale-user-text));

    /* RGB versions for potential JS interop or legacy components. HSL is primary for CSS. */
    --primary-rgb: 59, 130, 246; 
    --ring-rgb: 59, 130, 246; 
    --background-rgb: 255, 255, 255; 
    --foreground-rgb: 23, 23, 23; 
    --primary-foreground-rgb: 255, 255, 255; 
    --secondary-rgb: 245, 245, 245; 
    --muted-rgb: 245, 245, 245; 
    --muted-foreground-rgb: 115, 115, 115; 
    --accent-rgb: 245, 245, 245; 
    --accent-foreground-rgb: 23, 23, 23; 
    --card-rgb: 255, 255, 255; 
    --card-foreground-rgb: 23, 23, 23; 
    --destructive-rgb: 239, 68, 68; 
    --destructive-foreground-rgb: 255, 255, 255; 
    --border-rgb: 229, 231, 235; 
    --input-rgb: 229, 231, 235; 
  }
}

/* Apply preference styling to UI elements */
.label, label, .btn-label, h1, h2, h3, h4, h5, h6, button, .menu-item {
  font-family: var(--font-family-labels) !important;
  font-size: var(--font-size-base-labels) !important;
}

.user-content, .editable-content, textarea, input, [contenteditable="true"], .user-text {
  font-family: var(--font-family-user-text) !important;
  font-size: var(--font-size-base-user-text) !important; 
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 16px;
  background-color: transparent;
}

::-webkit-scrollbar-track {
  background-color: hsl(var(--background)); 
  border-left: 1px solid hsl(var(--border)); 
}

::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted)); 
  border: 4px solid hsl(var(--background)); 
  border-radius: var(--radius); 
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground)); 
}

/* Enhanced outline styles */
.outline-item {
  transition: background-color 0.2s ease;
  position: relative; 
  min-width: 0; 
  display: flex; 
  flex-direction: column; 
  max-width: 100%; 
}

.outline-item > div {
  width: auto; 
  max-width: 100%; 
  min-width: 0; 
  overflow-x: visible; 
}

.outline-list-container {
  position: relative;
  overflow: visible;
}

.panel.relative.flex-1 {
  position: relative;
}

/* Style for action buttons to make them stand out on hover */
.outline-item .action-buttons {
  background-color: hsl(var(--card) / 0.97); 
  border-radius: var(--radius-sm, calc(var(--radius) - 4px)); 
  box-shadow: 0 0 5px hsl(var(--foreground) / 0.05); 
}

/* Style for react-beautiful-dnd placeholders */
[data-rbd-placeholder-context-id] {
  border: 1px dashed hsl(var(--border)) !important; 
  background-color: hsl(var(--muted)) !important; 
  margin: 2px 0 !important;
  min-height: 30px !important;
}

:root {
  /* RGB fallbacks / JS interop variables */
  --background-rgb: 255, 255, 255;
  --foreground-rgb: 23, 23, 23;
  /* --primary-rgb is already defined in the first :root block, tied to --custom-primary-rgb */
  --primary-foreground-rgb: 255, 255, 255;
  --secondary-rgb: 245, 245, 245;
  --muted-rgb: 245, 245, 245;
  --muted-foreground-rgb: 115, 115, 115;
  --accent-rgb: 245, 245, 245;
  --accent-foreground-rgb: 23, 23, 23;
  --card-rgb: 255, 255, 255;
  --card-foreground-rgb: 23, 23, 23;
  --destructive-rgb: 239, 68, 68;
  --destructive-foreground-rgb: 255, 255, 255;
  --border-rgb: 229, 231, 235;
  --input-rgb: 229, 231, 235;
  /* --ring-rgb is already defined, tied to --custom-primary-rgb */
}

.dark {
  /* Dark theme HSL values. These are overridden by use-preferences.tsx for custom themes. */
  --background: 0 0% 12%;
  --foreground: 0 0% 83%;
  --card: 0 0% 15%;
  --card-foreground: 0 0% 80%;
  --popover: 0 0% 12%;
  --popover-foreground: 0 0% 83%;
  --primary: 207 83% 33%; 
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 15%;
  --secondary-foreground: 0 0% 83%;
  --muted: 0 0% 20%;
  --muted-foreground: 0 0% 52%;
  --accent: 0 0% 17%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 70% 50%; 
  --destructive-foreground: 0 0% 100%;
  --border: 240 5% 26%;
  --input: 240 5% 26%;
  --ring: 207 83% 33%; 

  --outline-item-background-even: var(--background);
  --outline-item-background-odd: hsl(var(--muted));
}

@layer base {
  /* Remove global border application to prevent unwanted borders */
  
  body {
    @apply font-sans antialiased bg-background text-foreground;
    width: 100vw;
    overflow-x: hidden;
  }

  /* Override container class to use max available width with consistent margins */
  .container {
    max-width: 100% !important;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Enhanced visual feedback for drag and drop */
body.dragging-mode,
body.dragging-mode * {
  cursor: grabbing !important;
  user-select: none !important;
}

/* Force grabbing cursor on entire document during drags */
html.outline-dragging-active,
html.outline-dragging-active * {
  cursor: grabbing !important;
  user-select: none !important;
}

/* Ensure grabbing cursor even for interactive elements during drag */
html.outline-dragging-active button,
html.outline-dragging-active a,
html.outline-dragging-active input,
html.outline-dragging-active .action-buttons * {
  cursor: grabbing !important;
}

/* Override any hover effects during drag */
.outline-dragging-active .outline-item:hover {
  cursor: grabbing !important;
}

.outline-item.dragging {
  opacity: 0.4;
}

/* Ensure drag overlay is always visible */
.dnd-kit-drag-overlay {
  z-index: 1000 !important;
}

/* Prevent text selection during drag */
html.outline-dragging-active .outline-item {
  user-select: none !important;
}

/* Visual styles for drag targets */
.drop-target-before {
  border-top: 2px solid hsl(var(--primary)) !important; 
  margin-top: -2px;
  z-index: 5;
}

.drop-target-after {
  border-bottom: 2px solid hsl(var(--primary)) !important; 
  margin-bottom: -2px;
  z-index: 5;
}

.drop-target-parent {
  background-color: hsl(var(--primary) / 0.1) !important; 
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.5) !important; 
  z-index: 5;
  position: relative;
}

.drop-target-parent::after {
  content: "";
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: hsl(var(--primary)); 
  border-radius: var(--radius-sm, calc(var(--radius) - 4px)); 
}

/* Add a visual child indicator */
.drop-target-parent::before {
  content: "➦";
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(var(--primary)); 
  font-size: 16px;
  opacity: 0.8;
  z-index: 10;
}

/* Hierarchical highlight path - highlights all parent items when hovering */
.highlighted-path {
  background: linear-gradient(90deg, hsl(var(--primary) / 0.08) 0%, hsl(var(--primary) / 0.01) 40%, transparent 60%) !important; 
  border: none !important;
  position: relative;
  z-index: 2;
  transition: all 0.15s ease-in-out !important;
}

/* Custom border gradients to fade out borders - simpler version with better browser support */
.highlighted-path:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border-radius: inherit;
  border: 1px solid transparent;
  border-image: linear-gradient(90deg, 
    hsl(var(--primary) / 0.3) 0%, 
    hsl(var(--primary) / 0.1) 30%, 
    transparent 50%
  ) 1; 
  border-right: none;
}

.highlighted-path:after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: hsl(var(--primary)); 
  border-radius: 0 2px 2px 0; /* Consider var(--radius-sm) */
  opacity: 0.8;
}

/* Add a subtle pulsing animation to the leftmost item in the path */
.highlighted-path:first-child:after {
  animation: pulseBorder 2s infinite ease-in-out;
}

@keyframes pulseBorder {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* Minimap styling */
/* Most !important flags are kept here as minimap styling can be very sensitive to overrides */
/* due to scaling and potential library conflicts. Colors are changed to use HSL variables. */
.minimap-content {
  transform: scale(0.4);
  transform-origin: top left;
  width: 250%;
  height: auto;
}

.minimap-content * {
  pointer-events: none !important;
  font-family: inherit !important;
  border-color: hsl(var(--border) / 0.3) !important;
}

/* Make sure outline items are visible in minimap */
.minimap-content .outline-item {
  display: block !important;
  height: auto !important;
  min-height: 10px !important;
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  margin-bottom: 3px !important;
  border-radius: var(--radius-sm, 3px) !important; /* Use theme variable with fallback */
  padding: 0 !important;
  width: 96% !important;
  border-left: 3px solid hsl(var(--primary)) !important;
  position: relative !important;
  box-shadow: 0 1px 2px hsl(var(--foreground) / 0.1) !important;
}

/* Clear visual structure for minimap items */
.minimap-content .outline-item-content {
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Different colors for nested items */
.minimap-content .outline-item .outline-item {
  border-left-color: hsl(var(--primary) / 0.8) !important;
  background-color: hsl(var(--muted)) !important;
  margin-left: 3px !important;
  width: 94% !important;
}

.minimap-content .outline-item .outline-item .outline-item {
  border-left-color: hsl(var(--primary) / 0.6) !important;
  background-color: hsl(var(--accent)) !important;
  margin-left: 3px !important;
  width: 92% !important;
}

/* Custom styles for dark theme minimap */
/* Rely on .dark class setting HSL variables for card, primary, muted, accent, foreground, border */
.dark .minimap-content .outline-item {
  /* background-color, color, border-left-color should correctly use dark theme HSL variables */
  box-shadow: 0 1px 2px hsl(var(--foreground) / 0.2) !important; /* Adjusted alpha for dark */
}

/* .dark .minimap-content .outline-item-content and nested items will also inherit dark theme HSL vars */

.dark .minimap-content .outline-item-number {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .minimap-content .outline-item-title {
  color: hsl(var(--foreground)) !important;
}

.dark .minimap-viewport-indicator {
  background-color: hsl(var(--muted-foreground) / 0.2) !important;
  border-color: hsl(var(--border)) !important;
}

.minimap-content .outline-item-children {
  margin-left: 8px !important;
  padding-left: 0 !important;
  border-left: 1px solid hsl(var(--border) / 0.3) !important;
}

/* .dark .minimap-content .outline-item-children will use .dark --border HSL variable with its alpha */

/* Hide unnecessary elements - this rule is critical for minimap display */
.minimap-content button,
.minimap-content .drag-handle,
.minimap-content input,
.minimap-content .action-button,
.minimap-content .action-buttons,
.minimap-content .outline-actions,
.minimap-content svg,
.minimap-content .divider,
.minimap-content .note-content,
.minimap-content iframe,
.minimap-content form {
  display: none !important;
}

/* Simplify colors for better visibility */
.minimap-content .outline-item-number {
  color: hsl(var(--foreground) / 0.7) !important;
  font-weight: bold !important;
  font-size: 6px !important;
  display: inline-block !important;
  min-width: 8px !important;
  opacity: 0.85 !important;
}

.minimap-content .outline-item-title {
  color: hsl(var(--foreground)) !important; /* Ensure this uses foreground directly */
  font-weight: normal !important;
  font-size: 6px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

/* Panel styling with fixed header */
.panel {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-radius: 0; 
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid hsl(var(--border));
  transition: border-color 0.15s ease, box-shadow 0.15s ease, background-color 0.15s ease;
}

/* Panel hover state for border */
.panel:hover {
  border-color: hsl(var(--primary));
  border-width: 1.5px;
}

/* Style the scrollable content area */
/* Removed padding-right: 20px !important; - should be handled by component layout */
.panel > div[class*="overflow-y-auto"] {
  scrollbar-gutter: stable;
  margin-top: 0;
  flex-grow: 1;
}

/* Style scrollbars to work well with minimap */
.panel > div[class*="overflow-y-auto"]::-webkit-scrollbar {
  width: 6px;
}

.panel > div[class*="overflow-y-auto"]::-webkit-scrollbar-track {
  background: transparent; /* Track is transparent, main scrollbar track handles color */
  margin-top: 4px; /* Keep margins for aesthetics */
  margin-bottom: 4px;
}

.panel > div[class*="overflow-y-auto"]::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted));
  border-radius: var(--radius-sm, 20px); /* Use theme variable with fallback */
}

.panel > div[class*="overflow-y-auto"]::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground));
  opacity: 0.7;
}

/* Fixed header styling */
.panel > div:first-child {
  position: sticky;
  top: 0;
  background-color: hsl(var(--background));
  z-index: 20;
  border-bottom: 1px solid hsl(var(--border)); 
  flex-shrink: 0;
  transition: background-color 0.15s ease, border-color 0.15s ease;
}

/* Header hover effects */
.panel:hover > div:first-child {
  background-color: hsl(var(--accent));
  border-bottom-color: hsl(var(--primary)); 
  border-bottom-width: 1.5px;
}

/* Remove padding from the helper text in outline panel */
/* This is a very specific selector. If possible, this should be handled by utility classes on the component itself. */
/* Keeping for now, but marked for potential future refactor at component level. */
.outline-container > div:first-child {
  padding-left: 0;
  padding-right: 0;
}

/* Style the outline items */
.outline-item {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: hsl(var(--foreground));
}

.outline-item-number {
  color: hsl(var(--muted-foreground));
  font-size: 0.875rem;
  font-weight: 400;
  margin-right: 0.5rem;
}

.outline-item-title {
  color: hsl(var(--foreground));
  font-weight: 400;
}

/* Adjust the vertical lines connecting outline items */
.outline-item .connector-line {
  background-color: hsl(var(--border));
  width: 1px;
}

/* Outline Drag icon opacity styles are fine. */
.outline-item .drag-handle {
  opacity: 0.5;
  transition: opacity 0.2s;
}

.outline-item:hover .drag-handle {
  opacity: 1;
}

/* Add a highlight effect for items in the active path */
.highlighted-path {
  position: relative; /* This is fine for pseudo-element positioning */
}

/* Dark theme specific overrides have been significantly reduced. */
/* The remaining rules below are for specific components or behaviors in dark mode. */

/* Ensure Radix/Shadcn components like Menus, Dialogs, Popovers use theme variables correctly. */
/* Removed !important as theme variables should be sufficient. */
.dark [role="menu"],
.dark [role="listbox"],
.dark [data-radix-popper-content-wrapper] div { /* This selector for Radix popper can be broad. Consider component-specific styling if issues arise. */
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  border-color: hsl(var(--border));
}

.dark [role="menuitem"]:hover,
.dark [role="option"]:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* General dark theme prose adjustments. These are fine. */
.dark .prose {
  color: hsl(var(--foreground));
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4,
.dark .prose h5,
.dark .prose h6 {
  color: hsl(var(--foreground));
}

.dark .prose a {
  color: hsl(var(--primary));
}

.dark .prose code {
  background-color: hsl(var(--muted));
  /* Ensure primary color provides enough contrast on muted background in dark mode */
  /* Or consider a different variable for code text color in dark mode if needed */
  color: hsl(var(--primary)); 
}

.dark .prose blockquote {
  border-color: hsl(var(--border));
  color: hsl(var(--muted-foreground));
}

/* Caret color for contenteditables in dark theme. Fine. */
.dark [contenteditable="true"] {
  caret-color: hsl(var(--foreground));
}

/* Custom scrollbar for dark theme. Fine. */
.dark ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.dark ::-webkit-scrollbar-track {
  background: hsl(var(--background)); 
}

.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.5); 
  border-radius: var(--radius-sm, 3px); /* Using a theme variable with fallback */
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.7);
}

/* Minimap note styles */
.minimap-note-preview-wrapper {
  /* Add a small border to represent the note area */
  border: 1px solid #ccc;
  /* Reduce padding and margin to save space */
  padding: 1px;
  margin: 1px 0;
  /* Ensure it doesn't take too much space */
  max-height: 10px; /* Adjust as needed */
  overflow: hidden;
}

.minimap-note {
  /* Make notes smaller and less prominent */
  font-size: 0.8em; /* Relative to parent, which is already small */
  background-color: #f0f0f0; /* Light gray background */
  border: none; /* Remove existing borders if any */
  padding: 0.5px;
  margin: 0.5px;
  /* Ensure notes don't expand too much */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; /* Add ellipsis if content overflows */
  display: block; /* Make it block to control height if needed */
  height: 3px; /* Fixed height for visual representation */
  min-height: 3px; /* Fixed height for visual representation */
}

/* Hide the text content of notes in the minimap */
.minimap-note .outline-item-note-text {
  display: none;
}

/* Selection color in dark mode. !important is often needed here. */
.dark ::selection {
  background-color: hsl(var(--primary)) !important; 
  color: hsl(var(--primary-foreground)) !important;
}

/*
  Cleanup notes:
  - Removed `padding-right: 20px !important` from panel scrollable area.
  - Corrected minor syntax issues (extra `)`).
  - Ensured HSL color usage for panel and outline items.
  - Minimized `!important` in dark theme Radix component overrides.
  - Added a comment about dark mode code color contrast.
  - Used theme variable for scrollbar thumb radius in dark mode.
*/
/* Radix/Shadcn component base styles - ensuring HSL variables and minimizing !important */
[role="dialog"] {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border: 1px solid hsl(var(--border)); 
}

[role="dialog"] .bg-background { 
  background-color: hsl(var(--card)); 
}

[role="menu"],
[role="listbox"],
[data-radix-popper-content-wrapper] div { 
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  border: 1px solid hsl(var(--border));
}

[role="menuitem"],
[role="option"] {
  color: hsl(var(--popover-foreground)); 
}

[role="menuitem"]:hover,
[role="option"]:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground)); 
}

[role="status"][data-state] { /* Toast notifications */
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

[data-radix-toast-viewport] {
  background-color: transparent !important; /* This !important is likely needed for toast viewport positioning/layering */
}

[role="tooltip"] {
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  border: 1px solid hsl(var(--border));
}

/* Dark theme specific overrides for Radix/Shadcn components if variable scoping isn't enough. */
/* Removing !important where possible. */
.dark [role="dialog"] {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
}
.dark [role="menu"],
.dark [role="listbox"],
.dark [data-radix-popper-content-wrapper] div {
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  border-color: hsl(var(--border));
}
.dark [role="menuitem"]:hover,
.dark [role="option"]:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}
.dark [role="tooltip"] {
  background-color: hsl(var(--popover)); 
  color: hsl(var(--popover-foreground));
  border-color: hsl(var(--border));
}


/* Rich text editor specific styles */
.rich-text-editor-container {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Light theme specific override for prose. */
:root:not(.dark) .prose {
  color: hsl(222.2 47.4% 11.2%); /* slate-900 equivalent */
}

/* Dark theme specific override for contenteditable within rich text editor */
.dark .rich-text-editor-container [contenteditable] {
  color: hsl(var(--foreground)) !important; /* !important likely needed for tiptap */
  background-color: hsl(var(--background)) !important; /* !important likely needed for tiptap */
}

/* Dark theme prose adjustments */
.dark .prose {
  color: hsl(var(--foreground));
}
.dark .prose h1, .dark .prose h2, .dark .prose h3, .dark .prose h4, .dark .prose h5, .dark .prose h6 {
  color: hsl(var(--foreground));
}
.dark .prose a {
  color: hsl(var(--primary));
}
.dark .prose code {
  background-color: hsl(var(--muted));
  color: hsl(var(--primary)); /* Consider contrast */
}
.dark .prose blockquote {
  border-color: hsl(var(--border));
  color: hsl(var(--muted-foreground));
}

/* Dark theme caret and selection */
.dark [contenteditable="true"] {
  caret-color: hsl(var(--foreground));
}
.dark ::selection {
  background-color: hsl(var(--primary)) !important; /* !important for browser override */
  color: hsl(var(--primary-foreground)) !important;
}

/* Dark theme scrollbar */
.dark ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
.dark ::-webkit-scrollbar-track {
  background: hsl(var(--background)); 
}
.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.5); 
  border-radius: var(--radius-sm, 3px);
}
.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.7);
}

/* Ensure scrollbar visibility for .metadata-editor-scroll-area */
.metadata-editor-scroll-area [data-radix-scroll-area-viewport] {
  scrollbar-width: auto !important;
  -ms-overflow-style: auto !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: 100% !important; /* Added this line */
}

.metadata-editor-scroll-area [data-radix-scroll-area-viewport]::-webkit-scrollbar {
  display: block !important;
  width: 8px;
}

.metadata-editor-scroll-area [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 4px;
}

.metadata-editor-scroll-area [data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
  background-color: hsl(var(--background));
  border-radius: 4px;
}

import React, { useState, useEffect } from 'react';

interface RemoteCursorProps {
  userId: number;
  position: number;
  containerRef: React.RefObject<HTMLElement>;
  username?: string;
  color?: string;
}

export function RemoteCursor({ userId, position, containerRef, username = 'User', color = 'hsl(var(--icon-blue-hsl))' }: RemoteCursorProps) {
  const [cursorPos, setCursorPos] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  const [visible, setVisible] = useState(true);

  // Position the cursor based on the container element and position
  useEffect(() => {
    if (!containerRef.current) return;

    const calculateCursorPosition = () => {
      try {
        const container = containerRef.current;
        if (!container) return;

        // Get all text nodes within the container
        const textNodes: Node[] = [];
        const walker = document.createTreeWalker(
          container,
          NodeFilter.SHOW_TEXT,
          null
        );

        let node;
        while ((node = walker.nextNode())) {
          textNodes.push(node);
        }

        let currentPosition = 0;
        let targetNode: Node | null = null;
        let targetOffset = 0;

        // Find the text node and offset for the cursor position
        for (const node of textNodes) {
          if (currentPosition + node.textContent!.length >= position) {
            targetNode = node;
            targetOffset = position - currentPosition;
            break;
          }
          currentPosition += node.textContent!.length;
        }

        if (targetNode) {
          const range = document.createRange();
          range.setStart(targetNode, targetOffset);
          range.collapse(true);

          const rect = range.getBoundingClientRect();
          const containerRect = container.getBoundingClientRect();

          setCursorPos({
            top: rect.top - containerRect.top,
            left: rect.left - containerRect.left
          });
        }
      } catch (error) {
        console.error('Error calculating cursor position:', error);
      }
    };

    calculateCursorPosition();

    // Blink effect for cursor
    const interval = setInterval(() => {
      setVisible(prev => !prev);
    }, 500);

    return () => clearInterval(interval);
  }, [containerRef, position]);

  return (
    <div
      className="absolute pointer-events-none"
      style={{
        top: `${cursorPos.top}px`,
        left: `${cursorPos.left}px`,
        transition: 'top 0.3s ease, left 0.3s ease',
        zIndex: 100
      }}
    >
      <div 
        className={`h-5 w-0.5 ${visible ? 'opacity-100' : 'opacity-50'}`}
        style={{ backgroundColor: color }}
      />
      
      <div 
        className="absolute -top-6 left-0 px-2 py-0.5 text-xs text-primary-foreground rounded whitespace-nowrap"
        style={{ backgroundColor: color }}
      >
        {username || `User ${userId}`}
      </div>
    </div>
  );
}
# Architecture Overview

## Overview

This application is a full-stack web application for document creation, editing, and collaboration. It follows a client-server architecture with a clear separation between frontend and backend components. The application is built with modern web technologies and emphasizes collaboration features.

## System Architecture

The system follows a modern web application architecture with the following key components:

1. **Frontend**: A React-based single-page application (SPA) built with TypeScript, using Vite as the build tool.
2. **Backend**: A Node.js/Express server providing RESTful API endpoints.
3. **Database**: PostgreSQL database with Drizzle ORM for data modeling and queries.
4. **Authentication**: Session-based authentication using Passport.js with local strategy.
5. **Real-time Collaboration**: WebSocket-based collaboration features (via the `ws` package).

### Architecture Diagram

```
┌─────────────────┐         ┌────────────────┐         ┌─────────────┐
│                 │         │                │         │             │
│  React Frontend │ ◄─────► │  Express API   │ ◄─────► │  PostgreSQL │
│  (Vite + ShadCN)│         │  (Node.js)     │         │  Database   │
│                 │         │                │         │             │
└─────────────────┘         └────────────────┘         └─────────────┘
        ▲                           ▲
        │                           │
        │                           │
        │                           │
        ▼                           ▼
┌─────────────────┐         ┌────────────────┐
│                 │         │                │
│  WebSocket      │ ◄─────► │  External      │
│  (Collaboration)│         │  Services      │
│                 │         │  (Stripe, etc.)│
└─────────────────┘         └────────────────┘
```

## Key Components

### Frontend Architecture

The frontend is built with:

1. **React**: Core UI library
2. **TypeScript**: Type-safe JavaScript
3. **ShadCN UI**: Component library based on Radix UI
4. **TailwindCSS**: Utility-first CSS framework
5. **React Router (Wouter)**: Lightweight client-side routing
6. **React Query (TanStack Query)**: Data fetching and state management
7. **React Hook Form**: Form handling and validation
8. **Zod**: Schema validation
9. **DND Kit**: Drag-and-drop functionality

The frontend is organized around feature-based modules:

- `components/`: UI components, including ShadCN UI components
- `hooks/`: Custom React hooks for shared logic
- `lib/`: Utility functions and shared code
- `pages/`: Page components representing routes

### Backend Architecture

The backend is built with:

1. **Node.js**: JavaScript runtime
2. **Express**: Web server framework
3. **TypeScript**: Type-safe JavaScript
4. **Drizzle ORM**: Database ORM for PostgreSQL
5. **Passport.js**: Authentication middleware
6. **WebSockets (ws)**: Real-time communication

The backend follows a layered architecture:

- **Routes**: API endpoint definitions
- **Controllers**: Request handling logic
- **Services**: Business logic
- **Storage**: Data access layer for database operations

### Database Architecture

The database is built on PostgreSQL with Drizzle ORM providing the schema definition and query builder. Key tables include:

1. **users**: User account information and preferences
2. **documents**: Document metadata
3. **session**: User sessions for authentication

### Authentication Mechanism

The application uses session-based authentication with:

1. **Passport.js**: Authentication middleware
2. **Local Strategy**: Username/password authentication
3. **Express Session**: HTTP session management
4. **Connect PG Simple**: PostgreSQL session store
5. **Crypto**: Secure password hashing with scrypt

## Data Flow

### Document Creation and Editing

1. User logs in through the authentication system
2. User creates/edits document through the UI
3. Changes are sent to the server via REST API
4. Server validates and persists changes to the database
5. Real-time updates are broadcast to collaborators via WebSockets

### Collaboration

1. Multiple users can connect to a document
2. User actions (outline updates, content updates, cursor movements) are synchronized via WebSockets
3. The system tracks user presence and displays it to collaborators
4. Document history is maintained for version control

### Outline Management

The application features a sophisticated outline management system with:

1. **Drag-and-Drop Reordering**: Native HTML5 drag-and-drop implementation allowing users to:
   - Drag items using visual grip handles (⋮⋮)
   - Drop items before/after other items to change sibling order
   - Drop items onto other items to create parent-child relationships
   - Support for deeply nested outline structures

2. **Visual Drop Zones**: Theme-consistent blue drop zones that provide clear feedback:
   - "DROP BEFORE" and "DROP AFTER" zones for sibling positioning
   - Child drop zones with blue outline for parent-child relationships
   - Hover states with "+" buttons for quick sibling addition

3. **Minimap Integration**: A functional minimap that:
   - Provides visual overview of document structure
   - Syncs with main content scrolling
   - Shows viewport indicator for current view
   - Updates in real-time as outline changes

4. **Auto-numbering**: Automatic outline numbering that updates when items are moved or added

### Document Access Request System

This feature provides a formal system for users to request access to documents they don't have permission to view. When a user navigates to a shared document link and is denied access, they are presented with a page where they can request access from the document owner.

Here's the workflow:

1.  **Request Initiation:** A user without access to a document is redirected to a "Request Access" page. Clicking the "Request Access" button sends a request to the server.

2.  **Backend Processing:** The server creates a new entry in the `document_access_requests` table. This entry links the user, the document, and the document owner, and has a default status of "pending."

3.  **Owner Notification & Management:** The document owner sees a new "Access Requests" tab on their dashboard. This tab lists all pending requests, showing who is requesting access to which document.

4.  **Approval/Denial:** The owner can approve or deny each request.
    *   **Deny:** The request's status is updated to "denied."
    *   **Approve:** The owner can choose to grant either "view" or "edit" permissions. The request's status is updated to "approved," and a new entry is added to the `document_shares` table, officially granting the user access to the document.

This system improves collaboration by providing a clear and manageable process for handling access permissions, moving beyond simple link sharing to a more robust and secure model.

### Document Sharing

The application supports two primary methods of sharing documents:

1.  **Direct User Invitation**: Document owners can directly invite other registered users to collaborate.
    *   This is managed in the "Collaboration" tab of the Share Modal.
    *   Owners can assign either 'view' or 'edit' permissions.
    *   An entry is created in the `document_shares` table, establishing a permanent link between the user and the document.
    *   Invited users see the document on their dashboard under "Shared With Me".

2.  **Secure Shareable Links (Token-Based)**: To share a document with users without inviting them directly, owners can generate secure, single-use links.
    *   **Generation**: From the "Share Link" tab in the Share Modal, an owner can create a unique link for either 'view' or 'edit' permissions. This action calls a secure API endpoint (`POST /api/documents/:documentId/share-links`).
    *   **Token Creation**: The backend generates a cryptographically secure, unique token and stores it in the `share_tokens` table, linking it to the document and the specified permission level.
    *   **Access and Redemption**: When a new, logged-in user clicks the link (`/document/:documentId?token=...`), the backend validates the token.
        *   If the token is valid, the system grants the user access by creating a record for them in the `document_shares` table.
        *   The token is then immediately deleted, ensuring it cannot be used again to grant access to another user.
    *   **Subsequent Access**: After redeeming a token, the user has permanent access based on the permission granted, and they can access the document from their dashboard without needing the token again. This flow replaces a previous, less secure method that relied on `?mode=` query parameters.
    *   **Unauthenticated Access**: If a user clicks a share link but is not logged in, they are redirected to the login page. After successfully logging in, they are automatically redirected back to the document they were trying to access.

## External Dependencies

The application integrates with several external services:

1. **Stripe**: Payment processing for premium subscriptions
2. **NeonDB**: PostgreSQL database provider (serverless)
3. **Replit**: Deployment platform with dev environment

## Deployment Strategy

The application is configured for deployment on Replit with:

1. **Development**: Uses Vite's dev server with HMR
2. **Production**: Pre-builds frontend assets with Vite and serves them statically
3. **Database**: Connects to NeonDB PostgreSQL database
4. **Environment**: Uses environment variables for configuration

The deployment process:
1. Build frontend assets with `npm run build`
2. Bundle server with esbuild
3. Start the Node.js server to serve both API and static assets

## Security Considerations

1. **Password Storage**: Passwords are hashed using scrypt with salts
2. **Session Management**: Sessions are stored in the database with secure cookies
3. **Authentication**: Routes are protected with authentication middleware
4. **CSRF Protection**: Implemented via same-site cookies and token validation

## Testing Strategy

The repository doesn't have explicit test files, but the architecture supports testing through:

1. **Component Testing**: UI components can be tested in isolation
2. **API Testing**: Backend endpoints can be tested independently
3. **Integration Testing**: End-to-end workflows can be tested across layers

## Future Considerations

Areas for potential architectural evolution:

1. **Microservices**: Potentially splitting into more focused services
2. **Serverless**: Moving more components to serverless architecture
3. **GraphQL**: Potentially replacing REST API with GraphQL for more flexible data fetching
4. **Offline Support**: Adding offline capabilities with service workers
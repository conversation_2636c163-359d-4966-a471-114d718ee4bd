import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpgrade: (plan: 'monthly' | 'annual') => void;
}

export function SubscriptionModal({ isOpen, onClose, onUpgrade }: SubscriptionModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-card-foreground">
            Upgrade to Inspire Premium
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Unlock AI-powered features to enhance your writing
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 mt-6">
          <div className="border rounded-lg p-4 flex items-start gap-3">
            <div className="bg-primary text-primary-foreground p-2 rounded-full">
              <i className="ri-magic-line"></i>
            </div>
            <div>
              <h3 className="font-medium text-card-foreground">AI-Generated Drafts</h3>
              <p className="text-sm text-muted-foreground">
                Generate high-quality drafts from your outline and notes with one click
              </p>
            </div>
          </div>
          
          <div className="border rounded-lg p-4 flex items-start gap-3">
            <div className="bg-primary text-primary-foreground p-2 rounded-full">
              <i className="ri-search-eye-line"></i>
            </div>
            <div>
              <h3 className="font-medium text-card-foreground">Enhanced Plagiarism Checking</h3>
              <p className="text-sm text-muted-foreground">
                Unlimited access to our comprehensive plagiarism detection engine
              </p>
            </div>
          </div>
          
          <div className="border rounded-lg p-4 flex items-start gap-3">
            <div className="bg-primary text-primary-foreground p-2 rounded-full">
              <i className="ri-translate-2"></i>
            </div>
            <div>
              <h3 className="font-medium text-card-foreground">Advanced Editing Tools</h3>
              <p className="text-sm text-muted-foreground">
                Rewrite, simplify, or enhance your text with AI assistance
              </p>
            </div>
          </div>
        </div>
        
        <div className="mt-6 space-y-3">
          <Button 
            className="w-full" // Removed bg-accent-500 hover:bg-accent-600 to use default primary button style
            onClick={() => onUpgrade('monthly')}
          >
            Upgrade Now - $9.99/month
          </Button>
          <Button 
            variant="outline"
            className="w-full"
            onClick={() => onUpgrade('annual')}
          >
            Annual Plan - $89.99/year (25% off)
          </Button>
        </div>
        
        <p className="text-xs text-muted-foreground text-center mt-4">
          Subscription can be canceled anytime. See our{' '}
          <a href="#" className="text-primary hover:underline">
            terms and conditions
          </a>.
        </p>
      </DialogContent>
    </Dialog>
  );
}

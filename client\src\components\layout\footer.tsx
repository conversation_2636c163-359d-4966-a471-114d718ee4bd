import React from 'react';
import { Button } from '@/components/ui/button';

interface FooterProps {
  wordCount: number;
  saveStatus: 'saved' | 'saving' | 'unsaved';
  onReportIssue: () => void;
  onHelp: () => void;
}

export function Footer({ wordCount, saveStatus, onReportIssue, onHelp }: FooterProps) {
  return (
    <footer className="bg-white border-t border-neutral-200 py-2 px-4 text-sm text-neutral-500">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1">
            {saveStatus === 'saved' && (
              <>
                <i className="ri-check-line text-success-500"></i>
                <span>Saved</span>
              </>
            )}
            {saveStatus === 'saving' && (
              <>
                <i className="ri-loader-2-line animate-spin text-primary-500"></i>
                <span>Saving...</span>
              </>
            )}
            {saveStatus === 'unsaved' && (
              <>
                <i className="ri-error-warning-line text-warning-500"></i>
                <span>Unsaved changes</span>
              </>
            )}
          </div>
          <div>Word count: {wordCount}</div>
        </div>
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost"
            size="sm"
            className="flex items-center gap-1 text-neutral-500 hover:text-primary-600"
            onClick={onReportIssue}
          >
            <i className="ri-error-warning-line"></i>
            <span>Report Issue</span>
          </Button>
          <Button 
            variant="ghost"
            size="sm"
            className="flex items-center gap-1 text-neutral-500 hover:text-primary-600"
            onClick={onHelp}
          >
            <i className="ri-question-line"></i>
            <span>Help</span>
          </Button>
        </div>
      </div>
    </footer>
  );
}

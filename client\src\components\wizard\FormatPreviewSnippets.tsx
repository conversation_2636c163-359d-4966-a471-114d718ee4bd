import React from 'react';

interface PreviewProps {
  title?: string;
  authorName?: string;
  year?: string;
  institution?: string;
  signers?: Array<{ name?: string; jobTitle?: string }>;
  dedicationText?: string;
}

export const CoverPreview: React.FC<Pick<PreviewProps, 'title' | 'authorName'>> = ({ title, authorName }) => {
  return (
    <div className="border rounded p-3">
      <h4 className="text-xs font-semibold text-center mb-2">Cover</h4>
      <div className="aspect-w-8.5 aspect-h-11 bg-card border">
        <div className="flex flex-col items-center justify-center p-4 text-center">
          <div className="text-xs mb-2 truncate font-semibold">{title || "[Document Title]"}</div>
          <div className="text-[0.5625rem] mb-3">A Project submitted to the faculty of<br />The School of Humanities</div> {/* 9px, mb-3 */}
          <div className="text-[0.5625rem] mb-3">In partial fulfillment of the<br />requirements for the degree<br />Doctor of Philosophy</div> {/* 9px, mb-3 */}
          <div className="text-[0.5625rem] mb-3">by<br />{authorName || "[Author Name]"}<br />{"[Date]"}</div> {/* 9px, mb-3 */}
        </div>
      </div>
    </div>
  );
};

export const CopyrightPreview: React.FC<Pick<PreviewProps, 'year' | 'authorName' | 'institution'>> = ({ year, authorName, institution }) => {
  return (
    <div className="border rounded p-3">
      <h4 className="text-xs font-semibold text-center mb-2">Copyright</h4>
      <div className="aspect-w-8.5 aspect-h-11 bg-card border">
        <div className="flex flex-col items-center justify-center p-4 text-center">
          <div className="text-[0.5625rem] mb-2">Copyright © {year || new Date().getFullYear().toString()} {authorName || "[Author Name]"}</div> {/* 9px */}
          <div className="text-[0.5625rem]">All rights reserved. {institution || "[Institution Name]"}</div> {/* 9px */}
        </div>
      </div>
    </div>
  );
};

export const ApprovalSheetPreview: React.FC<Pick<PreviewProps, 'title' | 'authorName' | 'signers'>> = ({ title, authorName, signers }) => {
  const displaySigners = (signers && signers.length > 0 
    ? signers 
    : [
        { name: "[Signer Name]", jobTitle: "[Signer Title]" },
        { name: "[Signer Name]", jobTitle: "[Signer Title]" },
      ]
  ).slice(0, 3); // Show a maximum of 3 signers for preview

  return (
    <div className="border rounded p-3">
      <h4 className="text-xs font-semibold text-center mb-2">Approval Sheet</h4>
      <div className="aspect-w-8.5 aspect-h-11 bg-card border">
        <div className="flex flex-col p-4">
          <div className="text-xs text-center mb-2 truncate font-semibold">{title || "[Document Title]"}</div>
          <div className="text-[0.5625rem] text-center mb-2">{authorName || "[Author Name]"}</div> {/* 9px, mb-2 */}
          <div className="space-y-2"> {/* Reduced space-y-3 to space-y-2 */}
            {displaySigners.map((signer, i) => (
              <div key={i} className="text-[0.5625rem]"> {/* 9px */}
                <div>{(signer.name || `[Signer ${i+1}]`)}, {signer.jobTitle || "[Signer Title]"}</div>
                <div className="border-t border-gray-400 mt-1 w-40"></div>
              </div>
            ))}
            <div className="text-[0.5625rem] mt-3"> {/* 9px, Reduced mt-5 pt-3 to mt-3 */}
              <div>Date</div>
              <div className="border-t border-gray-400 mt-1 w-20"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const DedicationPreview: React.FC<Pick<PreviewProps, 'dedicationText'>> = ({ dedicationText }) => {
  return (
    <div className="border rounded p-3">
      <h4 className="text-xs font-semibold text-center mb-2">Dedication</h4>
      <div className="aspect-w-8.5 aspect-h-11 bg-card border">
        <div className="flex flex-col items-center justify-center p-4 text-center">
          <div className="text-xs font-semibold mb-2">Dedication</div>
          <div className="text-[0.5625rem] leading-relaxed"> {/* 9px */}
            {dedicationText || "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."}
          </div>
        </div>
      </div>
    </div>
  );
};

// routes/github-webhook.ts
import "dotenv/config";
import express from 'express';
import crypto from 'crypto';
import execSh from 'exec-sh';

const exec = execSh.promise;
export const githubWebhookRouter = express.Router();

// Use express.raw ONLY for this route
githubWebhookRouter.post(
  '/',
  express.raw({ type: 'application/json' }),
  async (req, res) => {
    try {
      const sig = (req.get('X-Hub-Signature-256') || '').trim();

      // Sanity check that req.body is a Buffer
      if (!(req.body instanceof Buffer)) {
        console.error('❌ Webhook body is not a Buffer');
        return res.status(400).send('Invalid body type');
      }

      const hmac = crypto
        .createHmac('sha256', process.env.GITHUB_WEBHOOK_SECRET!)
        .update(req.body)
        .digest('hex');

      if (sig !== `sha256=${hmac}`) {
        console.warn('❌ Webhook signature mismatch');
        return res.status(401).send('Bad signature');
      }

      // Parse the webhook payload
      const payload = JSON.parse(req.body.toString());
      const eventType = req.get('X-GitHub-Event');

      // Only process pull_request events
      if (eventType !== 'pull_request') {
        console.log(`ℹ️ Ignoring ${eventType} event (not a pull_request)`);
        return res.status(204).end();
      }

      // Check if this is a pull request being closed (merged or just closed)
      if (payload.action !== 'closed') {
        console.log(`ℹ️ Ignoring pull_request ${payload.action} action (not closed)`);
        return res.status(204).end();
      }

      // Check if the pull request was actually merged (not just closed)
      if (!payload.pull_request.merged) {
        console.log(`ℹ️ Ignoring closed pull request (not merged)`);
        return res.status(204).end();
      }

      // Check if the pull request was merged into main
      const baseBranch = payload.pull_request.base.ref;
      if (baseBranch !== 'main') {
        console.log(`ℹ️ Ignoring pull request merged into ${baseBranch} (not main)`);
        return res.status(204).end();
      }

      // Log the successful merge
      const prNumber = payload.pull_request.number;
      const prTitle = payload.pull_request.title;
      const prAuthor = payload.pull_request.user.login;
      console.log(`🔀 Pull request #${prNumber} merged into main: "${prTitle}" by ${prAuthor}`);

      res.status(204).end(); // Acknowledge webhook

      // --- Deployment sequence ---
      console.log('📥 Pulling, installing, restarting…');
      await exec(`cd ~/www`);
      await exec(`git pull --ff-only`);
      await exec(`npm install`);
      await exec(`npm run build --omit=dev`);
      await exec(`pm2 restart inspire`);
      console.log('🚀 Deployment finished');
    } catch (err) {
      console.error('❌ Deployment failed:', err);
    }
  }
);

import { useState, useEffect, useRef, useCallback } from 'react';
import { nanoid } from 'nanoid';
import { apiRequest, queryClient } from '@/lib/queryClient';
import {
  Note, OutlineItem, Citation, DocumentExportMetadata, Signer,
  DocumentContentData,
  // DocumentChange, DocumentChangeType, etc. are now from shared/schema
} from '@/lib/types';
// Adjust the path based on actual file structure from hooks/use-document.ts to shared/schema.ts
// Assuming hooks is in src/hooks and shared is at the root, then ../../../shared/schema might be correct
// Or if there's a path alias for shared: e.g. @shared/schema
import diff from 'fast-diff';
import {
  DocumentChangeType, // Enum (value)
  type DocumentChange, // Union type
  type OutlineItemAddedChange, // Specific change types
  type OutlineItemRemovedChange,
  type OutlineItemModifiedChange,
  type NoteAddedChange,
  type NoteRemovedChange,
  type NoteModifiedChange,
  type WritingSectionModifiedChange,
  type CitationAddedChange,
  type CitationRemovedChange,
  type CitationModifiedChange,
  type UserPresenceInfo, // For presence state
  type UserJoinedPayload, // For payload typing
  type UserLeftPayload,
  type PresenceListPayload,
  type DocumentChatMessage, // For chat messages state
  type ChatMessageSentChange, // For sending chat messages
  type NewChatMessagePayload // For receiving new chat messages (payload is DocumentChatMessage)
} from '../../../shared/schema';
import { useToast } from '@/hooks/use-toast';

// Defined here or imported if centralized later
type DocumentFormat = 'apa-pro' | 'apa-student' | 'mla-cover' | 'mla' | 'turabian' | 'turabian-dissertation';

// Use wss:// for HTTPS pages, ws:// for HTTP pages
const WEBSOCKET_URL = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/app-ws`;

const getDefaultMetadata = (docTitle: string, format: DocumentFormat): DocumentExportMetadata => {
  // Basic default, can be expanded based on format
  let bibliographyHeading = 'Bibliography';
  if (format && format.startsWith('mla')) {
    bibliographyHeading = 'Works Cited';
  }

  return {
    firstName: '',
    middleName: '',
    lastName: '',
    mainTitle: docTitle || 'Untitled Document',
    coauthors: '',
    instructor: '',
    course: '',
    courseName: '',
    institution: '',
    submissionDate: new Date().toLocaleDateString('en-CA'), // Using 'en-CA' for YYYY-MM-DD format
    shortTitle: '',
    subtitle: '',
    tocHeading: 'Table of Contents',
    introductionHeading: 'Introduction',
    conclusionHeading: 'Conclusion',
    bibliographyHeading: bibliographyHeading,
    authorNote: '',
    dedication: '',
    abstract: '',
    keywords: '',
    footnotes: 'page', // 'page' or 'end'
    degreeTitle: '',
    college: '',
    copyrightYear: new Date().getFullYear().toString(),
    signers: [
      { name: '', jobTitle: '', role: '' },
      { name: '', jobTitle: '', role: '' },
      { name: '', jobTitle: '', role: '' }
    ]
  };
};

interface UseDocumentOptions {
  initialDocumentId?: string;
  initialFormatProp?: DocumentFormat;
  onNewMessageWhileClosed?: () => void;
  token?: string | null;
}

export function useDocument(options: UseDocumentOptions) {
  const { initialDocumentId, initialFormatProp, onNewMessageWhileClosed, token } = options;
  const [title, setTitle] = useState('');
  const [documentFormat, setDocumentFormat] = useState<DocumentFormat | null>(null);
  const [metadata, setMetadata] = useState<DocumentExportMetadata | null>(null);
  const [outline, setOutlineInternal] = useState<OutlineItem[]>([]);
  const [notes, setNotes] = useState<Note[]>([]);
  const [writing, setWriting] = useState<Record<string, { content: string, citations: Citation[] }>>({});
  const [activeOutlineItemId, setActiveOutlineItemId] = useState<string | null>(null);
  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'unsaved'>('saved');
  const [documentId, setDocumentId] = useState<string | undefined>(initialDocumentId);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null); // Added error state
  const [currentUserPermissionLevel, setCurrentUserPermissionLevel] = useState<'view' | 'edit' | null>(null); // Added permission state
  const [presentUsers, setPresentUsers] = useState<UserPresenceInfo[]>([]); // State for present users
  const [chatMessages, setChatMessages] = useState<DocumentChatMessage[]>([]); // State for chat messages
  const { toast } = useToast();
  const wsRef = useRef<WebSocket | null>(null);
  const previousContentRef = useRef<DocumentContentData | null>(null);
  const authenticatedUserIdRef = useRef<number | null>(null);

  // Refs to hold current state for WebSocket handlers to avoid stale closures
  const outlineRef = useRef(outline);
  const notesRef = useRef(notes);
  const writingRef = useRef(writing);

  useEffect(() => {
    outlineRef.current = outline;
  }, [outline]);

  useEffect(() => {
    notesRef.current = notes;
  }, [notes]);

  useEffect(() => {
    writingRef.current = writing;
  }, [writing]);

  // useEffect to log chatMessages state when it changes for debugging
  useEffect(() => {
    console.log('[WS EFFECT]: chatMessages state updated:', JSON.stringify(chatMessages));
  }, [chatMessages]);

  useEffect(() => {
    // Fetch authenticated user ID when the hook mounts
    const fetchUser = async () => {
      console.log('fetchUser: Attempting to fetch /api/user');
      try {
        const response = await apiRequest('GET', '/api/user');
        console.log('fetchUser: Raw response object from /api/user:', response);

        if (!response.ok) { // Check if response status is OK
          console.error(`fetchUser: API request to /api/user failed with status ${response.status}`);
          throw new Error(`Failed to fetch user: ${response.status} ${response.statusText}`);
        }

        const user = await response.json(); // Parse JSON from the response body
        console.log('fetchUser: Parsed user object from /api/user:', user);

        if (user && typeof user.id === 'number') {
          authenticatedUserIdRef.current = user.id;
          console.log('fetchUser: Authenticated user ID set:', user.id);
        } else {
          console.error('fetchUser: Failed to fetch authenticated user ID, user.id is missing or not a number. Parsed user data:', user);
        }
      } catch (error) {
        console.error('fetchUser: Error fetching user for WebSocket userId:', error);
      }
    };
    fetchUser();
  }, []); // Runs once on mount

  useEffect(() => {
    if (initialDocumentId) {
      setDocumentId(initialDocumentId);
    }
  }, [initialDocumentId]);

  useEffect(() => {
    if (documentId) {
      wsRef.current = new WebSocket(WEBSOCKET_URL);

      wsRef.current.onopen = () => {
        console.log('WebSocket connection established');
        // Send a message to identify the document this client is working on
        wsRef.current?.send(JSON.stringify({ type: 'register', documentId }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data as string);
          console.log('WebSocket message received:', message);

          // 1. CHECK FOR CHAT MESSAGES FIRST
          // Check if this 'documentChange' is actually a NEW_CHAT_MESSAGE
          if (message.type === 'documentChange' && message.elementType === DocumentChangeType.NEW_CHAT_MESSAGE) {
            console.log('[WS MSG]: Entered NEW_CHAT_MESSAGE processing block.');
            // Ensure the message is for the current document
            if (message.documentId === documentId) {
              const newChatMessage = message.payload as DocumentChatMessage; // Payload is the DocumentChatMessage object
              console.log('[WS MSG]: Received newChatMessage payload:', JSON.stringify(newChatMessage));
              console.log(`[WS MSG]: Sender ID in message: ${message.userId}, Authenticated user ID: ${authenticatedUserIdRef.current}`);

              setChatMessages(prevMessages => {
                console.log('[WS MSG]: setChatMessages callback. Previous messages count:', prevMessages.length);
                // Optional: Add a check to prevent duplicates if this becomes an issue
                // For example, if an optimistic update was also implemented elsewhere.
                 if (prevMessages.find(m => m.id === newChatMessage.id)) {
                   console.log('[WS MSG]: Duplicate message ID found, not adding. Message ID:', newChatMessage.id);
                   return prevMessages;
                 }
                const updatedMessages = [...prevMessages, newChatMessage];
                console.log('[WS MSG]: setChatMessages callback. New messages count:', updatedMessages.length);
                return updatedMessages;
              });
              // If the chat panel is closed, notify the parent to show an indicator
              if (onNewMessageWhileClosed) {
                onNewMessageWhileClosed();
              }
            }
          // 2. THEN CHECK FOR OTHER DOCUMENT CONTENT CHANGES (with self-ignore for these content changes)
          } else if (message.type === 'documentChange' && message.documentId === documentId) {
            // Compare with authenticatedUserIdRef.current (which is a number)
            // message.userId from server is also expected to be a number (the authenticated ws.userId of sender)
            // This self-ignore is for general content updates, not for chat messages (handled above).
            if (message.userId === authenticatedUserIdRef.current) {
              console.log("Ignoring own documentChange broadcast for content updates.");
              return;
            }

            console.log("Applying granular changes from server:", message.changes);

            // Use refs to get the latest state for applying changes, then update local vars
            let currentOutlineState = [...outlineRef.current];
            let currentNotesState = [...notesRef.current];
            let currentWritingState = JSON.parse(JSON.stringify(writingRef.current));


            const updateOutlineState = (items: OutlineItem[], change: DocumentChange): OutlineItem[] => {
              if (change.elementType === DocumentChangeType.OUTLINE_ITEM_ADDED) {
                const { item: newItem, parentItemId, index } = (change as OutlineItemAddedChange).payload;
                if (!parentItemId) {
                  const newItems = [...items];
                  newItems.splice(index ?? newItems.length, 0, newItem);
                  return newItems;
                }
              }
              return items.map(item => {
                let modifiedItem = { ...item };
                if (change.elementType === DocumentChangeType.OUTLINE_ITEM_ADDED) {
                  const { item: newItem, parentItemId: addParentId, index: addIndex } = (change as OutlineItemAddedChange).payload;
                  if (item.id === addParentId) {
                    const children = modifiedItem.children ? [...modifiedItem.children] : [];
                    children.splice(addIndex ?? children.length, 0, newItem);
                    modifiedItem.children = children;
                  }
                }
                if (item.id === change.elementId && change.elementType === DocumentChangeType.OUTLINE_ITEM_MODIFIED) {
                  const { propertyName, newValue } = (change as OutlineItemModifiedChange).payload;
                  if (propertyName === 'title') modifiedItem.title = newValue as string;
                  if (propertyName === 'number') modifiedItem.number = newValue as string;
                }
                if (change.elementType === DocumentChangeType.OUTLINE_ITEM_REMOVED) {
                    const { parentItemId: removeParentId } = (change as OutlineItemRemovedChange).payload || {};
                    if (item.id === removeParentId && modifiedItem.children) {
                        modifiedItem.children = modifiedItem.children.filter(child => child.id !== change.elementId);
                    }
                }
                if (modifiedItem.children && modifiedItem.children.length > 0) {
                  modifiedItem.children = updateOutlineState(modifiedItem.children, change);
                }
                return modifiedItem;
              }).filter(item => {
                if (change.elementType === DocumentChangeType.OUTLINE_ITEM_REMOVED) {
                  const { parentItemId: removeParentId } = (change as OutlineItemRemovedChange).payload || {};
                  return !(!removeParentId && item.id === change.elementId);
                }
                return true;
              });
            };

            message.changes.forEach((change: DocumentChange) => {
              switch (change.elementType) {
                case DocumentChangeType.OUTLINE_ITEM_ADDED:
                case DocumentChangeType.OUTLINE_ITEM_REMOVED:
                case DocumentChangeType.OUTLINE_ITEM_MODIFIED:
                  currentOutlineState = updateOutlineState(currentOutlineState, change);
                  break;
                case DocumentChangeType.NOTE_ADDED:
                  currentNotesState = [...currentNotesState, (change as NoteAddedChange).payload.note];
                  break;
                case DocumentChangeType.NOTE_REMOVED:
                  currentNotesState = currentNotesState.filter(note => note.id !== change.elementId);
                  break;
                case DocumentChangeType.NOTE_MODIFIED: {
                  const { propertyName, newValue } = (change as NoteModifiedChange).payload;
                  currentNotesState = currentNotesState.map(note =>
                    note.id === change.elementId
                      ? { ...note, [propertyName]: newValue }
                      : note
                  );
                  break;
                }
                case DocumentChangeType.WRITING_SECTION_MODIFIED: {
                  const { patch } = (change as WritingSectionModifiedChange).payload;
                  let newContent = '';
                  let lastIndex = 0;
                  for (const [type, text] of patch) {
                    if (type === diff.EQUAL) {
                      newContent += text;
                      lastIndex += text.length;
                    } else if (type === diff.INSERT) {
                      newContent += text;
                    } else if (type === diff.DELETE) {
                      lastIndex += text.length;
                    }
                  }
                  currentWritingState = {
                    ...currentWritingState,
                    [change.elementId]: {
                      ...(currentWritingState[change.elementId] || { citations: [] }),
                      content: newContent,
                    }
                  };
                  break;
                }
                case DocumentChangeType.CITATION_ADDED: {
                  const sectionId = change.elementId;
                  const { citation: newCitation } = (change as CitationAddedChange).payload;
                  const section = currentWritingState[sectionId] || { content: '', citations: [] };
                  currentWritingState = {
                    ...currentWritingState,
                    [sectionId]: {
                      ...section,
                      citations: [...(section.citations || []), newCitation]
                    }
                  };
                  break;
                }
                case DocumentChangeType.CITATION_REMOVED: {
                  const sectionId = change.elementId;
                  const { citationId } = (change as CitationRemovedChange).payload;
                  const section = currentWritingState[sectionId];
                  if (section?.citations) {
                    currentWritingState = {
                      ...currentWritingState,
                      [sectionId]: {
                        ...section,
                        citations: section.citations.filter(c => c.id !== citationId)
                      }
                    };
                  }
                  break;
                }
                case DocumentChangeType.CITATION_MODIFIED: {
                  const sectionId = change.elementId;
                  const { citation: updatedCitation } = (change as CitationModifiedChange).payload;
                  const section = currentWritingState[sectionId];
                  if (section?.citations) {
                    currentWritingState = {
                      ...currentWritingState,
                      [sectionId]: {
                        ...section,
                        citations: section.citations.map(c => c.id === updatedCitation.id ? updatedCitation : c)
                      }
                    };
                  }
                  break;
                }
                default:
                  console.warn(`Unknown change elementType received on client for application: ${(change as any).elementType}`);
              }
            });

            setOutlineInternal(currentOutlineState);
            setNotes(currentNotesState);
            setWriting(currentWritingState);

            previousContentRef.current = {
              outline: currentOutlineState,
              notes: currentNotesState,
              writing: currentWritingState
            };

            // Avoid toast for every granular update, could be too noisy.
            // toast({ title: "Document Updated", description: "Changes from another user have been applied." });

          } else if (message.type === 'documentChange' && message.elementType === DocumentChangeType.NEW_CHAT_MESSAGE) {
            // Handle new chat messages
            // Ensure the message is for the current document (though WebSocket room should handle this)
            if (message.documentId === documentId) {
              const newChatMessage = message.payload as DocumentChatMessage; // Payload is the DocumentChatMessage object
              setChatMessages(prevMessages => [...prevMessages, newChatMessage]);
              // If the chat panel is closed, notify the parent to show an indicator
              if (onNewMessageWhileClosed) {
                onNewMessageWhileClosed();
              }
            }
          } else if (message.type === 'presenceEvent') { // Assuming presence events have a top-level 'type'
            if (message.elementType === DocumentChangeType.USER_JOINED) {
              const { user: joinedUser } = message.payload as UserJoinedPayload;
              if (joinedUser.id !== authenticatedUserIdRef.current) {
                setPresentUsers(prev => [...prev.filter(u => u.id !== joinedUser.id), joinedUser]);
                toast({ title: "User Joined", description: `${joinedUser.username} joined the document.` });
              }
            } else if (message.elementType === DocumentChangeType.USER_LEFT) {
              const { userId: leftUserId } = message.payload as UserLeftPayload;
              const leavingUser = presentUsers.find(u => u.id === leftUserId);
              if (leavingUser) {
                  toast({ title: "User Left", description: `${leavingUser.username} left the document.` });
              }
              setPresentUsers(prev => prev.filter(user => user.id !== leftUserId));
            } else if (message.elementType === DocumentChangeType.PRESENCE_LIST) {
              const { users } = message.payload as PresenceListPayload;
              setPresentUsers(users);
            }
          } else if (message.type === 'error') {
            toast({ title: "Error from server", description: message.message, variant: "destructive" });
          }
        } catch (error) {
          console.error('Error processing incoming WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket connection closed');
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

      return () => {
        wsRef.current?.close();
      };
    }
  }, [documentId, toast, onNewMessageWhileClosed]);

  // REORDERED FUNCTIONS START HERE

  // Function to calculate and send deltas
  const sendDeltaChanges = useCallback((contentToSend?: DocumentContentData) => {
    if (currentUserPermissionLevel === 'view') return;
    console.log('sendDeltaChanges: authenticatedUserIdRef.current =', authenticatedUserIdRef.current); // Added log
    if (!documentId || !wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.log("Cannot send delta: no documentId, WebSocket not open, or not initialized.");
      return;
    }

    const newChanges: DocumentChange[] = [];
    const currentContent: DocumentContentData = contentToSend || { outline, notes, writing };
    const prevContent = previousContentRef.current;

    console.log('sendDeltaChanges: prevContent for diff:', JSON.stringify(prevContent)); // Added log
    console.log('sendDeltaChanges: currentContent for diff:', JSON.stringify(currentContent)); // Added log
    if(prevContent){ // Guard against prevContent being null for the stringify comparison log
      console.log('sendDeltaChanges: Are top-level current and previous content different (stringified)?', JSON.stringify(currentContent) !== JSON.stringify(prevContent)); // Added log
    } else {
      console.log('sendDeltaChanges: prevContent is null, cannot compare stringified versions for overall diff.');
    }


    try {
      // --- Outline Diffing ---
      const diffOutlineItems = (
        currentItems: OutlineItem[],
        previousItems: OutlineItem[] | undefined,
        parentId?: string
      ): DocumentChange[] => {
        const itemChanges: DocumentChange[] = [];
        const prevItemsMap = new Map(previousItems?.map(item => [item.id, item]));
        const currentItemsMap = new Map(currentItems.map(item => [item.id, item]));

        // Check for removed and modified items
        previousItems?.forEach((prevItem, index) => {
          const currentItem = currentItemsMap.get(prevItem.id);
          if (!currentItem) {
            itemChanges.push({
              elementType: DocumentChangeType.OUTLINE_ITEM_REMOVED,
              elementId: prevItem.id,
              parentId,
              // No specific payload needed beyond elementId and parentId for simple removal
            } as OutlineItemRemovedChange); // Type assertion
          } else {
            // Item exists, check for modifications (e.g., title)
            if (prevItem.title !== currentItem.title) {
              itemChanges.push({
                elementType: DocumentChangeType.OUTLINE_ITEM_MODIFIED,
                elementId: currentItem.id,
                parentId,
                payload: {
                  propertyName: 'title',
                  oldValue: prevItem.title,
                  newValue: currentItem.title,
                },
              } as OutlineItemModifiedChange);
            }
            // TODO: Check for 'number' modification if applicable
            // TODO: Check for moves (more complex, involves newParentId, newIndex)

            // Recursively diff children
            const childrenChanges = diffOutlineItems(
              currentItem.children || [],
              prevItem.children || [],
              currentItem.id
            );
            itemChanges.push(...childrenChanges);
          }
        });

        // Check for added items
        currentItems.forEach((currentItem, index) => {
          if (!prevItemsMap.has(currentItem.id)) {
            itemChanges.push({
              elementType: DocumentChangeType.OUTLINE_ITEM_ADDED,
              elementId: currentItem.id, // ID of the new item itself
              parentId, // parentId is the ID of the list it's added to
              payload: {
                item: currentItem,
                parentItemId: parentId,
                index,
              },
            } as OutlineItemAddedChange);
            // If an item is added, its children are also "new" effectively,
            // but the `item` payload includes children, so server can reconstruct.
          }
        });
        return itemChanges;
      };

      if (prevContent) { // Only diff if there's a previous state
        const outlineChanges = diffOutlineItems(currentContent.outline, prevContent.outline);
        newChanges.push(...outlineChanges);
      } else {
        // If no prevContent, consider all current content as "added" if needed,
        // or handle as initial state. For deltas, typically we need a prev state.
        // For now, if no prevContent, we won't generate deltas.
        // This case should ideally be handled by ensuring previousContentRef is set on load.
        console.warn("Skipping delta generation: previousContentRef is null.");
      }

      // --- Notes Diffing (To be implemented in Part 2) ---
      const diffNotes = (
        currentNotes: Note[],
        previousNotes: Note[] | undefined
      ): DocumentChange[] => {
        const noteChanges: DocumentChange[] = [];
        const prevNotesMap = new Map(previousNotes?.map(note => [note.id, note]));
        const currentNotesMap = new Map(currentNotes.map(note => [note.id, note]));

        // Check for removed and modified notes
        previousNotes?.forEach(prevNote => {
          const currentNote = currentNotesMap.get(prevNote.id);
          if (!currentNote) {
            noteChanges.push({
              elementType: DocumentChangeType.NOTE_REMOVED,
              elementId: prevNote.id,
            } as NoteRemovedChange);
          } else {
            // Item exists, check for modifications
            const propsToCompare: (keyof Pick<Note, 'title' | 'content' | 'linkedOutlineId'>)[] = ['title', 'content', 'linkedOutlineId'];
            propsToCompare.forEach(prop => {
              if (prevNote[prop] !== currentNote[prop]) {
                noteChanges.push({
                  elementType: DocumentChangeType.NOTE_MODIFIED,
                  elementId: currentNote.id,
                  payload: {
                    propertyName: prop,
                    oldValue: prevNote[prop],
                    newValue: currentNote[prop],
                  },
                } as NoteModifiedChange);
              }
            });
            // TODO: Diff other note properties if necessary (e.g., type, attachment URLs if not handled by content)
          }
        });

        // Check for added notes
        currentNotes.forEach(currentNote => {
          if (!prevNotesMap.has(currentNote.id)) {
            noteChanges.push({
              elementType: DocumentChangeType.NOTE_ADDED,
              elementId: currentNote.id, // ID of the new note
              payload: {
                note: currentNote,
              },
            } as NoteAddedChange);
          }
        });
        return noteChanges;
      };

      if (prevContent) {
        const noteChanges = diffNotes(currentContent.notes, prevContent.notes);
        newChanges.push(...noteChanges);
      }

      // --- Writing Sections Diffing ---
      const diffWritingSections = (
        currentWriting: Record<string, { content: string; citations?: Citation[] }>,
        previousWriting: Record<string, { content: string; citations?: Citation[] }> | undefined
      ): DocumentChange[] => {
        const writingChanges: DocumentChange[] = [];
        const allSectionIds = new Set([
          ...Object.keys(currentWriting || {}),
          ...Object.keys(previousWriting || {}),
        ]);

        allSectionIds.forEach(sectionId => {
          const currentSection = currentWriting?.[sectionId];
          const prevSection = previousWriting?.[sectionId];

          // If section content has changed
          if (currentSection && prevSection && currentSection.content !== prevSection.content) {
            const patch = diff(prevSection.content, currentSection.content);
            writingChanges.push({
              elementType: DocumentChangeType.WRITING_SECTION_MODIFIED,
              elementId: sectionId, // This is the outline item ID
              payload: {
                patch,
              },
            } as WritingSectionModifiedChange);
          } else if (currentSection && !prevSection) {
            // Section added (content only, citations handled next)
             writingChanges.push({
              elementType: DocumentChangeType.WRITING_SECTION_MODIFIED, // Treat as modification with new content
              elementId: sectionId,
              payload: {
                newContent: currentSection.content,
                oldContent: "",
              },
            } as WritingSectionModifiedChange);
          }
          // Note: Section removal (where sectionId exists in prevWriting but not currentWriting)
          // is implicitly handled if the corresponding outline item is removed, which cleans up its writing section.
          // If a writing section could be "cleared" but the outline item remains, that's a content modification to empty.

          // Diff citations for this section
          const currentCitations = currentSection?.citations || [];
          const prevCitations = prevSection?.citations || [];
          const prevCitationsMap = new Map(prevCitations.map(c => [c.id, c]));
          const currentCitationsMap = new Map(currentCitations.map(c => [c.id, c]));

          // Check for removed and modified citations
          prevCitations.forEach(prevCitation => {
            const currentCitation = currentCitationsMap.get(prevCitation.id);
            if (!currentCitation) {
              writingChanges.push({
                elementType: DocumentChangeType.CITATION_REMOVED,
                elementId: sectionId, // sectionId is the parent for the citation
                payload: { citationId: prevCitation.id },
              } as CitationRemovedChange);
            } else {
              // Simple modification check: if stringified versions are different.
              // For more complex objects, a deep diff might be needed or compare specific props.
              if (JSON.stringify(prevCitation) !== JSON.stringify(currentCitation)) {
                writingChanges.push({
                  elementType: DocumentChangeType.CITATION_MODIFIED,
                  elementId: sectionId,
                  payload: { citation: currentCitation },
                } as CitationModifiedChange);
              }
            }
          });

          // Check for added citations
          currentCitations.forEach(currentCitation => {
            if (!prevCitationsMap.has(currentCitation.id)) {
              writingChanges.push({
                elementType: DocumentChangeType.CITATION_ADDED,
                elementId: sectionId,
                payload: { citation: currentCitation },
              } as CitationAddedChange);
            }
          });
        });
        return writingChanges;
      };

      if (prevContent) {
        const writingChanges = diffWritingSections(currentContent.writing, prevContent.writing);
        newChanges.push(...writingChanges);
      }

      console.log('sendDeltaChanges: Generated granular changes array:', JSON.stringify(newChanges)); // Added log

      if (newChanges.length > 0) {
        if (authenticatedUserIdRef.current === null) {
          console.error("Cannot send changes: Authenticated user ID not available.");
          setSaveStatus('unsaved'); // Or handle as appropriate
          return;
        }
        const message = {
          type: 'documentChange',
          userId: authenticatedUserIdRef.current,
          timestamp: new Date().toISOString(),
          documentId,
          changes: newChanges,
        };
        wsRef.current.send(JSON.stringify(message));
        console.log('Sent granular delta changes:', message);
        previousContentRef.current = JSON.parse(JSON.stringify(currentContent));
        setSaveStatus('saved');
      } else {
        console.log('No granular changes detected to send.');
        setSaveStatus('saved');
      }
    } catch (error) {
      console.error("Error sending WebSocket message or processing granular deltas:", error);
      setSaveStatus('unsaved');
    }
  }, [documentId, outline, notes, writing, authenticatedUserIdRef, setSaveStatus, currentUserPermissionLevel]); // Added currentUserPermissionLevel to deps

  // Direct save function for drag operations - bypasses granular changes
  const saveDocumentDirectly = useCallback(async (content: any) => {
    if (!documentId || currentUserPermissionLevel === 'view') return;

    console.log("Saving document directly (bypassing granular changes)...");
    setSaveStatus('saving');

    try {
      await apiRequest('PUT', `/api/documents/${documentId}/content`, content);
      console.log("Direct save completed successfully");
      setSaveStatus('saved');
      // Update the previous content reference to prevent future granular diffs from being confused
      previousContentRef.current = JSON.parse(JSON.stringify(content));
    } catch (error) {
      console.error("Error in direct save:", error);
      setSaveStatus('unsaved');
    }
  }, [documentId, currentUserPermissionLevel, setSaveStatus]);

  // Internal save function - prepares content and calls sendDeltaChanges
  const saveDocumentInternal = useCallback(async (options?: { latestNotes?: Note[], contentToSave?: DocumentContentData }) => {
    if (!documentId) return;

    console.log("Processing save request...");
    setSaveStatus('saving');

    try {
      // Handle title update (still HTTP for now)
      // Check against actual current title state, not previousContentRef.title as that might be stale for title
      const currentDocForTitleCheck = await apiRequest('GET', `/api/documents/${documentId}`);
      if (currentDocForTitleCheck && currentDocForTitleCheck.title !== title && currentUserPermissionLevel !== 'view') {
         console.log("Saving document title change.");
         await apiRequest('PATCH', `/api/documents/${documentId}/title`, { title });
         queryClient.invalidateQueries({ queryKey: ["/api/documents"] });
      }

      let contentForDelta: DocumentContentData;
      if (options?.contentToSave) {
        contentForDelta = options.contentToSave;
      } else {
        const notesToUse = options?.latestNotes || notes;
        contentForDelta = { outline, notes: notesToUse, writing };
      }

      sendDeltaChanges(contentForDelta);

      console.log("Delta changes sent (or attempted). Title (if changed) updated.");
      // setSaveStatus('saved') is now handled by sendDeltaChanges
    } catch (error) {
      console.error('Error in saveDocumentInternal:', error);
      setSaveStatus('unsaved');
    }
  }, [documentId, title, outline, notes, writing, sendDeltaChanges, queryClient, currentUserPermissionLevel]);


  const writingDebounceTimerRef = useRef<NodeJS.Timeout | null>(null); // Timer for writing debounce

  // Wrapper around setOutline to mark document as unsaved and trigger auto-save
  const setOutline = useCallback((newOutlineArg: OutlineItem[] | ((prevOutline: OutlineItem[]) => OutlineItem[])) => {
    if (currentUserPermissionLevel === 'view') return;
    let newOutline: OutlineItem[];
    if (typeof newOutlineArg === 'function') {
      newOutline = newOutlineArg(outline);
    } else {
      newOutline = newOutlineArg;
    }
    setOutlineInternal(newOutline);
    setSaveStatus('unsaved');

    // Check if we're in a drag operation - if so, use direct content update
    const isDragging = window.localStorage.getItem('disable_websocket') === 'true';
    if (isDragging) {
      // During drag operations, use direct HTTP update to avoid granular change conflicts
      console.log('Drag operation detected - using direct content update');
      saveDocumentDirectly({ outline: newOutline, notes, writing });
    } else {
      // Normal operation - use granular changes
      saveDocumentInternal({ contentToSave: { outline: newOutline, notes, writing } });
    }
  }, [outline, notes, writing, saveDocumentInternal, currentUserPermissionLevel]);

  // Helper function to generate basic empty content for sections (ensure it's stable or memoized)
  const getSampleContentForSection = useCallback((sectionId: string): string => {
    const findSection = (items: OutlineItem[]): OutlineItem | null => {
      for (const item of items) {
        if (item.id === sectionId) return item;
        if (item.children?.length) {
          const found = findSection(item.children);
          if (found) return found;
        }
      }
      return null;
    };
    const section = findSection(outline); // outline dependency
    if (!section) return '';
    return `<p>Click to start writing in this section...</p>`;
  }, [outline]);

  // Update writing content with DEBOUNCED auto-save
  const updateWritingContent = useCallback((content: string) => {
    if (!activeOutlineItemId || currentUserPermissionLevel === 'view') return;

    const finalContent = content || getSampleContentForSection(activeOutlineItemId); // getSampleContentForSection is now defined before

    setWriting(prevWriting => ({
      ...prevWriting,
      [activeOutlineItemId]: {
        ...(prevWriting[activeOutlineItemId] || { citations: [] }), // Ensure citations array exists
        content: finalContent
      }
    }));

    setSaveStatus('unsaved');

    if (writingDebounceTimerRef.current) {
      clearTimeout(writingDebounceTimerRef.current);
    }

    writingDebounceTimerRef.current = setTimeout(() => {
      console.log("Debounced: Saving writing content...");
      saveDocumentInternal({ contentToSave: { outline, notes, writing: { ...writing, [activeOutlineItemId]: { content: finalContent, citations: (writing[activeOutlineItemId]?.citations || []) } } } });
    }, 750); // 750ms debounce timer
  }, [activeOutlineItemId, saveDocumentInternal, outline, notes, writing, getSampleContentForSection, currentUserPermissionLevel]);

  // REORDERED FUNCTIONS END HERE - Other functions (createNote, updateNote, etc.) will follow

  // Load document content from API
  useEffect(() => {
    const loadOrCreateDocument = async () => {
      setIsLoading(true); // Ensure this is active
      // ... rest of the async function ...
      try {
        // ... logic to load or create document, content, and settings ...
        // setTitle(document.title) will still be called inside here for existing docs,
        // and title prop will be used for newDocInitialTitle for new docs.
        // The key is that changes to the 'title' state itself no longer re-trigger this entire effect.
        if (documentId) {
          // Load existing document by ID
          console.log('[useDocument] Attempting to load main document content for ID:', documentId);
          const url = new URL(`/api/documents/${documentId}`, window.location.origin);
          if (token) {
            url.searchParams.append('token', token);
          }
          const documentRes = await fetch(url.toString());
          
          if (!documentRes.ok) {
            const errorData = await documentRes.json().catch(() => ({ message: `Failed to load document: ${documentRes.status} ${documentRes.statusText}` }));
            const specificError = new Error(errorData.message);
            (specificError as any).status = documentRes.status;
            throw specificError;
          }
          
          const document = await documentRes.json();
          setDocumentId(document.id);
          setTitle(document.title);
          setCurrentUserPermissionLevel(document.currentUserPermissionLevel || null); // Set permission level
          
          // Only attempt to load content if permission was granted (implicitly by documentRes.ok)
          const contentRes = await fetch(`/api/documents/${documentId}/content`);
          
          if (!contentRes.ok) {
            throw new Error(`Failed to load document content: ${contentRes.statusText}`);
          }
          
          const content: DocumentContentData = await contentRes.json();
          console.log("Content loaded:", content);
          
          setOutlineInternal(content.outline || []);
          setNotes(content.notes || []);
          setWriting(content.writing || {});
          previousContentRef.current = JSON.parse(JSON.stringify(content)); // Initialize previous content
          console.log('loadOrCreateDocument (existing): previousContentRef.current initialized:', previousContentRef.current); // Added log

          // Fetch historical chat messages
          try {
            console.log('[useDocument] Fetching chat messages for document ID:', document.id);
            const chatRes = await apiRequest('GET', `/api/documents/${document.id}/chat`);
            // apiRequest already handles !res.ok by throwing, so we can assume success if it doesn't throw
            const historicalMessages = await chatRes.json(); // Assuming apiRequest returns a Response-like object that needs .json()
                                                            // If apiRequest directly returns parsed JSON, this line changes
            if (Array.isArray(historicalMessages)) {
              setChatMessages(historicalMessages);
              console.log('[useDocument] Historical chat messages loaded:', historicalMessages.length);
            } else {
              console.error('[useDocument] Fetched historical chat messages is not an array:', historicalMessages);
              setChatMessages([]); // Default to empty if structure is wrong
            }
          } catch (chatError) {
            console.error('[useDocument] Error fetching historical chat messages:', chatError);
            toast({
              title: 'Chat Error',
              description: 'Could not load chat history.',
              variant: 'destructive',
            });
            setChatMessages([]); // Default to empty on error
          }

          console.log('[useDocument] Main content loaded. Now fetching export settings...');

          try {
            console.log('[useDocument] Fetching settings from:', `/api/documents/${documentId}/export-settings`);
            const settingsRes = await fetch(`/api/documents/${documentId}/export-settings`);
            console.log('[useDocument] Settings response status:', settingsRes.status);

            if (settingsRes.ok) {
              const responsePayload = await settingsRes.json(); // Renamed for clarity
              console.log('[useDocument] Settings fetched successfully (status 200). Payload:', responsePayload);

              // Check if the payload indicates settings exist and has the nested structure
              if (responsePayload && responsePayload.exists === true && responsePayload.settings && responsePayload.settings.format && responsePayload.settings.metadata) {
                console.log('[useDocument] Valid settings found in 200 OK response (nested structure).');
                // Access format and metadata from the nested 'settings' object
                console.log('[useDocument] Value for setDocumentFormat (existing settings):', responsePayload.settings.format);
                setDocumentFormat(responsePayload.settings.format);
                // Use document.title from the earlier fetch of document details
                const defaultMetaForLoadedFormat = getDefaultMetadata(document.title, responsePayload.settings.format);
                const finalMetadata = { ...defaultMetaForLoadedFormat, ...responsePayload.settings.metadata };
                console.log('[useDocument] Value for setMetadata (existing settings):', finalMetadata);
                setMetadata(finalMetadata);
                console.log('[useDocument] documentFormat and metadata set from fetched nested settings.');
              } else if (responsePayload && responsePayload.exists === false) {
                // This handles the explicit "exists: false" case
                console.log('[useDocument] 200 OK response, but payload indicates settings do not exist (exists: false). Initializing with defaults.');
                const defaultFormat: DocumentFormat = initialFormatProp || 'apa-student';
                const defaultMeta = getDefaultMetadata(document.title, defaultFormat);

                console.log('[useDocument] Value for setDocumentFormat (exists: false path, defaultFormat):', defaultFormat);
                setDocumentFormat(defaultFormat);
                console.log('[useDocument] Value for setMetadata (exists: false path, defaultMeta):', defaultMeta);
                setMetadata(defaultMeta);
                console.log('[useDocument] Set local format and metadata to defaults. Attempting to save to backend...');

                try {
                  await apiRequest('POST', `/api/documents/${documentId}/export-settings`, { format: defaultFormat, metadata: defaultMeta });
                  console.log('[useDocument] Successfully saved default settings to backend (after exists: false).');
                } catch (postError) {
                  console.error('[useDocument] Failed to POST default settings to backend (after exists: false):', postError);
                }
              } else {
                 // This handles cases where 200 OK is returned but the payload is unexpected
                 // or if settings.format/settings.metadata are missing even if 'exists' might be true or absent.
                console.log('[useDocument] 200 OK response, but settings data is missing, malformed, or indicates non-existence. Initializing with defaults. Payload:', responsePayload);
                const defaultFormat: DocumentFormat = initialFormatProp || 'apa-student';
                const defaultMeta = getDefaultMetadata(document.title, defaultFormat);

                console.log('[useDocument] Value for setDocumentFormat (malformed 200 OK, defaultFormat):', defaultFormat);
                setDocumentFormat(defaultFormat);
                console.log('[useDocument] Value for setMetadata (malformed 200 OK, defaultMeta):', defaultMeta);
                setMetadata(defaultMeta);
                console.log('[useDocument] Set local format and metadata to defaults (malformed 200 OK). Attempting to save to backend...');

                try {
                  await apiRequest('POST', `/api/documents/${documentId}/export-settings`, { format: defaultFormat, metadata: defaultMeta });
                  console.log('[useDocument] Successfully saved default settings to backend (after malformed 200 OK).');
                } catch (postError) {
                  console.error('[useDocument] Failed to POST default settings to backend (after malformed 200 OK):', postError);
                }
              }
            } else if (settingsRes.status === 404) {
              // This block remains the same for explicit 404s
              console.log('[useDocument] Export settings not found (404). Initializing with defaults.');
              const defaultFormat: DocumentFormat = initialFormatProp || 'apa-student';
              const defaultMeta = getDefaultMetadata(document.title, defaultFormat);

              console.log('[useDocument] Value for setDocumentFormat (404 path, defaultFormat):', defaultFormat);
              setDocumentFormat(defaultFormat);
              console.log('[useDocument] Value for setMetadata (404 path, defaultMeta):', defaultMeta);
              setMetadata(defaultMeta);
              console.log('[useDocument] Set local format and metadata to defaults (404 path). Attempting to save to backend...');

              try {
                await apiRequest('POST', `/api/documents/${documentId}/export-settings`, { format: defaultFormat, metadata: defaultMeta });
                console.log('[useDocument] Successfully saved default settings to backend (404 path).');
              } catch (postError) {
                console.error('[useDocument] Failed to POST default settings to backend (404 path):', postError);
              }
            } else {
              // Handle other non-ok statuses (500, etc.)
              console.warn('[useDocument] Failed to load export settings with status:', settingsRes.status, settingsRes.statusText);
              console.log('[useDocument] Falling back to default format and metadata locally (non-200/404 status).');
              const fallbackFormat: DocumentFormat = 'apa-student';
              // Ensure 'document' is available from the earlier fetch of document details
              console.log('[useDocument] Value for setDocumentFormat (other error fallback, fallbackFormat):', fallbackFormat);
              setDocumentFormat(fallbackFormat);
              const fallbackMeta = getDefaultMetadata(document.title, fallbackFormat);
              console.log('[useDocument] Value for setMetadata (other error fallback, fallbackMeta):', fallbackMeta);
              setMetadata(fallbackMeta);
            }
          } catch (settingsError) {
            console.error('[useDocument] Error fetching or initializing export settings:', settingsError);
            console.log('[useDocument] Falling back to default format and metadata locally due to caught error.');
            const errorFallbackFormat: DocumentFormat = 'apa-student';
            console.log('[useDocument] Value for setDocumentFormat (settingsError catch, errorFallbackFormat):', errorFallbackFormat);
            setDocumentFormat(errorFallbackFormat);
            const errorFallbackMeta = getDefaultMetadata(document.title, errorFallbackFormat);
            console.log('[useDocument] Value for setMetadata (settingsError catch, errorFallbackMeta):', errorFallbackMeta);
            setMetadata(errorFallbackMeta); // `document` from prior fetch
          }
          
          // Document is now saved
          setSaveStatus('saved');
        } else if (!documentId) {
          // Create a new document with default content
          console.log("Creating new document...");
          // Use the current title state or "Untitled Document" if title is empty
          const newDocInitialTitle = title || "Untitled Document";
          if (!title) setTitle(newDocInitialTitle); // Set title state if it was initially empty

          const res = await apiRequest('POST', '/api/documents', { title: newDocInitialTitle });
          const document = await res.json(); // document.id is now a string
          
          console.log("Document created with ID:", document.id);
          
          // Store the document ID
          setDocumentId(document.id); // document.id is now a string
          
          // Now load the content (which should be empty or default for a new doc)
          const contentRes = await fetch(`/api/documents/${document.id}/content`);
          const content: DocumentContentData = await contentRes.json();
          
          console.log("Initial content loaded:", content);
          
          setOutlineInternal(content.outline || []);
          setNotes(content.notes || []);
          setWriting(content.writing || {});
          previousContentRef.current = JSON.parse(JSON.stringify(content)); // Initialize previous content for new doc
          console.log('loadOrCreateDocument (new): previousContentRef.current initialized:', previousContentRef.current); // Added log

          console.log('[useDocument] New document created with ID:', document.id, 'Title:', newDocInitialTitle);
          const newDocFormat = initialFormatProp || 'apa-student';
          console.log('[useDocument] Value for setDocumentFormat (new doc path, newDocFormat):', newDocFormat);
          setDocumentFormat(newDocFormat);
          const defaultMeta = getDefaultMetadata(newDocInitialTitle, newDocFormat);
          console.log('[useDocument] Value for setMetadata (new doc path, defaultMeta):', defaultMeta);
          setMetadata(defaultMeta);
          console.log('[useDocument] Set format and metadata for new doc. Attempting to save to backend...');

          if (document.id) {
            try {
              await apiRequest('POST', `/api/documents/${document.id}/export-settings`, { format: newDocFormat, metadata: defaultMeta });
              console.log('[useDocument] Successfully saved initial settings for new doc to backend.');
            } catch (postError) {
              console.error('[useDocument] Failed to POST initial settings for new doc:', postError);
            }
          } else {
            console.error("[useDocument] No document ID available to save initial export settings for new doc.");
          }
          
          // Document is now saved
          setSaveStatus('saved');
        }
      } catch (error) {
        console.error('Error in loadOrCreateDocument:', error); // Added a generic error log for the main try/catch
        toast({
          title: 'Error Loading Document',
          description: error.message || 'An unexpected error occurred.',
          variant: 'destructive'
        });
        // Ensure states are somewhat sane even on catastrophic error
        console.log('[useDocument] Value for setDocumentFormat (main catch, null)');
        setDocumentFormat(null); // Or a default
        console.log('[useDocument] Value for setMetadata (main catch, null)');
        setMetadata(null); // Or a default
        setError(error as Error); // Set error state
      } finally {
         setIsLoading(false); // Ensure this is active
      }
    };
    
    if (documentId || !initialDocumentId) { // Ensure it runs if documentId is provided, or if no initialDocumentId is set yet (for new doc creation flow)
      loadOrCreateDocument();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    // The above comment might be useful if your linter complains about missing 'title',
    // but for this specific fix, we are intentionally omitting it.
  }, [documentId, initialFormatProp, toast, token]); // 'title' has been removed

// Helper function to adjust metadata when format changes
const getAdjustedMetadataForFormat = (currentMetadata: DocumentExportMetadata, newFormat: DocumentFormat, docTitle: string): DocumentExportMetadata => {
  const newDefaults = getDefaultMetadata(docTitle, newFormat);
  const titleToPreserve = currentMetadata.mainTitle || docTitle;

  let adjustedMetadata: DocumentExportMetadata = { ...newDefaults };

  // Carry over common fields from currentMetadata.
  // This loop carries over any key present in currentMetadata to newDefaults if that key exists in newDefaults.
  // This is broad and assumes that if a field exists in both, it's desirable to carry it over.
  for (const key in currentMetadata) {
     if (Object.prototype.hasOwnProperty.call(currentMetadata, key) &&
         Object.prototype.hasOwnProperty.call(adjustedMetadata, key)) {

         const currentValue = currentMetadata[key];
         // Only carry over if the current value is not empty, null, or undefined.
         // This prevents overwriting a new default with an empty value from the old metadata.
         if (currentValue !== undefined && currentValue !== null && currentValue !== "") {
              adjustedMetadata[key] = currentValue;
         }
     }
  }
  adjustedMetadata.mainTitle = titleToPreserve; // Always ensure mainTitle is correctly preserved or set.

  // Specific adjustments based on format change
  if (newFormat.startsWith('mla')) {
    // If current bib heading is not the MLA default, keep it, otherwise set MLA default.
    adjustedMetadata.bibliographyHeading = (currentMetadata.bibliographyHeading && currentMetadata.bibliographyHeading !== newDefaults.bibliographyHeading && currentMetadata.bibliographyHeading !== 'Bibliography')
                                           ? currentMetadata.bibliographyHeading
                                           : 'Works Cited';
  } else { // For non-MLA formats
    // If current bib heading is 'Works Cited' (MLA default) or generic 'Bibliography', set to new format's default.
    // If it's something else custom, preserve it.
    if (currentMetadata.bibliographyHeading === 'Works Cited' || currentMetadata.bibliographyHeading === 'Bibliography') {
      adjustedMetadata.bibliographyHeading = newDefaults.bibliographyHeading;
    } else {
      adjustedMetadata.bibliographyHeading = currentMetadata.bibliographyHeading || newDefaults.bibliographyHeading;
    }
  }

  // Example: APA specific - Short title might need to be cleared or re-evaluated
  if (!newFormat.startsWith('apa')) {
     // If switching away from APA, you might want to clear APA-specific fields
     // or handle them based on requirements. For now, they'll be kept if filled.
     // adjustedMetadata.shortTitle = ''; // Or based on logic
  }

  return adjustedMetadata;
};

  // Add effect for saving when leaving the document
  useEffect(() => {
    // This will run when the component unmounts
    return () => {
      if (documentId && saveStatus === 'unsaved') {
        console.log('Saving document before navigating away...');
        saveDocumentInternal();
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [documentId, saveStatus, saveDocumentInternal]); // Ensuring this line is clean
  
  // Save document content to API (uses our internal save function)
  const saveDocument = async () => {
    console.log("Manual save triggered");
    try {
      await saveDocumentInternal();
      toast({
        title: 'Success',
        description: 'Document saved successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save document',
        variant: 'destructive'
      });
    }
  };

  // Create a new note - now requires linkedOutlineId
  const createNote = (linkedOutlineId: string, options?: { type?: 'text' | 'image' | 'video' | 'file' }) => {
    if (currentUserPermissionLevel === 'view') return;
    const type = options?.type || 'text';
    console.log("useDocument.createNote: Creating note with options:", options, "Resolved type:", type); // Added console.log
    // const outlineItemId = options?.outlineItemId; // This is now the first parameter

    // Default content for different note types
    let title = type === 'text' ? 'New Note' : `${type.charAt(0).toUpperCase() + type.slice(1)} Note`;
    let content = '';
    
    // Determine content based on note type
    if (type === 'text') {
      content = `<h1>New Note</h1>
<p>Add your research notes, ideas, and key points here.</p>

<h2>Tips for Taking Good Notes</h2>
<ul>
  <li><strong>Be concise</strong>: Focus on key concepts and main ideas</li>
  <li><strong>Use structure</strong>: Organize with headings and bullet points</li>
  <li><strong>Connect ideas</strong>: Link to relevant sections in your outline</li>
  <li><strong>Add context</strong>: Note why information is important to your work</li>
  <li><strong>Include sources</strong>: Add citations for proper attribution</li>
</ul>

<blockquote><p>Highlight important quotes or information using blockquotes</p></blockquote>

<p>You can drag and drop files below to attach relevant documents, images, or other reference materials.</p>`;
    } else {
      // For media notes, use a simple title-based content in HTML format
      content = `<h1>${title}</h1>`;
    }
    
    // Calculate position for the new note (append to end)
    const existingNotesForOutline = notes.filter(note => note.linkedOutlineId === linkedOutlineId);
    const maxPosition = existingNotesForOutline.reduce((max, note) => Math.max(max, note.position || 0), -1);
    const newPosition = maxPosition + 1;

    // Create the new note with appropriate type and all attachment arrays
    const newNote: Note = {
      id: nanoid(),
      title,
      content,
      type, // Explicitly set the note type
      linkedOutlineId: linkedOutlineId, // Enforce direct linking
      position: newPosition, // Add position for ordering
      // Always include all attachment arrays, even for non-media notes
      imageUrls: [],
      videoUrls: [],
      fileUrls: [],
      primaryAssetUrl: undefined, // Explicitly set to undefined for clarity
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    console.log(`Creating new ${type} note with ID:`, newNote.id);
    console.log("useDocument.createNote: newNote object:", newNote); // Added console.log
    
    // Use function form of setState to ensure we have the latest state and save immediately
    setNotes(currentNotes => {
      const newNotesArray = [...currentNotes, newNote];
      console.log("Adding note to state and saving immediately:", newNote.id);
      // Save immediately with the updated notes array
      saveDocumentInternal({ latestNotes: newNotesArray });
      return newNotesArray;
    });

    setSaveStatus('unsaved');
    
    return newNote;
  };

  // Link a note to an outline item
  const linkNoteToOutlineItem = (noteId: string, outlineItemId: string) => {
    let updatedNotesArray: Note[] = [];
    setNotes(currentNotes => {
      updatedNotesArray = currentNotes.map(note =>
        note.id === noteId ? { ...note, linkedOutlineId: outlineItemId, updatedAt: new Date().toISOString() } : note
      );
      // Immediate save with the updated notes array
      saveDocumentInternal({ latestNotes: updatedNotesArray });
      return updatedNotesArray;
    });
    setSaveStatus('unsaved'); // saveDocumentInternal will manage this, but good to mark intent

    toast({
      title: 'Note Linked',
      description: `Note successfully linked to outline item.`,
    });
  };

  // Delete an outline item and all its linked notes
  const deleteOutlineItemWithNotes = (outlineItemId: string) => {
    let updatedNotesArr: Note[] = [];
    // First, remove notes linked to this outline item
    setNotes(currentNotes => {
      updatedNotesArr = currentNotes.filter(note => note.linkedOutlineId !== outlineItemId);
      if (updatedNotesArr.length < currentNotes.length) {
        toast({
          title: 'Linked Notes Deleted',
          description: `${currentNotes.length - updatedNotesArr.length} note(s) linked to the deleted outline item were also removed.`,
        });
      }
      return updatedNotesArr;
    });

    // Then, remove the outline item itself
    // This requires a recursive function to traverse the outline tree
    const removeOutlineItemRecursively = (items: OutlineItem[], idToRemove: string): OutlineItem[] => {
      return items.reduce((acc, item) => {
        if (item.id === idToRemove) {
          return acc; // Skip this item
        }
        if (item.children) {
          item.children = removeOutlineItemRecursively(item.children, idToRemove);
        }
        acc.push(item);
        return acc;
      }, [] as OutlineItem[]);
    };

    let updatedOutlineArr: OutlineItem[] = [];
    setOutlineInternal(currentOutline => {
      updatedOutlineArr = removeOutlineItemRecursively(currentOutline, outlineItemId);
      return updatedOutlineArr;
    });

    setSaveStatus('unsaved');

    // Immediate save: Pass both updated notes and outline
    // Note: setOutline already calls saveDocumentInternal with the new outline.
    // We need to ensure the notes are also part of that save if setOutline's save hasn't run yet,
    // or if we want a single atomic update.
    // For simplicity, let saveDocumentInternal use the latest state from the hook for outline,
    // but we provide the latest notes.
    saveDocumentInternal({ latestNotes: updatedNotesArr, contentToSave: { outline: updatedOutlineArr, notes: updatedNotesArr, writing } });


    toast({
      title: 'Outline Item Deleted',
      description: `Successfully deleted outline item and its linked notes.`,
    });
  };

  // Update a note and trigger auto-save
  const updateNote = (updatedNote: Note, forceSaveNow: boolean = false) => {
    if (currentUserPermissionLevel === 'view') return;
    // Log attachment presence for debugging
    const hasAttachments = 
      (updatedNote.imageUrls && updatedNote.imageUrls.length > 0) ||
      (updatedNote.videoUrls && updatedNote.videoUrls.length > 0) ||
      (updatedNote.fileUrls && updatedNote.fileUrls.length > 0) ||
      updatedNote.primaryAssetUrl;
    
    if (hasAttachments) {
      console.log("Updating note with attachments:", {
        id: updatedNote.id,
        imageUrls: updatedNote.imageUrls,
        videoUrls: updatedNote.videoUrls,
        fileUrls: updatedNote.fileUrls,
        primaryAssetUrl: updatedNote.primaryAssetUrl
      });
    }
    
    // Use the functional update form to ensure we have the most recent state
    // and capture the updated notes array
    let updatedNotes: Note[] = [];
    setNotes(currentNotes => {
      console.log("Updating note, current count:", currentNotes.length);
      updatedNotes = currentNotes.map(note => 
        note.id === updatedNote.id ? updatedNote : note
      );
      return updatedNotes;
    });
    
    setSaveStatus('unsaved');
    
    if (forceSaveNow) {
      console.log("Force saving note with attachments immediately");
      saveDocumentInternal({ latestNotes: updatedNotes });
    } else {
      // For regular edits (e.g. title change, non-attachment content change),
      // we can also make this immediate, or use a very short debounce if needed.
      // For now, let's make it immediate for consistency with other discrete actions.
      saveDocumentInternal({ latestNotes: updatedNotes });
    }
  };

  // Helper function to delete attachments from server
  const deleteAttachmentsFromServer = async (attachmentIds: string[]) => {
    if (attachmentIds.length === 0) return;

    toast({
      title: "Cleaning up attachments...",
      description: `Deleting ${attachmentIds.length} orphaned attachment(s).`,
      duration: 3000,
    });

    for (const fileId of attachmentIds) {
      try {
        const response = await fetch(`/api/uploads/${fileId}`, { method: 'DELETE' });
        if (!response.ok) {
          console.error(`Failed to delete file ${fileId} from server:`, response.statusText);
          toast({
            title: "Attachment Deletion Error",
            description: `Could not delete attachment ${fileId}. Status: ${response.statusText}`,
            variant: "destructive",
          });
        } else {
          console.log(`Successfully deleted file ${fileId} from server`);
        }
      } catch (error) {
        console.error(`Error deleting file ${fileId} from server:`, error);
        toast({
          title: "Attachment Deletion Exception",
          description: `Error deleting attachment ${fileId}: ${error.message}`,
          variant: "destructive",
        });
      }
    }
    // Consider a summary toast after all deletions attempt
  };

  // Delete a note and its attachments, then trigger auto-save
  const deleteNote = (noteId: string) => {
    if (currentUserPermissionLevel === 'view') return;
    const noteToDelete = notes.find(note => note.id === noteId);
    if (!noteToDelete) return;

    const allAttachmentIdsInNote: string[] = [];
    if (noteToDelete.primaryAssetData?.id) {
      allAttachmentIdsInNote.push(noteToDelete.primaryAssetData.id);
    }
    (noteToDelete.imageUrlsData || []).forEach(att => allAttachmentIdsInNote.push(att.id));
    (noteToDelete.videoUrlsData || []).forEach(att => allAttachmentIdsInNote.push(att.id));
    (noteToDelete.fileUrlsData || []).forEach(att => allAttachmentIdsInNote.push(att.id));
    
    const uniqueAttachmentIdsInNote = [...new Set(allAttachmentIdsInNote)];
    const attachmentIdsToDeleteFromServer: string[] = [];

    if (uniqueAttachmentIdsInNote.length > 0) {
      const otherNotes = notes.filter(n => n.id !== noteId);
      
      uniqueAttachmentIdsInNote.forEach(attachmentId => {
        let isAttachmentUsedElsewhere = false;
        for (const otherNote of otherNotes) {
          if (otherNote.primaryAssetData?.id === attachmentId) {
            isAttachmentUsedElsewhere = true;
            break;
          }
          if ((otherNote.imageUrlsData || []).some(att => att.id === attachmentId)) {
            isAttachmentUsedElsewhere = true;
            break;
          }
          if ((otherNote.videoUrlsData || []).some(att => att.id === attachmentId)) {
            isAttachmentUsedElsewhere = true;
            break;
          }
          if ((otherNote.fileUrlsData || []).some(att => att.id === attachmentId)) {
            isAttachmentUsedElsewhere = true;
            break;
          }
        }
        if (!isAttachmentUsedElsewhere) {
          attachmentIdsToDeleteFromServer.push(attachmentId);
        }
      });
    }

    if (attachmentIdsToDeleteFromServer.length > 0) {
      deleteAttachmentsFromServer(attachmentIdsToDeleteFromServer);
    }

    // Use the functional update form to ensure we have the most recent state
    let updatedNotesArr: Note[] = [];
    setNotes(currentNotes => {
      updatedNotesArr = currentNotes.filter(note => note.id !== noteId);
      console.log("Deleting note, current count:", currentNotes.length, "new count:", updatedNotesArr.length);
      return updatedNotesArr;
    });
    
    setSaveStatus('unsaved');
    saveDocumentInternal({ latestNotes: updatedNotesArr });
  };

  const duplicateNote = (noteId: string): Note | undefined => {
    const originalNote = notes.find(n => n.id === noteId);
    if (!originalNote) {
      console.error(`Cannot duplicate note: Note with ID ${noteId} not found.`);
      toast({
        title: "Error Duplicating Note",
        description: "Original note not found.",
        variant: "destructive",
      });
      return undefined;
    }

    const newNote: Note = {
      ...originalNote, // Spread to copy all properties
      id: nanoid(), // New unique ID
      title: `${originalNote.title} (Copy)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Deep copy arrays of objects if necessary, for now shallow spread is used for Data fields
      // For URLs, shallow copy is fine.
      imageUrls: originalNote.imageUrls ? [...originalNote.imageUrls] : [],
      imageUrlsData: originalNote.imageUrlsData ? originalNote.imageUrlsData.map(d => ({...d})) : [],
      videoUrls: originalNote.videoUrls ? [...originalNote.videoUrls] : [],
      videoUrlsData: originalNote.videoUrlsData ? originalNote.videoUrlsData.map(d => ({...d})) : [],
      fileUrls: originalNote.fileUrls ? [...originalNote.fileUrls] : [],
      fileUrlsData: originalNote.fileUrlsData ? originalNote.fileUrlsData.map(d => ({...d})) : [],
      // primaryAssetData is an object, so spread it if it exists
      primaryAssetData: originalNote.primaryAssetData ? {...originalNote.primaryAssetData} : undefined,
      // linkedOutlineId remains the same
    };

    let updatedNotesArr: Note[] = [];
    setNotes(currentNotes => {
      updatedNotesArr = [...currentNotes, newNote];
      saveDocumentInternal({ latestNotes: updatedNotesArr });
      return updatedNotesArr;
    });
    setSaveStatus('unsaved');
    // saveDocumentInternal called inside setNotes callback

    toast({
      title: "Note Duplicated",
      description: `"${originalNote.title}" was successfully duplicated.`,
    });
    return newNote;
  };

  // Upload any file (image, video, or document)
  const uploadFile = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file); // Using a more generic field name
    
    try {
      console.log('Uploading file:', file.name, file.type);
      const res = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });
      
      if (!res.ok) {
        throw new Error('Failed to upload file');
      }
      
      const data = await res.json();
      console.log('File upload response:', data);
      
      // Mark document as unsaved to ensure autosave picks it up
      setSaveStatus('unsaved');
      
      // For uploaded files, we should trigger an immediate save to prevent loss
      // This ensures the file URL is saved to the server immediately
      console.log('File uploaded successfully, triggering immediate save');
      // Get the current state of notes to pass directly.
      // Note: If uploadFile itself modified `notes` (e.g. by adding a placeholder for the file),
      // then `notes` from the hook state would be correct. Assuming it doesn't modify `notes` directly
      // but the caller (e.g. `updateNote`) is responsible for putting the URL into a note.
      // Thus, the save here ensures any *other* pending changes are saved along with the one
      // that will incorporate this URL.
      saveDocumentInternal(); // Call with current hook state for notes, outline, writing
      
      return data.url;
    } catch (error) {
      console.error('Error uploading file:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload file',
        variant: 'destructive'
      });
      throw error;
    }
  };

  // Get current writing content
  const getCurrentWriting = () => {
    if (!activeOutlineItemId) return { content: '', citations: [] };
    return writing[activeOutlineItemId] || { content: '', citations: [] };
  };

  // Auto-save reference to track 20-second auto-save
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Function to schedule a 20-second auto-save
  // Accepts optional notes array to ensure the latest notes are used in saveDocumentInternal
  const scheduleAutoSave = useCallback((notesForSave?: Note[]) => {
    // Clear any existing timer
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
    }

    // Set a new 20-second timer
    autoSaveTimerRef.current = setTimeout(() => {
      if (documentId && saveStatus === 'unsaved') {
        console.log("Auto-saving document after 20 seconds via WebSocket deltas");
        // saveDocumentInternal will handle sending deltas
        saveDocumentInternal(notesForSave);
      }
    }, 20000); // 20 seconds auto-save timer
  }, [documentId, saveStatus, saveDocumentInternal]);
  
  // Helper function to generate basic empty content for sections
  // const getSampleContentForSection = useCallback((sectionId: string): string => { // This is the duplicate, removing it.
  //   // Find the section in the outline
  //   const findSection = (items: OutlineItem[]): OutlineItem | null => {
  //     for (const item of items) {
  //       if (item.id === sectionId) return item;
  //       if (item.children?.length) {
  //         const found = findSection(item.children);
  //         if (found) return found;
  //       }
  //     }
  //     return null;
  //   };
    
  //   const section = findSection(outline);
  //   if (!section) return '';
    
  //   return `<p>Click to start writing in this section...</p>`;
  // }; // End of duplicate getSampleContentForSection

  // Add a citation
  const addCitation = (referenceData?: Partial<Citation>) => {
    if (!activeOutlineItemId || currentUserPermissionLevel === 'view') return;
    
    const currentCitations = getCurrentWriting().citations || [];
    
    // Create citation with reference data when available, or use minimal placeholder
    const citationId = nanoid();
    const marker = (currentCitations.length + 1).toString();
    
    // Start with minimal citation properties
    const newCitation: Citation = {
      id: citationId,
      marker: marker,
      reference: ''  // Will be set properly below
    };
    
    // Add reference data when available
    if (referenceData) {
      console.log("Adding citation with reference data:", referenceData);
      
      // Copy all available reference data properties
      Object.assign(newCitation, {
        type: referenceData.type,
        title: referenceData.title,
        authors: referenceData.authors || [],
        year: referenceData.year || new Date().getFullYear().toString(),
        url: referenceData.url,
        doi: referenceData.doi,
        publisher: referenceData.publisher,
        journal: referenceData.journal,
        volume: referenceData.volume,
        issue: referenceData.issue,
        pages: referenceData.pages,
        collectionId: referenceData.collectionId,
        // Store original reference object for future reference
        originalReferenceId: referenceData.id
      });
      
      // If reference text is directly provided, use it
      if (referenceData.reference && referenceData.reference !== "Author, A. (2023). Title of the work. Publisher.") {
        newCitation.reference = referenceData.reference;
        console.log("Using provided reference text:", referenceData.reference);
      } 
      // Otherwise, generate a properly formatted reference based on available data
      else if (referenceData.authors?.length || referenceData.title) {
        // Get author information or use placeholder only if no author data exists
        const authorText = referenceData.authors && referenceData.authors.length > 0 
          ? referenceData.authors.join(', ')
          : '';
        
        // Get remaining citation components
        const title = referenceData.title || '';
        const year = referenceData.year || new Date().getFullYear().toString();
        const publisher = referenceData.publisher || '';
        const journal = referenceData.journal || '';
        
        // Build a properly formatted citation based on type
        if (referenceData.type === 'book') {
          newCitation.reference = `${authorText}${authorText ? '. ' : ''}(${year}). ${title}${title ? '. ' : ''}${publisher}`;
        } else if (referenceData.type === 'article') {
          newCitation.reference = `${authorText}${authorText ? '. ' : ''}(${year}). ${title}${title ? '. ' : ''}${journal}${journal ? ', ' : ''}${referenceData.volume || ''}${referenceData.pages ? ', ' + referenceData.pages : ''}`;
        } else {
          // Default format for other types
          newCitation.reference = `${authorText}${authorText ? '. ' : ''}(${year}). ${title}${publisher ? '. ' + publisher : ''}`;
        }
        
        console.log("Generated formatted reference:", newCitation.reference);
      }
    }
    
    // If no reference information was provided, use a descriptive placeholder instead of generic text
    if (!newCitation.reference) {
      // Try to build from title and year if available
      if (newCitation.title) {
        newCitation.reference = `${newCitation.title}${newCitation.year ? ' (' + newCitation.year + ')' : ''}`;
      } else {
        // Use a more descriptive placeholder that doesn't look like final content
        newCitation.reference = `Reference #${marker} (click to edit citation details)`;
      }
    }
    
    // Avoid the generic placeholder text
    if (newCitation.reference === "Author, A. (2023). Title of the work. Publisher.") {
      if (newCitation.title && newCitation.authors && newCitation.authors.length > 0) {
        newCitation.reference = `${newCitation.authors.join(', ')}. (${newCitation.year || '2023'}). ${newCitation.title}.`;
      } else {
        newCitation.reference = `Reference #${marker} (click to edit citation details)`;
      }
    }
    
    // Make sure we have title set
    if (!newCitation.title && newCitation.reference) {
      // Extract a title from the reference if possible
      const firstSentence = newCitation.reference.split('.')[0];
      newCitation.title = firstSentence;
    }
    
    setWriting({
      ...writing,
      [activeOutlineItemId]: {
        ...getCurrentWriting(),
        citations: [...currentCitations, newCitation]
      }
    });
    
    setSaveStatus('unsaved');
    // Immediate save
    // Need to ensure saveDocumentInternal gets the updated writing state
    const newWritingState = {
      ...writing,
      [activeOutlineItemId]: {
        ...getCurrentWriting(),
        citations: [...currentCitations, newCitation]
      }
    };
    saveDocumentInternal({ contentToSave: { outline, notes, writing: newWritingState } });
    return newCitation;
  };

  // Update a citation
  const updateCitation = (updatedCitation: Citation) => {
    if (!activeOutlineItemId || currentUserPermissionLevel === 'view') return;
    
    const currentCitations = getCurrentWriting().citations || [];
    const newWritingState = {
      ...writing,
      [activeOutlineItemId]: {
        ...getCurrentWriting(),
        citations: currentCitations.map(citation => 
          citation.id === updatedCitation.id ? updatedCitation : citation
        )
      }
    };
    setWriting(newWritingState); // Update local state first
    setSaveStatus('unsaved');
    // Immediate save
    saveDocumentInternal({ contentToSave: { outline, notes, writing: newWritingState } });
  };

  // Delete a citation
  const deleteCitation = (citationId: string) => {
    if (!activeOutlineItemId || currentUserPermissionLevel === 'view') return;
    
    const currentCitations = getCurrentWriting().citations || [];
    const newWritingState = {
      ...writing,
      [activeOutlineItemId]: {
        ...getCurrentWriting(),
        citations: currentCitations.filter(citation => citation.id !== citationId)
      }
    };
    setWriting(newWritingState); // Update local state first
    setSaveStatus('unsaved');
    // Immediate save
    saveDocumentInternal({ contentToSave: { outline, notes, writing: newWritingState } });
  };

  // Count words in the document across all writing sections
  const getWordCount = (): number => {
    // Count words across all writing sections, not just the active one
    let totalWordCount = 0;
    const sectionCounts = {}; // For debugging
    
    // Make sure writing object exists
    if (!writing || typeof writing !== 'object') {
      console.log('Word count: writing object is invalid or empty');
      return 0;
    }
    
    try {
      // Get the number of sections
      const numSections = Object.keys(writing).length;
      console.log(`Word count: calculating across ${numSections} writing sections`);
      
      // Iterate through all writing sections and count words in each
      Object.entries(writing).forEach(([sectionId, section]) => {
        if (section && typeof section === 'object') {
          const content = section.content || '';
          const wordCount = content.split(/\s+/).filter(Boolean).length;
          sectionCounts[sectionId] = wordCount;
          totalWordCount += wordCount;
        }
      });
      
      // Log detailed breakdown for debugging
      console.log('Word count breakdown by section:', sectionCounts);
      console.log('Total word count:', totalWordCount);
    } catch (error) {
      console.error('Error counting words:', error);
    }
    
    return totalWordCount;
  };

  // Check spelling and grammar
  const checkText = async (type: 'spelling' | 'grammar') => {
    if (!activeOutlineItemId) return;
    
    const content = getCurrentWriting().content || '';
    
    try {
      const res = await apiRequest('POST', '/api/check-text', {
        text: content,
        type
      });
      
      const data = await res.json();
      
      if (data.errors.length > 0) {
        toast({
          title: `${type === 'spelling' ? 'Spelling' : 'Grammar'} check`,
          description: `Found ${data.errors.length} ${data.errors.length === 1 ? 'error' : 'errors'}`,
          variant: 'default'
        });
      } else {
        toast({
          title: `${type === 'spelling' ? 'Spelling' : 'Grammar'} check`,
          description: 'No errors found',
          variant: 'default'
        });
      }
      
      return data.errors;
    } catch (error) {
      console.error(`Error checking ${type}:`, error);
      toast({
        title: 'Error',
        description: `Failed to check ${type}`,
        variant: 'destructive'
      });
      return [];
    }
  };

  const updateDocumentFormat = async (newFormat: DocumentFormat) => {
    if (!documentId || !metadata) {
      toast({ title: 'Error', description: 'Document or metadata not loaded.', variant: 'destructive' });
      return;
    }

    try {
      const adjustedMetadata = getAdjustedMetadataForFormat(metadata, newFormat, title);

      setDocumentFormat(newFormat);
      setMetadata(adjustedMetadata);

      if (adjustedMetadata.mainTitle && adjustedMetadata.mainTitle !== title) {
        setTitle(adjustedMetadata.mainTitle);
        // Title is saved separately by saveDocumentInternal or a dedicated title save,
        // but export-settings should have the latest title.
      }

      await apiRequest('POST', `/api/documents/${documentId}/export-settings`, { format: newFormat, metadata: adjustedMetadata });

      toast({ title: 'Format Updated', description: `Document format changed to ${newFormat}.` });
      setSaveStatus('saved'); // Reflect that settings are saved
    } catch (error) {
      console.error('Error updating document format:', error);
      toast({ title: 'Error', description: 'Failed to update document format.', variant: 'destructive' });
      setSaveStatus('unsaved'); // Revert to unsaved if error
    }
  };
  const updateMetadata = async (newMetadataPartial: Partial<DocumentExportMetadata>) => {
    if (!documentId || !metadata || !documentFormat) {
      toast({ title: 'Error', description: 'Document, metadata, or format not loaded.', variant: 'destructive' });
      return;
    }

    try {
      const updatedMetadata = { ...metadata, ...newMetadataPartial };
      setMetadata(updatedMetadata);

      if (newMetadataPartial.mainTitle && newMetadataPartial.mainTitle !== title) {
        setTitle(newMetadataPartial.mainTitle);
        // Attempt to save title separately as it's often a primary field
        try {
          await apiRequest('PATCH', `/api/documents/${documentId}/title`, { title: newMetadataPartial.mainTitle });
          queryClient.invalidateQueries({ queryKey: ["/api/documents"] }); // Invalidate dashboard list
        } catch (titleError) {
          console.error('Error saving title separately:', titleError);
          // Proceed with saving full metadata, but maybe log this or show a specific warning
        }
      }

      await apiRequest('POST', `/api/documents/${documentId}/export-settings`, { format: documentFormat, metadata: updatedMetadata });

      toast({ title: 'Metadata Updated', description: 'Document metadata saved successfully.' });
      setSaveStatus('saved'); // Reflect that settings are saved
    } catch (error) {
      console.error('Error updating metadata:', error);
      toast({ title: 'Error', description: 'Failed to update metadata.', variant: 'destructive' });
      setSaveStatus('unsaved'); // Revert to unsaved if error
    }
  };

  const sendChatMessage = useCallback((messageText: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !documentId) {
      console.error("WebSocket not connected or documentId not set, cannot send chat message.");
      toast({ title: "Chat Error", description: "Cannot send message. Not connected.", variant: "destructive" });
      return;
    }
    if (!messageText.trim()) {
      return; // Do not send empty messages
    }

    const messagePayload: ChatMessageSentChange = {
      type: 'documentChange',
      elementType: DocumentChangeType.CHAT_MESSAGE_SENT,
      elementId: documentId,
      payload: { messageText: messageText.trim() },
    };
    wsRef.current.send(JSON.stringify(messagePayload));
    // console.log("Sent CHAT_MESSAGE_SENT:", messagePayload); // Optional: for debugging
  }, [documentId, toast]);

  // Function to refresh document content (useful when popout windows are closed)
  const refreshDocumentContent = useCallback(async () => {
    if (!documentId) return;

    try {
      console.log('Refreshing document content...');
      const contentRes = await fetch(`/api/documents/${documentId}/content`);

      if (!contentRes.ok) {
        throw new Error(`Failed to refresh document content: ${contentRes.statusText}`);
      }

      const content: DocumentContentData = await contentRes.json();
      console.log("Content refreshed:", content);
      console.log("Notes in refreshed content:", content.notes?.map(n => ({ id: n.id, title: n.title, linkedOutlineId: n.linkedOutlineId })));

      setOutlineInternal(content.outline || []);
      setNotes(content.notes || []);
      setWriting(content.writing || {});
      previousContentRef.current = JSON.parse(JSON.stringify(content));

      console.log('Document content refreshed successfully');
    } catch (error) {
      console.error('Error refreshing document content:', error);
      toast({
        title: "Refresh Error",
        description: "Failed to refresh document content",
        variant: "destructive"
      });
    }
  }, [documentId, toast]);

  return { // This is the original, comprehensive return statement
    documentId,
    title,
    setTitle,
    outline,
    setOutline,
    notes,
    createNote,
    updateNote,
    deleteNote,
    activeOutlineItemId,
    setActiveOutlineItemId,
    saveStatus,
    saveDocument,
    uploadFile,
    getCurrentWriting,
    updateWritingContent,
    addCitation,
    updateCitation,
    deleteCitation,
    getWordCount,
    checkText,
    isLoading,       // From actual useState
    documentFormat,  // From actual useState
    metadata,        // From actual useState
    updateDocumentFormat,
    updateMetadata,
    linkNoteToOutlineItem,
    deleteOutlineItemWithNotes,
    duplicateNote,
    presentUsers, // Added presentUsers to the return object
    chatMessages, // Added for chat
    sendChatMessage, // Added for chat
    refreshDocumentContent, // Added refresh function
    error, // Added error state to return
    currentUserPermissionLevel, // Added permission level to return
  };
}

// Helper type for the sendChatMessage payload (already defined in shared/schema.ts as ChatMessageSentPayload)
// export interface ChatMessageSentPayload {
//   messageText: string;
// }
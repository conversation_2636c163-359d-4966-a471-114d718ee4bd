import React, { useState, useEffect } from 'react';
import { Header } from '@/components/layout/header';
import { DocumentTitle } from '@/components/layout/document-title';
import { Footer } from '@/components/layout/footer';
import { OutlinePanel } from '@/components/outline';
import { WritingPanel } from '@/components/writing/panel';
import { SubscriptionModal } from '@/components/subscription-modal';
import { ExportDocumentModal, DocumentMetadata } from '@/components/export-document-modal';
import { SimpleExportModal } from '@/components/simple-export-modal';
import { DebugConsole } from '@/components/ui/debug-console';
import { useDocument } from '@/hooks/use-document';
import { useToast } from '@/hooks/use-toast';
import { calculateOutlineNumbers } from '@/lib/utils/outline';
import { exportDocument, downloadBlob } from '@/lib/services/document-export';

export default function Home() {
  const [activePanel, setActivePanel] = useState<'outline' | 'writing'>('outline');
  const [isSubscriptionModalOpen, setIsSubscriptionModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const { toast } = useToast();
  
  const {
    title,
    setTitle,
    outline,
    setOutline,
    notes,
    createNote,
    updateNote,
    deleteNote,
    activeOutlineItemId,
    setActiveOutlineItemId,
    saveStatus,
    saveDocument,
    uploadFile,
    getCurrentWriting,
    updateWritingContent,
    addCitation,
    updateCitation,
    deleteCitation,
    getWordCount,
    checkText
  } = useDocument();

  // Find the active outline item
  const activeOutlineItem = outline.flatMap(item => {
    const result = [item];
    if (item.children) result.push(...item.children);
    return result;
  }).find(item => item.id === activeOutlineItemId) || null;

  const handleSave = () => {
    saveDocument();
  };
  
  const handleSpellCheck = () => {
    checkText('spelling');
  };
  
  const handleGrammarCheck = () => {
    checkText('grammar');
  };
  
  const handleAddOutlineItem = (parentId?: string) => {
    if (parentId) {
      // Add a child to an existing item
      const updatedOutline = outline.map(item => {
        if (item.id === parentId) {
          return {
            ...item,
            children: [
              ...(item.children || []),
              {
                id: crypto.randomUUID(),
                number: '',
                title: 'New Subsection',
                children: []
              }
            ]
          };
        }
        
        if (item.children) {
          return {
            ...item,
            children: item.children.map(child => {
              if (child.id === parentId) {
                return {
                  ...child,
                  children: [
                    ...(child.children || []),
                    {
                      id: crypto.randomUUID(),
                      number: '',
                      title: 'New Subsection',
                      children: []
                    }
                  ]
                };
              }
              return child;
            })
          };
        }
        
        return item;
      });
      
      setOutline(calculateOutlineNumbers(updatedOutline));
    } else {
      // Add a top-level item
      const newItem = {
        id: crypto.randomUUID(),
        number: '',
        title: 'New Section',
        children: []
      };
      
      const updatedOutline = [...outline, newItem];
      setOutline(calculateOutlineNumbers(updatedOutline));
    }
  };
  
  const handleImportOutline = () => {
    toast({
      title: "Import Outline",
      description: "This feature is not implemented yet.",
    });
  };
  
  const handleImportNote = () => {
    toast({
      title: "Import Note",
      description: "This feature is not implemented yet.",
    });
  };
  
  const handleExport = () => {
    console.log('Home.handleExport called');
    console.log('Current state of isExportModalOpen:', isExportModalOpen);
    setIsExportModalOpen(true);
    
    // Verify the state was updated
    setTimeout(() => {
      console.log('isExportModalOpen after update:', isExportModalOpen);
    }, 0);
  };
  
  const handleDocumentExport = async (format: string, metadata: DocumentMetadata) => {
    try {
      const currentWriting = getCurrentWriting();
      
      // Generate the document
      const blob = await exportDocument({
        format: format as any,
        metadata,
        content: currentWriting.content || '',
        outline,
        notes,
        citations: currentWriting.citations || [],
      });
      
      // Download the file
      const sanitizedTitle = title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
      downloadBlob(blob, `${sanitizedTitle}.docx`);
      
      toast({
        title: "Document Exported",
        description: "Your document has been successfully exported.",
      });
    } catch (error) {
      console.error('Error exporting document:', error);
      toast({
        title: "Export Failed",
        description: "There was an error exporting your document. Please try again.",
        variant: "destructive",
      });
    }
  };
  
  const handleReportIssue = () => {
    toast({
      title: "Report Issue",
      description: "This feature is not implemented yet.",
    });
  };
  
  const handleHelp = () => {
    toast({
      title: "Help",
      description: "This feature is not implemented yet.",
    });
  };
  
  const handleUpgrade = (plan: 'monthly' | 'annual') => {
    toast({
      title: "Subscription",
      description: `You selected the ${plan} plan. This feature is not implemented yet.`,
    });
    setIsSubscriptionModalOpen(false);
  };

  // Get current writing content and citations
  const currentWriting = getCurrentWriting();

  return (
    <div className="bg-background font-sans text-foreground min-h-screen flex flex-col">
      <Header
        onSave={handleSave}
        onSaveAndClose={() => {}}
        onSpellCheck={handleSpellCheck}
        onGrammarCheck={handleGrammarCheck}
        onShare={() => {}}
        onUserMenuToggle={() => {}}
      />
      
      <DocumentTitle
        title={title}
        onTitleChange={setTitle}
        saveStatus={saveStatus === 'saved' ? 'saved' : 'draft'}
      />
      
      <main className="container mx-auto px-4 flex-grow flex flex-col md:flex-row gap-0 overflow-hidden">
        <div className="flex flex-1 overflow-hidden border rounded-lg shadow-sm bg-card">
          {/* Panel navigation */}
          <div className="border-r border-border bg-accent w-16 shrink-0 flex flex-col items-center py-4">
            <button 
              className={`w-10 h-10 flex items-center justify-center rounded-lg mb-1 ${
                activePanel === 'outline' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
              }`}
              onClick={() => setActivePanel('outline')}
            >
              <i className="ri-list-check text-xl"></i>
            </button>
            

            
            <button 
              className={`w-10 h-10 flex items-center justify-center rounded-lg mb-1 ${
                activePanel === 'writing' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
              }`}
              onClick={() => setActivePanel('writing')}
            >
              <i className="ri-file-text-line text-xl"></i>
            </button>
            
            <div className="mt-auto">
              <button 
                className="w-10 h-10 flex items-center justify-center rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-colors"
              >
                <i className="ri-settings-4-line text-xl"></i>
              </button>
            </div>
          </div>

          {/* Panels */}
          {activePanel === 'outline' && (
            <OutlinePanel
              outline={outline}
              onOutlineChange={setOutline}
              onAddItem={handleAddOutlineItem}
              onImportOutline={handleImportOutline}
            />
          )}
          

          
          {activePanel === 'writing' && (
            <WritingPanel
              outline={outline}
              activeOutlineItemId={activeOutlineItemId}
              notes={notes}
              content={currentWriting.content || ''}
              citations={currentWriting.citations || []}
              onContentChange={updateWritingContent}
              onAddCitation={addCitation}
              onUpdateCitation={updateCitation}
              onDeleteCitation={deleteCitation}
              onGrammarCheck={() => checkText('grammar')}
              onExport={handleExport}
              onOutlineItemSelect={setActiveOutlineItemId}
              onShowSubscriptionModal={() => setIsSubscriptionModalOpen(true)}
            />
          )}
        </div>
      </main>
      
      <Footer
        wordCount={getWordCount()}
        saveStatus={saveStatus}
        onReportIssue={handleReportIssue}
        onHelp={handleHelp}
      />
      
      <SubscriptionModal
        isOpen={isSubscriptionModalOpen}
        onClose={() => setIsSubscriptionModalOpen(false)}
        onUpgrade={handleUpgrade}
      />
      
      {/* Try using the simple export modal instead of the complex one */}
      <SimpleExportModal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        onExport={handleDocumentExport}
        documentTitle={title}
      />
      
      <DebugConsole />
    </div>
  );
}

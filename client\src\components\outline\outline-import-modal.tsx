import React, { useState, useRef } from 'react';
import { OutlineItem } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Upload, File, Loader2, FileText, FileSpreadsheet } from 'lucide-react';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface OutlineImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (outline: OutlineItem[]) => void;
}

/**
 * Parses text content with indentation-based hierarchy
 * Supports spaces, tabs, and mixed indentation.
 */
function parseIndentedText(text: string): OutlineItem[] {
  const lines = text.trim().split('\n');
  if (lines.length === 0) return [];

  const result: OutlineItem[] = [];
  let stackItems: { item: OutlineItem, indent: number }[] = [];
  let currentIndent = 0;
  
  // Analyze the first few lines to determine indentation style
  const indentSizes: number[] = [];
  for (let i = 0; i < Math.min(lines.length, 5); i++) {
    const leadingSpaces = lines[i].match(/^(\s*)/)?.[1].length || 0;
    if (leadingSpaces > 0) {
      indentSizes.push(leadingSpaces);
    }
  }
  
  // Find most common indentation size (or default to 2)
  const indentSize = indentSizes.length > 0 
    ? Math.min(...indentSizes.filter(s => s > 0)) 
    : 2;

  for (const line of lines) {
    if (!line.trim()) continue; // Skip empty lines
    
    // Calculate indentation level
    const leadingSpaces = line.match(/^(\s*)/)?.[1].length || 0;
    const level = Math.floor(leadingSpaces / indentSize);
    
    // Create new item
    const item: OutlineItem = {
      id: crypto.randomUUID(),
      number: '',
      title: line.trim().replace(/^[0-9.]*[\s.]*/, '').trim(), // Remove any leading numbers/bullets
      children: []
    };
    
    if (level === 0) {
      // Top-level item
      result.push(item);
      stackItems = [{ item, indent: level }];
    } else if (level > currentIndent) {
      // Child of previous item
      const parent = stackItems[stackItems.length - 1].item;
      if (!parent.children) parent.children = [];
      parent.children.push(item);
      stackItems.push({ item, indent: level });
    } else {
      // Find the appropriate parent at this level or above
      while (stackItems.length > 0 && stackItems[stackItems.length - 1].indent >= level) {
        stackItems.pop();
      }
      
      if (stackItems.length === 0) {
        // If we've popped everything, this is a top-level item
        result.push(item);
        stackItems = [{ item, indent: level }];
      } else {
        // Add to the parent at the appropriate level
        const parent = stackItems[stackItems.length - 1].item;
        if (!parent.children) parent.children = [];
        parent.children.push(item);
        stackItems.push({ item, indent: level });
      }
    }
    
    currentIndent = level;
  }
  
  return result;
}

/**
 * Parses text content with bullet or number-based hierarchy
 * Supports various formats like *, -, 1., 1), I., A., etc.
 */
function parseBulletedText(text: string): OutlineItem[] {
  const lines = text.trim().split('\n');
  if (lines.length === 0) return [];

  const result: OutlineItem[] = [];
  let stackItems: { item: OutlineItem, level: number }[] = [];
  
  // Regex patterns for different types of list markers
  const bulletPattern = /^(\s*)([*\-•○◦⁃⦾⦿]+)\s+(.+)$/;
  const numberPattern = /^(\s*)(\d+[.)]\s+|\w+[.)]\s+|[ivxIVX]+[.)]\s+)(.+)$/;
  
  for (const line of lines) {
    if (!line.trim()) continue; // Skip empty lines
    
    // Try to match bullet points or numbered lists
    const bulletMatch = line.match(bulletPattern);
    const numberMatch = line.match(numberPattern);
    const match = bulletMatch || numberMatch;
    
    if (!match) {
      // If no bullet/number found, treat as plain text at current level
      const item: OutlineItem = {
        id: crypto.randomUUID(),
        number: '',
        title: line.trim(),
        children: []
      };
      
      if (stackItems.length === 0) {
        result.push(item);
      } else {
        const parent = stackItems[stackItems.length - 1].item;
        if (!parent.children) parent.children = [];
        parent.children.push(item);
      }
      continue;
    }
    
    const [, indent, marker, content] = match;
    const level = indent.length;
    
    // Create new item
    const item: OutlineItem = {
      id: crypto.randomUUID(),
      number: '',
      title: content.trim(),
      children: []
    };
    
    if (stackItems.length === 0) {
      // First item
      result.push(item);
      stackItems.push({ item, level });
    } else if (level > stackItems[stackItems.length - 1].level) {
      // Child of previous item
      const parent = stackItems[stackItems.length - 1].item;
      if (!parent.children) parent.children = [];
      parent.children.push(item);
      stackItems.push({ item, level });
    } else {
      // Pop stack until we find a parent with lower level
      while (stackItems.length > 0 && stackItems[stackItems.length - 1].level >= level) {
        stackItems.pop();
      }
      
      if (stackItems.length === 0) {
        // If we've popped everything, this is a top-level item
        result.push(item);
      } else {
        // Add to the parent at the appropriate level
        const parent = stackItems[stackItems.length - 1].item;
        if (!parent.children) parent.children = [];
        parent.children.push(item);
      }
      
      stackItems.push({ item, level });
    }
  }
  
  return result;
}

export function OutlineImportModal({ isOpen, onClose, onImport }: OutlineImportModalProps) {
  const [text, setText] = useState('');
  const [format, setFormat] = useState('auto');
  const [previewMode, setPreviewMode] = useState(false);
  const [parsedOutline, setParsedOutline] = useState<OutlineItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
    setPreviewMode(false);
  };
  
  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };
  
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setIsLoading(true);
    
    try {
      // Handle different file types
      if (file.type === 'text/plain' || 
          file.name.endsWith('.txt') || 
          file.name.endsWith('.md') ||
          file.type === 'text/markdown') {
        // Plain text files
        const content = await file.text();
        setText(content);
        toast({
          title: 'File loaded',
          description: `Imported text from "${file.name}"`,
        });
      } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                file.name.endsWith('.docx')) {
        // Word documents - extract plain text using mammoth
        try {
          const arrayBuffer = await file.arrayBuffer();
          const result = await mammoth.extractRawText({ arrayBuffer });
          setText(result.value);
          toast({
            title: 'File loaded',
            description: `Imported text from Word document "${file.name}"`,
          });
          
          if (result.messages.length > 0) {
            console.log('Mammoth warnings:', result.messages);
          }
        } catch (error) {
          console.error('Error processing Word document:', error);
          toast({
            title: 'Error',
            description: 'Failed to extract text from the Word document. Please try a different file.',
            variant: 'destructive',
          });
        }
      } else if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.name.endsWith('.xlsx')) {
        // Excel documents - extract content using xlsx
        try {
          const arrayBuffer = await file.arrayBuffer();
          const workbook = XLSX.read(arrayBuffer, { type: 'array' });
          
          // Get first worksheet
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          
          // Convert to JSON
          const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          
          // Convert data to text format
          const textContent = data
            .map((row: any) => {
              // For each row in the spreadsheet
              if (Array.isArray(row)) {
                // Join cells with tabs (can be changed to another separator if needed)
                return row.join('\t');
              }
              return row?.toString() || '';
            })
            .join('\n');
          
          setText(textContent);
          toast({
            title: 'File loaded',
            description: `Imported text from Excel spreadsheet "${file.name}"`,
          });
        } catch (error) {
          console.error('Error processing Excel file:', error);
          toast({
            title: 'Error',
            description: 'Failed to extract data from the Excel file. Please try a different file.',
            variant: 'destructive',
          });
        }
      } else {
        // Try to read as text anyway
        try {
          const content = await file.text();
          setText(content);
          toast({
            title: 'File loaded',
            description: `Imported text from "${file.name}" (unrecognized format)`,
          });
        } catch (error) {
          toast({
            title: 'Error',
            description: 'Could not read file contents. Please try another file format.',
            variant: 'destructive',
          });
        }
      }
    } catch (error) {
      console.error('Error reading file:', error);
      toast({
        title: 'Error',
        description: 'Failed to read file. Please try a different file or format.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setPreviewMode(false);
      
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };
  
  const generatePreview = () => {
    try {
      let outline: OutlineItem[] = [];
      
      if (format === 'auto' || format === 'indented') {
        outline = parseIndentedText(text);
      }
      
      if ((format === 'auto' && outline.length <= 1) || format === 'bulleted') {
        outline = parseBulletedText(text);
      }
      
      setParsedOutline(outline);
      setPreviewMode(true);
      
      if (outline.length === 0) {
        toast({
          title: 'Warning',
          description: 'Could not parse any outline items from the text. Please check your format.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error parsing outline:', error);
      toast({
        title: 'Error',
        description: 'Failed to parse outline. Please check your format.',
        variant: 'destructive',
      });
    }
  };
  
  const handleImport = () => {
    if (parsedOutline.length === 0) {
      toast({
        title: 'Warning',
        description: 'No outline items to import. Please check your text or format.',
        variant: 'destructive',
      });
      return;
    }
    
    onImport(parsedOutline);
    toast({
      title: 'Success',
      description: `Imported ${parsedOutline.length} top-level outline items.`,
    });
    onClose();
  };
  
  // Recursive function to render outline items for preview
  const renderOutlinePreview = (items: OutlineItem[], level = 0) => {
    return (
      <ul className={`${level > 0 ? 'ml-4' : ''} pl-0 list-inside`}>
        {items.map((item) => (
          <li key={item.id} className="my-1">
            <div className="flex items-center">
              <span className={`font-medium ${level === 0 ? 'text-primary' : ''}`}>
                {item.title}
              </span>
            </div>
            {item.children && item.children.length > 0 && (
              renderOutlinePreview(item.children, level + 1)
            )}
          </li>
        ))}
      </ul>
    );
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Import Outline</DialogTitle>
          <DialogDescription>
            Paste your outline text below. We'll automatically convert it to the application format.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm">Format:</span>
              <Select
                value={format}
                onValueChange={(value) => {
                  setFormat(value);
                  setPreviewMode(false);
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Auto-detect format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">Auto-detect</SelectItem>
                  <SelectItem value="indented">Indented text</SelectItem>
                  <SelectItem value="bulleted">Bulleted/numbered list</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {!previewMode && (
              <Button 
                variant="outline" 
                onClick={generatePreview}
                disabled={!text.trim()}
              >
                Preview
              </Button>
            )}
          </div>
          
          <Tabs defaultValue="input" className="flex-1 flex flex-col overflow-hidden">
            <TabsList>
              <TabsTrigger value="input">Input</TabsTrigger>
              <TabsTrigger 
                value="preview" 
                disabled={!previewMode || parsedOutline.length === 0}
              >
                Preview
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="input" className="flex-1 overflow-hidden flex flex-col">
              <div className="flex flex-wrap items-center justify-center gap-2 mb-3">
                <Button 
                  type="button" 
                  variant="outline" 
                  className="flex items-center gap-2" 
                  onClick={handleFileSelect}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4" />
                  )}
                  Import from file
                </Button>
                <div className="flex flex-wrap items-center justify-center gap-2">
                  <div className="flex items-center gap-1 text-xs text-muted-foreground px-2 py-1 rounded-md bg-muted/50">
                    <File className="h-3 w-3" />
                    <span>.txt, .md</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground px-2 py-1 rounded-md bg-muted/50">
                    <FileText className="h-3 w-3" />
                    <span>.docx (Word)</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground px-2 py-1 rounded-md bg-muted/50">
                    <FileSpreadsheet className="h-3 w-3" />
                    <span>.xlsx (Excel)</span>
                  </div>
                </div>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".txt,.md,.docx,.xlsx,text/plain,text/markdown,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                />
              </div>
              <Textarea
                value={text}
                onChange={handleTextChange}
                placeholder="Paste your outline here. Supported formats:
- Indented text (spaces or tabs)
- Bulleted lists (* or -)
- Numbered lists (1. or 1))
- Roman numerals (I., II., etc.)
- Alphabetical lists (a., b., etc.)"
                className="w-full flex-1 resize-none min-h-[260px] font-mono"
              />
            </TabsContent>
            
            <TabsContent value="preview" className="flex-1 overflow-auto border rounded-md p-4">
              {previewMode && parsedOutline.length > 0 ? (
                <div className="outline-preview">
                  {renderOutlinePreview(parsedOutline)}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  Preview not available. Please generate a preview first.
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleImport}
            disabled={!previewMode || parsedOutline.length === 0}
          >
            Import Outline
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
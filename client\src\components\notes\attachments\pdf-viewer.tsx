import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { Button } from '@/components/ui/button';

// Set the worker source for PDF.js
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface PDFViewerProps {
  fileUrl: string;
  height?: string | number;
  width?: string | number;
}

export function PDFViewer({ fileUrl, height = '100%', width = '100%' }: PDFViewerProps) {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);
  
  // Ensure the worker is loaded when the component mounts
  useEffect(() => {
    if (!pdfjs.GlobalWorkerOptions.workerSrc) {
      pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
    }
  }, []);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setLoading(false);
  }

  function changePage(offset: number) {
    setPageNumber(prevPageNumber => {
      const newPageNumber = prevPageNumber + offset;
      return newPageNumber > 0 && newPageNumber <= (numPages || 1) ? newPageNumber : prevPageNumber;
    });
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }

  function zoomIn() {
    setScale(prevScale => Math.min(prevScale + 0.2, 2.5));
  }

  function zoomOut() {
    setScale(prevScale => Math.max(prevScale - 0.2, 0.5));
  }

  function resetZoom() {
    setScale(1.0);
  }

  return (
    <div className="pdf-viewer flex flex-col" style={{ width, height }}>
      {/* PDF Controls */}
      <div className="flex justify-between items-center mb-3 bg-white dark:bg-neutral-800 p-2 rounded-t-md border-b border-neutral-200 dark:border-neutral-700">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={previousPage}
            disabled={pageNumber <= 1}
            className="h-8 px-2 text-xs"
          >
            <i className="ri-arrow-left-line"></i>
          </Button>
          <span className="text-sm">
            Page {pageNumber} of {numPages || '?'}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={nextPage}
            disabled={numPages === null || pageNumber >= numPages}
            className="h-8 px-2 text-xs"
          >
            <i className="ri-arrow-right-line"></i>
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={zoomOut}
            className="h-8 px-2 text-xs"
          >
            <i className="ri-zoom-out-line"></i>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetZoom}
            className="h-8 px-2 text-xs"
          >
            {Math.round(scale * 100)}%
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={zoomIn}
            className="h-8 px-2 text-xs"
          >
            <i className="ri-zoom-in-line"></i>
          </Button>
          <a
            href={fileUrl}
            download
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex h-8 px-2 items-center justify-center text-xs rounded-md border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
          >
            <i className="ri-download-line mr-1"></i>
            Download
          </a>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="relative flex-1 overflow-auto bg-neutral-100 dark:bg-neutral-900 rounded-b-md">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-neutral-50 dark:bg-neutral-800 bg-opacity-75 dark:bg-opacity-75">
            <div className="flex flex-col items-center">
              <i className="ri-loader-4-line animate-spin text-3xl text-primary mb-2"></i>
              <span className="text-sm text-neutral-600 dark:text-neutral-300">Loading PDF...</span>
            </div>
          </div>
        )}
        
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-neutral-50 dark:bg-neutral-800">
            <div className="flex flex-col items-center max-w-md text-center p-6">
              <i className="ri-error-warning-line text-3xl text-red-500 mb-2"></i>
              <h3 className="text-lg font-medium text-neutral-800 dark:text-neutral-200 mb-2">
                Error Loading PDF
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
                There was a problem loading this PDF. Please try downloading it instead or check that the file is not corrupted.
              </p>
              <a
                href={fileUrl}
                download
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 rounded-md bg-primary text-white hover:bg-primary/90"
              >
                <i className="ri-download-line mr-2"></i>
                Download PDF
              </a>
            </div>
          </div>
        )}

        <Document
          file={fileUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={() => {
            setLoading(false);
            setError(true);
          }}
          className="flex justify-center"
        >
          <Page
            pageNumber={pageNumber}
            scale={scale}
            className="shadow-md mb-4"
            renderAnnotationLayer={true}
            renderTextLayer={true}
          />
        </Document>
      </div>
    </div>
  );
}
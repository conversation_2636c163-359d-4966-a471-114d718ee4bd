import React, { useState, useCallback } from 'react';
import { Note } from '@/lib/types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { PDFViewerBasic } from '@/components/notes/attachments/pdf-viewer-basic'; // Adjusted path
import { useToast } from '@/hooks/use-toast'; // Added

interface InlineMediaViewerProps {
  note: Note;
  onNoteUpdate: (updatedNote: Note, forceSaveNow?: boolean) => void;
  onFileUpload: (file: File) => Promise<string>; // Changed to required
}

export function InlineMediaViewer({
  note,
  onNoteUpdate,
  onFileUpload, // Now required
}: InlineMediaViewerProps) {
  const [title, setTitle] = useState(note.title || '');
  const [isDraggingOver, setIsDraggingOver] = useState(false); // Added
  const { toast } = useToast(); // Added

  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(event.target.value);
  };

  const handleTitleBlur = () => {
    if (title !== (note.title || '')) {
      onNoteUpdate({ ...note, title }, true); // forceSaveNow = true for immediate update
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleTitleBlur();
      event.currentTarget.blur(); // Remove focus from input
    }
  };

  const renderMediaContent = () => {
    const assetUrl = note.primaryAssetUrl || (note.imageUrls && note.imageUrls[0]) || (note.videoUrls && note.videoUrls[0]) || (note.fileUrls && note.fileUrls[0]);

    if (!assetUrl) {
      // Render Drop Zone
      return (
        <div
          className={`mt-2 p-4 border-2 border-dashed rounded-md flex flex-col items-center justify-center text-center transition-colors
            ${isDraggingOver ? 'border-primary bg-primary/10' : 'border-border hover:border-primary/70'}`}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          style={{ minHeight: '100px' }} // Ensure it has some height
        >
          <i className={`ri-upload-cloud-2-line text-3xl mb-2 ${isDraggingOver ? 'text-primary' : 'text-muted-foreground'}`}></i>
          <p className={`text-sm font-medium ${isDraggingOver ? 'text-primary' : 'text-muted-foreground'}`}>
            {isDraggingOver ? 'Drop to upload' : `Drag & drop ${note.type || 'file'} here`}
          </p>
        </div>
      );
    }

    switch (note.type) {
      case 'image':
        return (
          <img
            src={assetUrl}
            alt={note.title || 'Image note'}
            className="max-w-full h-auto rounded border mt-2"
          />
        );
      case 'video':
        return (
          <video
            src={assetUrl}
            controls
            className="max-w-full h-auto rounded border mt-2 bg-black"
          >
            Your browser does not support the video tag.
          </video>
        );
      case 'file':
        if (assetUrl.toLowerCase().endsWith('.pdf')) {
          return (
            <div className="mt-2" style={{ height: '300px', width: '100%' }}> {/* Default height */}
              <PDFViewerBasic fileUrl={assetUrl} height="100%" width="100%" />
            </div>
          );
        }
        return (
          <div className="mt-2 p-2 border rounded flex flex-col items-center space-y-2">
            <i className="ri-file-3-line text-4xl text-muted-foreground"></i>
            <span className="text-sm text-muted-foreground truncate max-w-xs">
              {note.title || assetUrl.split('/').pop() || 'File'}
            </span>
            <a
              href={assetUrl}
              download
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center text-xs h-8 px-2 rounded-md border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
            >
              <i className="ri-download-line mr-1"></i>
              Download File
            </a>
          </div>
        );
      default:
        return <p className="text-muted-foreground text-sm p-2">Unsupported note type for media view.</p>;
    }
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(true);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation(); // Necessary to allow dropping
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);

    const file = e.dataTransfer.files && e.dataTransfer.files[0];
    if (!file) return;
    if (!onFileUpload) { // Should not happen if prop is required, but good check
        toast({ title: "Error", description: "File upload capability is not configured.", variant: "destructive" });
        return;
    }

    // File Type Validation
    if (note.type === 'image' && !file.type.startsWith('image/')) {
      toast({ title: "Invalid File Type", description: "Please drop an image file for an image note.", variant: "destructive" });
      return;
    }
    if (note.type === 'video' && !file.type.startsWith('video/')) {
      toast({ title: "Invalid File Type", description: "Please drop a video file for a video note.", variant: "destructive" });
      return;
    }
    // For 'file' type, can add more specific client-side validation if needed

    const noteTypeName = note.type ? note.type.charAt(0).toUpperCase() + note.type.slice(1) : "File";
    toast({ title: `Uploading ${noteTypeName}...`, description: file.name });

    try {
      const uploadedUrl = await onFileUpload(file);
      if (uploadedUrl) {
        let updatedNoteData: Partial<Note> = {
          primaryAssetUrl: uploadedUrl,
          updatedAt: new Date().toISOString(),
        };

        if (note.type === 'image') updatedNoteData.imageUrls = [uploadedUrl];
        else if (note.type === 'video') updatedNoteData.videoUrls = [uploadedUrl];
        else if (note.type === 'file') updatedNoteData.fileUrls = [uploadedUrl];

        const defaultTitles = ["New Note", "Image Note", "Video Note", "File Note", note.type];
        if (note.title && defaultTitles.includes(note.title)) {
          updatedNoteData.title = file.name;
          setTitle(file.name); // Update local title state as well
        }

        const finalUpdatedNote = { ...note, ...updatedNoteData };
        onNoteUpdate(finalUpdatedNote, true);
        toast({ title: `${noteTypeName} Uploaded`, description: `${file.name} has been successfully attached.` });
      }
    } catch (error) {
      console.error(`InlineMediaViewer: Failed to upload ${note.type} file:`, error);
      toast({ title: `Error Uploading ${noteTypeName}`, description: String(error), variant: "destructive" });
    }
  };

  return (
    <div className="p-2 space-y-2 border-t">
      <Input
        type="text"
        value={title}
        onChange={handleTitleChange}
        onBlur={handleTitleBlur}
        onKeyDown={handleKeyDown}
        placeholder="Note title..."
        className="text-sm font-medium focus:ring-1 focus:ring-blue-500"
      />
      {renderMediaContent()}
      {/* Placeholder for potential file upload/replace UI */}
      {/* {onFileUpload && (
        <div className="mt-2">
          <Button variant="outline" size="sm">Replace Media (Not Implemented)</Button>
        </div>
      )} */}
    </div>
  );
}

export default InlineMediaViewer;

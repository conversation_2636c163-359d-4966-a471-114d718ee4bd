import { 
  Document, 
  Packer, 
  Paragraph, 
  TextRun, 
  AlignmentType, 
  HeadingLevel,
  BorderStyle,
  Header,
  Footer,
  PageNumber,
  PageNumberFormat,
  PageOrientation,
  HeadingStyle,
  SectionType,
  LineRuleType,
  convertInchesToTwip,
  LevelFormat,
  Tab,
  TabStopPosition,
  TabStopType,
  NumberFormat,
  VerticalAlign
} from 'docx';
import type { DocumentMetadata } from '@/components/export-document-modal';
import { OutlineItem, Citation, Note } from '@/lib/types';

type DocumentFormat = 'apa-pro' | 'apa-student' | 'mla-cover' | 'mla' | 'turabian' | 'turabian-dissertation';

export interface DocumentExportOptions {
  format: DocumentFormat;
  metadata: DocumentMetadata;
  content: string;
  outline: OutlineItem[];
  notes: Note[];
  citations: Citation[];
}

function stripHtmlTags(html: string): string {
  return html.replace(/<\/?[^>]+(>|$)/g, "");
}

/**
 * Export document based on format and metadata
 */
export async function exportDocument(options: DocumentExportOptions): Promise<Blob> {
  const { format, metadata, content, outline, citations } = options;
  
  // Common document properties
  const defaultMarginSize = convertInchesToTwip(1);
  
  // Format-specific document builders
  switch (format) {
    case 'mla':
    case 'mla-cover':
      return createMLADocument(options, format === 'mla-cover');
    case 'apa-pro':
    case 'apa-student':
      return createAPADocument(options, format === 'apa-pro');
    case 'turabian':
    case 'turabian-dissertation':
      return createTurabianDocument(options, format === 'turabian-dissertation');
    default:
      return createDefaultDocument(options);
  }
}

/**
 * Create a document with MLA formatting
 */
async function createMLADocument(options: DocumentExportOptions, includeCover: boolean): Promise<Blob> {
  const { metadata, content, outline, citations } = options;
  
  // Create document sections
  const sections = [];
  
  // Full name with credentials
  const fullName = `${metadata.firstName} ${metadata.middleName ? metadata.middleName + ' ' : ''}${metadata.lastName}`;
  
  // Cover page (optional)
  if (includeCover) {
    sections.push({
      properties: {
        page: {
          margin: {
            top: convertInchesToTwip(1),
            right: convertInchesToTwip(1),
            bottom: convertInchesToTwip(1),
            left: convertInchesToTwip(1),
          },
        },
        type: SectionType.NEXT_PAGE,
      },
      children: [
        new Paragraph({
          text: metadata.mainTitle,
          alignment: AlignmentType.CENTER,
          style: "Title",
          spacing: {
            after: 400,
            before: 4000, // Push down from top
          },
        }),
        new Paragraph({
          text: '',
          spacing: {
            after: 400,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: fullName,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.institution || "",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: `${metadata.course} - ${metadata.courseName || ""}`,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: `Instructor: ${metadata.instructor}`,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: new Date(metadata.submissionDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }),
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
      ],
    });
  }
  
  // Content section
  const contentParagraphs = [];
  
  // For MLA, add header with name and page number
  const header = new Header({
    children: [
      new Paragraph({
        children: [
          new TextRun({
            text: fullName,
            font: "Times New Roman",
            size: 24,
          }),
          new TextRun({
            text: "\t",
            font: "Times New Roman",
          }),
          new TextRun({
            children: [PageNumber.CURRENT],
            font: "Times New Roman",
            size: 24,
          }),
        ],
        alignment: AlignmentType.RIGHT,
        tabStops: [
          {
            type: TabStopType.RIGHT,
            position: TabStopPosition.MAX,
          },
        ],
      }),
    ],
  });
  
  // If we're not including a cover page, add student info at the top of the first page
  if (!includeCover) {
    contentParagraphs.push(
      new Paragraph({
        children: [
          new TextRun({
            text: fullName,
            font: "Times New Roman",
            size: 24,
          }),
        ],
        spacing: {
          after: 240,
          line: 480,
          lineRule: LineRuleType.EXACT,
        },
      }),
      new Paragraph({
        children: [
          new TextRun({
            text: metadata.instructor,
            font: "Times New Roman",
            size: 24,
          }),
        ],
        spacing: {
          after: 240,
          line: 480,
          lineRule: LineRuleType.EXACT,
        },
      }),
      new Paragraph({
        children: [
          new TextRun({
            text: `${metadata.course} ${metadata.courseName ? '- ' + metadata.courseName : ''}`,
            font: "Times New Roman",
            size: 24,
          }),
        ],
        spacing: {
          after: 240,
          line: 480,
          lineRule: LineRuleType.EXACT,
        },
      }),
      new Paragraph({
        children: [
          new TextRun({
            text: new Date(metadata.submissionDate).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            font: "Times New Roman",
            size: 24,
          }),
        ],
        spacing: {
          after: 480,
          line: 480,
          lineRule: LineRuleType.EXACT,
        },
      })
    );
  }
  
  // Add title centered
  contentParagraphs.push(
    new Paragraph({
      children: [
        new TextRun({
          text: metadata.mainTitle,
          font: "Times New Roman",
          size: 24,
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 480,
        line: 480,
        lineRule: LineRuleType.EXACT,
      },
    })
  );
  
  // Add content with double spacing
  const contentText = stripHtmlTags(content);
  const contentLines = contentText
    .split('\n')
    .filter(para => para.trim() !== "");
  
  for (const para of contentLines) {
    // Check if paragraph matches an outline item (for headings)
    const outlineItem = outline.find(item => item.title === para.trim());
    
    if (outlineItem) {
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: para,
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          spacing: {
            before: 480,
            after: 240,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        })
      );
    } else {
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: para,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          indent: {
            firstLine: convertInchesToTwip(0.5),
          },
          spacing: {
            after: 0,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        })
      );
    }
  }
  
  // Add works cited section if there are citations
  if (citations.length > 0) {
    contentParagraphs.push(
      new Paragraph({
        text: "",
        pageBreakBefore: true,
      }),
      new Paragraph({
        children: [
          new TextRun({
            text: "Works Cited",
            font: "Times New Roman",
            size: 24,
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 480,
          line: 480,
          lineRule: LineRuleType.EXACT,
        },
      })
    );
    
    // Sort citations alphabetically
    const sortedCitations = [...citations].sort((a, b) => a.reference.localeCompare(b.reference));
    
    // Add each citation with proper MLA format (hanging indent)
    for (const citation of sortedCitations) {
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: citation.reference,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          indent: {
            hanging: convertInchesToTwip(0.5),
          },
          spacing: {
            after: 240,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        })
      );
    }
  }
  
  sections.push({
    properties: {
      page: {
        margin: {
          top: convertInchesToTwip(1),
          right: convertInchesToTwip(1),
          bottom: convertInchesToTwip(1),
          left: convertInchesToTwip(1),
        },
      },
      type: includeCover ? SectionType.NEXT_PAGE : SectionType.CONTINUOUS,
    },
    headers: {
      default: header,
    },
    children: contentParagraphs,
  });
  
  // Create the document with MLA format
  const doc = new Document({
    features: {
      updateFields: true,
    },
    sections: sections,
    styles: {
      default: {
        document: {
          run: {
            font: "Times New Roman",
            size: 24, // 12pt
          },
          paragraph: {
            spacing: {
              line: 480, // Double spacing
              lineRule: LineRuleType.EXACT,
            },
          },
        },
        paragraphStyles: [
          {
            id: "Title",
            name: "Title",
            basedOn: "Normal",
            next: "Normal",
            run: {
              font: "Times New Roman",
              size: 24,
              bold: false,
            },
            paragraph: {
              alignment: AlignmentType.CENTER,
              spacing: {
                after: 240,
                line: 480,
                lineRule: LineRuleType.EXACT,
              },
            },
          },
        ],
      },
    },
  });
  
  // Generate the Word document as a blob
  return await Packer.toBlob(doc);
}

/**
 * Create a document with APA formatting
 */
async function createAPADocument(options: DocumentExportOptions, isProfessional: boolean): Promise<Blob> {
  const { metadata, content, outline, citations } = options;
  
  // Create document sections
  const sections = [];
  
  // Full name and running head
  const fullName = `${metadata.firstName} ${metadata.middleName ? metadata.middleName + ' ' : ''}${metadata.lastName}`;
  const runningHead = metadata.shortTitle ? 
    `RUNNING HEAD: ${metadata.shortTitle.toUpperCase().substring(0, 50)}` : 
    `RUNNING HEAD: ${metadata.mainTitle.toUpperCase().substring(0, 50)}`;
  
  // Title page header
  const titlePageHeader = new Header({
    children: [
      new Paragraph({
        children: [
          new TextRun({
            text: runningHead,
            font: "Times New Roman",
            size: 22,
          }),
          new TextRun({
            text: "\t",
            font: "Times New Roman",
          }),
          new TextRun({
            children: [PageNumber.CURRENT],
            font: "Times New Roman",
            size: 22,
          }),
        ],
        alignment: AlignmentType.LEFT,
        tabStops: [
          {
            type: TabStopType.RIGHT,
            position: TabStopPosition.MAX,
          },
        ],
      }),
    ],
  });
  
  // Regular header for rest of document
  const regularHeader = new Header({
    children: [
      new Paragraph({
        children: [
          new TextRun({
            text: metadata.shortTitle ? metadata.shortTitle.toUpperCase().substring(0, 50) : metadata.mainTitle.toUpperCase().substring(0, 50),
            font: "Times New Roman",
            size: 22,
          }),
          new TextRun({
            text: "\t",
            font: "Times New Roman",
          }),
          new TextRun({
            children: [PageNumber.CURRENT],
            font: "Times New Roman",
            size: 22,
          }),
        ],
        alignment: AlignmentType.LEFT,
        tabStops: [
          {
            type: TabStopType.RIGHT,
            position: TabStopPosition.MAX,
          },
        ],
      }),
    ],
  });
  
  // Title page
  sections.push({
    properties: {
      page: {
        margin: {
          top: convertInchesToTwip(1),
          right: convertInchesToTwip(1),
          bottom: convertInchesToTwip(1),
          left: convertInchesToTwip(1),
        },
      },
      type: SectionType.NEXT_PAGE,
      titlePage: true,
    },
    headers: {
      default: titlePageHeader,
    },
    children: [
      // Center contents vertically on page
      new Paragraph({
        text: "",
        spacing: { before: 1440 }, // Space at top of page
      }),
      new Paragraph({
        children: [
          new TextRun({
            text: metadata.mainTitle.toUpperCase(),
            font: "Times New Roman",
            size: 24,
            bold: true,
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 240,
        },
      }),
      
      // Add subtitle if available
      ...(metadata.subtitle ? [
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.subtitle,
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 480,
          },
        }),
      ] : []),
      
      // Author information
      new Paragraph({
        children: [
          new TextRun({
            text: fullName,
            font: "Times New Roman",
            size: 24,
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 240,
        },
      }),
      new Paragraph({
        children: [
          new TextRun({
            text: metadata.institution || "",
            font: "Times New Roman",
            size: 24,
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 960, // Extra space
        },
      }),
      
      // Author note (professional format)
      ...(isProfessional && metadata.authorNote ? [
        new Paragraph({
          children: [
            new TextRun({
              text: "Author Note",
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.authorNote,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          spacing: {
            after: 240,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        }),
      ] : []),
    ],
  });
  
  // Abstract page (only for professional papers)
  if (isProfessional && metadata.abstract) {
    sections.push({
      properties: {
        page: {
          margin: {
            top: convertInchesToTwip(1),
            right: convertInchesToTwip(1),
            bottom: convertInchesToTwip(1),
            left: convertInchesToTwip(1),
          },
        },
        type: SectionType.NEXT_PAGE,
      },
      headers: {
        default: regularHeader,
      },
      children: [
        new Paragraph({
          children: [
            new TextRun({
              text: "Abstract",
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.abstract,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          spacing: {
            after: 240,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        }),
        ...(metadata.keywords ? [
          new Paragraph({
            children: [
              new TextRun({
                text: "Keywords: " + metadata.keywords,
                font: "Times New Roman",
                size: 24,
                italics: true,
              }),
            ],
            spacing: {
              after: 240,
              line: 480,
              lineRule: LineRuleType.EXACT,
            },
          }),
        ] : []),
      ],
    });
  }
  
  // Main content section
  const contentParagraphs = [];
  
  // Add title at the top of the content
  contentParagraphs.push(
    new Paragraph({
      children: [
        new TextRun({
          text: metadata.mainTitle,
          font: "Times New Roman",
          size: 24,
          bold: true,
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 480,
        line: 480,
        lineRule: LineRuleType.EXACT,
      },
    })
  );
  
  // Add content with double spacing
  const contentText = stripHtmlTags(content);
  const contentLines = contentText
    .split('\n')
    .filter(para => para.trim() !== "");
  
  for (const para of contentLines) {
    // Check if paragraph matches an outline item (for headings)
    const outlineItem = outline.find(item => item.title === para.trim());
    
    if (outlineItem) {
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: para,
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          spacing: {
            before: 480,
            after: 240,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
          alignment: AlignmentType.CENTER,
        })
      );
    } else {
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: para,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          indent: {
            firstLine: convertInchesToTwip(0.5),
          },
          spacing: {
            after: 0,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        })
      );
    }
  }
  
  // Add references section if there are citations
  if (citations.length > 0) {
    contentParagraphs.push(
      new Paragraph({
        text: "",
        pageBreakBefore: true,
      }),
      new Paragraph({
        children: [
          new TextRun({
            text: "References",
            font: "Times New Roman",
            size: 24,
            bold: true,
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 480,
          line: 480,
          lineRule: LineRuleType.EXACT,
        },
      })
    );
    
    // Sort citations alphabetically
    const sortedCitations = [...citations].sort((a, b) => a.reference.localeCompare(b.reference));
    
    // Add each citation with proper APA format (hanging indent)
    for (const citation of sortedCitations) {
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: citation.reference,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          indent: {
            hanging: convertInchesToTwip(0.5),
            left: convertInchesToTwip(0.5),
          },
          spacing: {
            after: 240,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        })
      );
    }
  }
  
  sections.push({
    properties: {
      page: {
        margin: {
          top: convertInchesToTwip(1),
          right: convertInchesToTwip(1),
          bottom: convertInchesToTwip(1),
          left: convertInchesToTwip(1),
        },
      },
      type: SectionType.NEXT_PAGE,
    },
    headers: {
      default: regularHeader,
    },
    children: contentParagraphs,
  });
  
  // Create the document with APA format
  const doc = new Document({
    features: {
      updateFields: true,
    },
    sections: sections,
    styles: {
      default: {
        document: {
          run: {
            font: "Times New Roman",
            size: 24, // 12pt
          },
          paragraph: {
            spacing: {
              line: 480, // Double spacing
              lineRule: LineRuleType.EXACT,
            },
          },
        },
      },
    },
  });
  
  // Generate the Word document as a blob
  return await Packer.toBlob(doc);
}

/**
 * Create a document with Turabian formatting
 */
async function createTurabianDocument(options: DocumentExportOptions, isDissertation: boolean): Promise<Blob> {
  const { metadata, content, outline, citations } = options;
  
  // Create document sections
  const sections = [];
  
  // Full name with credentials
  const fullName = `${metadata.firstName} ${metadata.middleName ? metadata.middleName + ' ' : ''}${metadata.lastName}`;
  
  // Common footer with page numbers
  const footer = new Footer({
    children: [
      new Paragraph({
        children: [
          new TextRun({
            children: [PageNumber.CURRENT],
            font: "Times New Roman",
            size: 22,
          }),
        ],
        alignment: AlignmentType.CENTER,
      }),
    ],
  });
  
  // Title page
  sections.push({
    properties: {
      page: {
        margin: {
          top: convertInchesToTwip(2),
          right: convertInchesToTwip(1),
          bottom: convertInchesToTwip(1),
          left: convertInchesToTwip(1),
        },
      },
      type: SectionType.NEXT_PAGE,
      titlePage: true,
    },
    children: [
      // For dissertation, we add more specialized title page elements
      ...(isDissertation ? [
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.mainTitle.toUpperCase(),
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
        }),
        
        // Add subtitle if available
        ...(metadata.subtitle ? [
          new Paragraph({
            children: [
              new TextRun({
                text: metadata.subtitle,
                font: "Times New Roman",
                size: 24,
                bold: true,
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: {
              after: 960, // Extra space
            },
          }),
        ] : [
          new Paragraph({
            text: "",
            spacing: { after: 960 },
          }),
        ]),
        
        // Dissertation specific elements
        new Paragraph({
          children: [
            new TextRun({
              text: "A Project presented to the faculty of",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.college || "The School of Humanities",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 480,
          },
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: "In partial fulfillment of the",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: "requirements for the degree",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.degreeTitle || "Doctor of Philosophy",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 960,
          },
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: "by",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: fullName,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: new Date(metadata.submissionDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }),
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
      ] : [
        // Regular Turabian paper title page
        new Paragraph({
          text: '',
          spacing: { before: 2880 }, // Space at top of page
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.mainTitle,
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 960, // Extra space
          },
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: "by",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 960, // Extra space
          },
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: fullName,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 1440, // Extra space
          },
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.course || "Course Name",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: new Date(metadata.submissionDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }),
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
      ]),
    ],
  });
  
  // For dissertation, add copyright page, approval page, etc.
  if (isDissertation) {
    // Copyright page
    sections.push({
      properties: {
        page: {
          margin: {
            top: convertInchesToTwip(2),
            right: convertInchesToTwip(1),
            bottom: convertInchesToTwip(1),
            left: convertInchesToTwip(1),
          },
        },
        type: SectionType.NEXT_PAGE,
      },
      footers: {
        default: footer,
      },
      children: [
        new Paragraph({
          text: "",
          spacing: { before: 5760 }, // Center vertically
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: `Copyright © ${metadata.copyrightYear || new Date().getFullYear()} ${fullName}`,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 480,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: "All rights reserved. University of",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: "America has permission to reproduce",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: "and disseminate this document in any",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: "form by any means for purposes",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: "chosen by the University.",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
      ],
    });
    
    // Approval sheet
    sections.push({
      properties: {
        page: {
          margin: {
            top: convertInchesToTwip(2),
            right: convertInchesToTwip(1),
            bottom: convertInchesToTwip(1),
            left: convertInchesToTwip(1),
          },
        },
        type: SectionType.NEXT_PAGE,
      },
      footers: {
        default: footer,
      },
      children: [
        new Paragraph({
          children: [
            new TextRun({
              text: "APPROVAL SHEET",
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: metadata.mainTitle.toUpperCase(),
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
        }),
        new Paragraph({
          children: [
            new TextRun({
              text: fullName,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 960,
          },
        }),
        
        // Add signers
        ...(metadata.signers.filter(s => s.name).map((signer, index) => (
          new Paragraph({
            children: [
              new TextRun({
                text: `${signer.name}${signer.jobTitle ? ', ' + signer.jobTitle : ''}${signer.role ? ', ' + signer.role : ''}`,
                font: "Times New Roman",
                size: 24,
              }),
            ],
            spacing: {
              after: 480,
            },
          })
        ))),
        
        // Add date line
        new Paragraph({
          children: [
            new TextRun({
              text: "DATE: _______________",
              font: "Times New Roman",
              size: 24,
            }),
          ],
          spacing: {
            before: 960,
          },
        }),
      ],
    });
    
    // Add dedication if provided
    if (metadata.dedication) {
      sections.push({
        properties: {
          page: {
            margin: {
              top: convertInchesToTwip(2),
              right: convertInchesToTwip(1),
              bottom: convertInchesToTwip(1),
              left: convertInchesToTwip(1),
            },
          },
          type: SectionType.NEXT_PAGE,
        },
        footers: {
          default: footer,
        },
        children: [
          new Paragraph({
            text: "",
            spacing: { before: 4320 }, // Space at top
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Dedication",
                font: "Times New Roman",
                size: 24,
                bold: true,
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: {
              after: 480,
            },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: metadata.dedication,
                font: "Times New Roman",
                size: 24,
              }),
            ],
            alignment: AlignmentType.CENTER,
          }),
        ],
      });
    }
    
    // Add abstract if provided
    if (metadata.abstract) {
      sections.push({
        properties: {
          page: {
            margin: {
              top: convertInchesToTwip(2),
              right: convertInchesToTwip(1),
              bottom: convertInchesToTwip(1),
              left: convertInchesToTwip(1),
            },
          },
          type: SectionType.NEXT_PAGE,
        },
        footers: {
          default: footer,
        },
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: "ABSTRACT",
                font: "Times New Roman",
                size: 24,
                bold: true,
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: {
              after: 240,
            },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: metadata.mainTitle.toUpperCase(),
                font: "Times New Roman",
                size: 24,
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: {
              after: 480,
            },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: metadata.abstract,
                font: "Times New Roman",
                size: 24,
              }),
            ],
            spacing: {
              after: 240,
              line: 480,
              lineRule: LineRuleType.EXACT,
            },
          }),
          
          // Student and faculty information
          new Paragraph({
            children: [
              new TextRun({
                text: `${fullName}, Ph.D., Literature`,
                font: "Times New Roman",
                size: 24,
              }),
            ],
            spacing: {
              before: 480,
            },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: `School of Fine Arts and Sciences`,
                font: "Times New Roman",
                size: 24,
              }),
            ],
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: `University of America, ${metadata.copyrightYear || new Date().getFullYear()}`,
                font: "Times New Roman",
                size: 24,
              }),
            ],
          }),
        ],
      });
    }
    
    // Table of contents
    const tocEntries = outline.map(item => item.title);
    
    if (tocEntries.length > 0) {
      sections.push({
        properties: {
          page: {
            margin: {
              top: convertInchesToTwip(2),
              right: convertInchesToTwip(1),
              bottom: convertInchesToTwip(1),
              left: convertInchesToTwip(1),
            },
          },
          type: SectionType.NEXT_PAGE,
        },
        footers: {
          default: footer,
        },
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: "CONTENTS",
                font: "Times New Roman",
                size: 24,
                bold: true,
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: {
              after: 480,
            },
          }),
          
          // Chapter entries with page numbers
          ...tocEntries.map((title, index) => (
            new Paragraph({
              children: [
                new TextRun({
                  text: title,
                  font: "Times New Roman",
                  size: 24,
                }),
                new TextRun({
                  text: `\t${index + 1}`,
                  font: "Times New Roman",
                  size: 24,
                }),
              ],
              tabStops: [
                {
                  type: TabStopType.RIGHT,
                  position: TabStopPosition.MAX,
                },
              ],
              spacing: {
                after: 240,
              },
            })
          )),
        ],
      });
    }
  }
  
  // Main content section
  const contentParagraphs = [];
  
  // Add content with double spacing
  const contentText = stripHtmlTags(content);
  const contentLines = contentText
    .split('\n')
    .filter(para => para.trim() !== "");
  
  // For Turabian, add section headers as chapter titles
  let chapterNum = 1;
  
  for (const para of contentLines) {
    // Check if paragraph matches an outline item (for headings)
    const outlineItem = outline.find(item => item.title === para.trim());
    
    if (outlineItem) {
      // For Turabian, we format section titles as chapters
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: isDissertation ? para.toUpperCase() : para,
              font: "Times New Roman",
              size: 24,
              bold: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            before: 480,
            after: 480,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        })
      );
      chapterNum++;
    } else {
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: para,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          indent: {
            firstLine: convertInchesToTwip(0.5),
          },
          spacing: {
            after: 0,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        })
      );
    }
  }
  
  // Add bibliography if there are citations
  if (citations.length > 0) {
    contentParagraphs.push(
      new Paragraph({
        text: "",
        pageBreakBefore: true,
      }),
      new Paragraph({
        children: [
          new TextRun({
            text: "Bibliography",
            font: "Times New Roman",
            size: 24,
            bold: true,
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 480,
          line: 480,
          lineRule: LineRuleType.EXACT,
        },
      })
    );
    
    // Sort citations alphabetically
    const sortedCitations = [...citations].sort((a, b) => a.reference.localeCompare(b.reference));
    
    // Add each citation with proper Turabian format (hanging indent)
    for (const citation of sortedCitations) {
      contentParagraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: citation.reference,
              font: "Times New Roman",
              size: 24,
            }),
          ],
          indent: {
            hanging: convertInchesToTwip(0.5),
            left: convertInchesToTwip(0.5),
          },
          spacing: {
            after: 240,
            line: 480,
            lineRule: LineRuleType.EXACT,
          },
        })
      );
    }
  }
  
  sections.push({
    properties: {
      page: {
        margin: {
          top: convertInchesToTwip(1),
          right: convertInchesToTwip(1),
          bottom: convertInchesToTwip(1),
          left: convertInchesToTwip(1),
        },
      },
      type: SectionType.NEXT_PAGE,
    },
    footers: {
      default: footer,
    },
    children: contentParagraphs,
  });
  
  // Create the document with Turabian format
  const doc = new Document({
    features: {
      updateFields: true,
    },
    sections: sections,
    styles: {
      default: {
        document: {
          run: {
            font: "Times New Roman",
            size: 24, // 12pt
          },
          paragraph: {
            spacing: {
              line: 480, // Double spacing
              lineRule: LineRuleType.EXACT,
            },
          },
        },
      },
    },
  });
  
  // Generate the Word document as a blob
  return await Packer.toBlob(doc);
}

/**
 * Create a basic document with minimal formatting
 */
async function createDefaultDocument(options: DocumentExportOptions): Promise<Blob> {
  const { metadata, content, outline, citations } = options;
  
  // Create a simplified document with basic paragraphs
  const paragraphs: Paragraph[] = [];
  
  // Add title
  paragraphs.push(
    new Paragraph({
      children: [
        new TextRun({
          text: metadata.mainTitle,
          bold: true,
          size: 28, // 14pt
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 240, // 12pt space after
      },
    })
  );
  
  if (metadata.subtitle) {
    paragraphs.push(
      new Paragraph({
        children: [
          new TextRun({
            text: metadata.subtitle,
            size: 24, // 12pt
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 240, // 12pt space after
        },
      })
    );
  }
  
  // Add author information
  paragraphs.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `${metadata.firstName} ${metadata.middleName ? metadata.middleName + ' ' : ''}${metadata.lastName}`,
          size: 24, // 12pt
        }),
      ],
      alignment: AlignmentType.CENTER,
    })
  );
  
  if (metadata.institution) {
    paragraphs.push(
      new Paragraph({
        children: [
          new TextRun({
            text: metadata.institution,
            size: 24, // 12pt
          }),
        ],
        alignment: AlignmentType.CENTER,
      })
    );
  }
  
  if (metadata.course) {
    paragraphs.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `Course: ${metadata.course}`,
            size: 24, // 12pt
          }),
        ],
        alignment: AlignmentType.CENTER,
      })
    );
  }
  
  if (metadata.instructor) {
    paragraphs.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `Instructor: ${metadata.instructor}`,
            size: 24, // 12pt
          }),
        ],
        alignment: AlignmentType.CENTER,
      })
    );
  }
  
  if (metadata.submissionDate) {
    const formattedDate = new Date(metadata.submissionDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    paragraphs.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `Date: ${formattedDate}`,
            size: 24, // 12pt
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 480, // 24pt space after
        },
      })
    );
  }
  
  // Add content paragraphs
  const contentText = stripHtmlTags(content);
  const contentParagraphs = contentText
    .split('\n')
    .filter(para => para.trim() !== "");
  
  for (const para of contentParagraphs) {
    // Check if paragraph matches an outline item (for headings)
    const outlineItem = outline.find(item => item.title === para.trim());
    
    if (outlineItem) {
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: para,
              bold: true,
              size: 24, // 12pt
            }),
          ],
          spacing: {
            before: 240, // 12pt space before
            after: 120, // 6pt space after
          },
        })
      );
    } else {
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: para,
              size: 24, // 12pt
            }),
          ],
          spacing: {
            after: 240, // 12pt space after
          },
        })
      );
    }
  }
  
  // Add bibliography if we have citations
  if (citations.length > 0) {
    // Page break before bibliography
    paragraphs.push(
      new Paragraph({
        text: "",
        pageBreakBefore: true,
      })
    );
    
    // Bibliography header
    paragraphs.push(
      new Paragraph({
        children: [
          new TextRun({
            text: metadata.bibliographyHeading || "Bibliography",
            bold: true,
            size: 24, // 12pt
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 240, // 12pt space after
        },
      })
    );
    
    // Sort citations
    const sortedCitations = [...citations].sort((a, b) => {
      const aNum = parseInt(a.marker);
      const bNum = parseInt(b.marker);
      
      if (isNaN(aNum) || isNaN(bNum)) {
        return a.marker.localeCompare(b.marker);
      }
      
      return aNum - bNum;
    });
    
    // Add each citation
    for (const citation of sortedCitations) {
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: citation.reference,
              size: 24, // 12pt
            }),
          ],
          indent: {
            left: 720, // 0.5 inch
            hanging: 360, // 0.25 inch
          },
          spacing: {
            after: 240, // 12pt space after
          },
        })
      );
    }
  }
  
  // Create the document
  const doc = new Document({
    styles: {
      default: {
        document: {
          run: {
            font: "Times New Roman",
            size: 24, // 12pt
          },
        },
      },
    },
    sections: [
      {
        properties: {
          page: {
            margin: {
              top: convertInchesToTwip(1),
              right: convertInchesToTwip(1),
              bottom: convertInchesToTwip(1),
              left: convertInchesToTwip(1),
            },
          },
        },
        children: paragraphs,
      },
    ],
  });
  
  // Generate the Word document as a blob
  return await Packer.toBlob(doc);
}

/**
 * Download a blob as a file
 */
export function downloadBlob(blob: Blob, fileName: string): void {
  // Create a URL for the blob
  const url = URL.createObjectURL(blob);
  
  // Create a link element
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  
  // Append to the document, click it, and remove it
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  URL.revokeObjectURL(url);
}
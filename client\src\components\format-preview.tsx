import React, { useState } from 'react';

interface FormatPreviewProps {
  format: string;
  selected: boolean;
  onClick: () => void;
  title: string;
  subtitle: string;
  pages: React.ReactNode[];
}

export function FormatPreview({ format, selected, onClick, title, subtitle, pages }: FormatPreviewProps) {
  const [currentPage, setCurrentPage] = useState(0);
  
  const nextPage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentPage < pages.length - 1) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  const prevPage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  return (
    <div 
      className={`p-4 border rounded-md cursor-pointer hover:border-primary ${selected ? 'border-primary ring-2 ring-ring' : 'border-border'}`}
      onClick={onClick}
    >
      <div className="font-medium">{title}</div>
      <div className="text-xs text-muted-foreground">{subtitle}</div>
      
      <div className="mt-2 relative">
        {/* Main preview */}
        <div className="aspect-[0.77] bg-foreground border border-border shadow-md">
          <div className="p-4 h-full flex flex-col bg-foreground text-background">
            {pages[currentPage]}
          </div>
        </div>
        
        {/* Horizontal scrollbar for page thumbnails */}
        <div className="mt-3 overflow-x-auto pb-2">
          <div className="flex space-x-2" style={{ minWidth: 'min-content' }}>
            {pages.map((page, index) => {
              // Generate simplified thumbnail based on page index
              let thumbnailContent;
              
              // First page is usually title/cover
              if (index === 0) {
                thumbnailContent = (
                  <div className="w-full h-full flex flex-col items-center justify-center">
                    <div className="w-3/4 h-1 bg-background mb-1"></div>
                    <div className="w-1/2 h-1 bg-background mb-2"></div>
                    <div className="w-2/3 h-4 flex flex-col justify-center">
                      <div className="w-full h-[2px] bg-background mb-[2px]"></div>
                      <div className="w-1/2 h-[2px] bg-background mx-auto mb-[2px]"></div>
                      <div className="w-3/4 h-[2px] bg-background mx-auto"></div>
                    </div>
                  </div>
                );
              }
              // Content pages
              else if (index === pages.length - 1) {
                // Last page is usually bibliography/references
                thumbnailContent = (
                  <div className="w-full h-full flex flex-col items-center pt-1">
                    <div className="w-2/3 h-[2px] bg-background mb-1"></div>
                    <div className="w-full px-1">
                      <div className="w-full h-[2px] bg-background mb-[3px]"></div>
                      <div className="w-full h-[2px] bg-background mb-[3px]"></div>
                      <div className="w-4/5 h-[2px] bg-background mb-[3px]"></div>
                      <div className="w-full h-[2px] bg-background mb-[3px]"></div>
                      <div className="w-3/4 h-[2px] bg-background"></div>
                    </div>
                  </div>
                );
              }
              else {
                // Regular content page
                thumbnailContent = (
                  <div className="w-full h-full flex flex-col items-center pt-1">
                    <div className="w-2/3 h-[2px] bg-background mb-1"></div>
                    <div className="w-full px-1">
                      <div className="w-full h-[2px] bg-background mb-[3px]"></div>
                      <div className="w-full h-[2px] bg-background mb-[3px]"></div>
                      <div className="w-full h-[2px] bg-background mb-[3px]"></div>
                      <div className="w-1/2 h-[2px] bg-background mb-2"></div>
                      <div className="w-[6px] h-[6px] rounded-sm bg-background opacity-50 mb-[3px] ml-2"></div>
                      <div className="w-full h-[2px] bg-background mb-[3px]"></div>
                    </div>
                  </div>
                );
              }

              return (
                <button
                  key={index}
                  className={`flex-shrink-0 w-10 h-14 border ${index === currentPage ? 'border-primary ring-1 ring-ring' : 'border-border'}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentPage(index);
                  }}
                  aria-label={`Page ${index + 1}`}
                  title={`Page ${index + 1}`}
                >
                  <div className="w-full h-full bg-foreground text-background flex items-center justify-center overflow-hidden">
                    {thumbnailContent}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
        
        {/* Page indicator text */}
        <div className="flex justify-center mt-1">
          <div className="text-xs text-muted-foreground">
            Page {currentPage + 1} of {pages.length}
          </div>
        </div>
      </div>
    </div>
  );
}
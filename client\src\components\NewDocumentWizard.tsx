import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CoverPreview, CopyrightPreview } from './wizard/FormatPreviewSnippets'; // Import previews

// Define Step interface
interface WizardStep {
  id: string;
  title: string;
  component: React.FC<{ formData: Partial<NewDocumentFormData>, updateFormData: (data: Partial<NewDocumentFormData>) => void }>;
}

// Define Wizard props
interface NewDocumentWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (formData: NewDocumentFormData) => void;
}

// Define Document Formats
export type DocumentFormat = 'apa-pro' | 'apa-student' | 'mla-cover' | 'mla' | 'turabian' | 'turabian-dissertation';

export const formatLabels: Record<DocumentFormat, string> = {
  'apa-pro': 'APA Professional',
  'apa-student': 'APA Student',
  'mla-cover': 'MLA + Cover Page',
  'mla': 'MLA',
  'turabian': 'Turabian',
  'turabian-dissertation': 'Turabian Dissertation'
};

export interface NewDocumentFormData { // Export for dashboard.tsx usage
  title?: string;
  format?: DocumentFormat;
}

// Updated SelectFormatStep component
interface SelectFormatStepProps {
  formData: NewDocumentFormData;
  updateFormData: (data: Partial<NewDocumentFormData>) => void;
}

const SelectFormatStep: React.FC<SelectFormatStepProps> = ({ formData, updateFormData }) => {
  const currentFormat = formData.format;
  const onFormatSelect = (format: DocumentFormat) => {
    updateFormData({ format });
  };

  return (
    <div className="text-center">
      <h3 className="text-lg font-medium text-foreground mb-1">Select Document Format</h3>
      <p className="text-sm text-muted-foreground max-w-lg mx-auto mt-1 mb-6">
        Choose the primary format for your new document. This influences the structure and metadata fields.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-3xl mx-auto"> {/* gap-4 to gap-3 */}
        {(Object.keys(formatLabels) as DocumentFormat[]).map(format => (
          <div
            key={format}
            onClick={() => onFormatSelect(format)}
            className={`p-3 border rounded-lg cursor-pointer hover:shadow-lg transition-all duration-150 ease-in-out
                        ${currentFormat === format ? 'border-primary ring-2 ring-primary shadow-xl' : 'border-border hover:border-gray-300 dark:hover:border-gray-600'}`}
            /* p-4 to p-3; mb-3 to mb-2 for h4; gap-2 to gap-1, p-2 to p-1 for preview div; scale-[0.6] to scale-[0.5] for preview items */
          >
            <h4 className="text-md font-semibold mb-2 text-foreground text-center">{formatLabels[format]}</h4>
            
            <div className="grid grid-cols-2 gap-1 p-1 bg-muted/50 dark:bg-muted/20 rounded-md border border-dashed border-gray-300 dark:border-gray-700">
              <div className="transform scale-[0.5] origin-top">
                <CoverPreview title={formData.title} authorName="[Author]" />
              </div>
              <div className="transform scale-[0.5] origin-top">
                <CopyrightPreview year="[Year]" authorName="[Author]" institution="[Institution]" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// New TitleStep component
interface TitleStepProps {
  currentTitle?: string;
  onTitleChange: (title: string) => void;
}

const TitleStep: React.FC<TitleStepProps> = ({ currentTitle, onTitleChange }) => {
  return (
    <div>
      <Label htmlFor="documentTitle" className="text-left block mb-1">Document Title</Label>
      <Input
        id="documentTitle"
        type="text"
        placeholder="Enter document title..."
        value={currentTitle || ''}
        onChange={(e) => onTitleChange(e.target.value)}
        className="mt-1 w-full"
        autoFocus
      />
    </div>
  );
};


export const NewDocumentWizard: React.FC<NewDocumentWizardProps> = ({ isOpen, onClose, onComplete }) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  // Initialize with empty title and default format
  const [formData, setFormData] = useState<NewDocumentFormData>({ title: '', format: 'apa-student' });

  const updateFormData = (data: Partial<NewDocumentFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  // Create stable component references to prevent recreation
  const TitleStepComponent = React.useCallback(({ formData: currentFormData, updateFormData: currentUpdateFormData }: { formData: Partial<NewDocumentFormData>, updateFormData: (data: Partial<NewDocumentFormData>) => void }) => (
    <TitleStep
      currentTitle={currentFormData.title}
      onTitleChange={(title) => currentUpdateFormData({ title })}
    />
  ), []);

  const FormatStepComponent = React.useCallback(({ formData: currentFormData, updateFormData: currentUpdateFormData }: { formData: Partial<NewDocumentFormData>, updateFormData: (data: Partial<NewDocumentFormData>) => void }) => (
    <SelectFormatStep
      formData={currentFormData}
      updateFormData={currentUpdateFormData}
    />
  ), []);

  // Memoize the steps to prevent recreation on every render
  const steps: WizardStep[] = React.useMemo(() => [
    {
      id: 'title',
      title: 'Set Document Title',
      component: TitleStepComponent,
    },
    {
      id: 'format',
      title: 'Select Document Format',
      component: FormatStepComponent,
    },
  ], [TitleStepComponent, FormatStepComponent]);

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      onComplete(formData);
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  if (!isOpen) {
    // Although Dialog manages its own open state, we respect isOpen for external control
    // For now, if not isOpen, we render nothing, consistent with the conceptual example.
    // Later, Dialog's `open` prop will be the primary driver.
    return null;
  }

  const CurrentStepComponent = steps[currentStepIndex].component;

  return (
    // Using Dialog components for layout as requested.
    // The `open` prop of Dialog will be controlled by `isOpen`.
    // `onOpenChange` will call `onClose` when the dialog is closed by internal means (e.g., Escape key, overlay click).
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) onClose(); }}>
      {/* Adjusted max-width for SelectFormatStep */}
      <DialogContent className={steps[currentStepIndex].id === 'format' ? "sm:max-w-2xl" : "sm:max-w-[600px]"}> {/* 750px to sm:max-w-2xl (672px) */}
        <DialogHeader>
          <DialogTitle>{steps[currentStepIndex].title}</DialogTitle>
        </DialogHeader>
        <div className="py-4 max-h-[60vh] overflow-y-auto"> {/* Added max-h and overflow */}
          <CurrentStepComponent formData={formData} updateFormData={updateFormData} />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <div className="flex gap-2">
            {currentStepIndex > 0 && (
              <Button variant="outline" onClick={handlePrevious}>Previous</Button>
            )}
            <Button onClick={handleNext}>
              {currentStepIndex === steps.length - 1 ? 'Finish' : 'Next'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

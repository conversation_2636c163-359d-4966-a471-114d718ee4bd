import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { DocumentExportMetadata, Signer } from '@/lib/types';

export type DocumentFormat = 'apa-pro' | 'apa-student' | 'mla-cover' | 'mla' | 'turabian' | 'turabian-dissertation';

export const ALL_FORMATS: { value: DocumentFormat; label: string }[] = [
  { value: 'apa-pro', label: 'APA Professional' },
  { value: 'apa-student', label: 'APA Student' },
  { value: 'mla-cover', label: 'MLA with Cover Page' },
  { value: 'mla', label: 'MLA Standard' },
  { value: 'turabian', label: 'Turabian' },
  { value: 'turabian-dissertation', label: 'Turabian Dissertation' },
];

interface MetadataEditorProps {
  documentFormat: DocumentFormat | null;
  metadata: DocumentExportMetadata | null;
  onFormatChange: (newFormat: DocumentFormat) => void;
  onMetadataChange: (updatedMetadata: Partial<DocumentExportMetadata>) => void;
}

export const MetadataEditor: React.FC<MetadataEditorProps> = ({
  documentFormat,
  metadata,
  onFormatChange,
  onMetadataChange,
}) => {
  if (!metadata || !documentFormat) {
    return (
         <div className="p-4 text-center text-muted-foreground">
             <p>Loading metadata editor...</p>
             <p className="text-xs">(Ensure a document is loaded and format is set)</p>
         </div>
     );
  }

  const handleInputChange = (field: keyof DocumentExportMetadata, value: string | string[] | number) => {
    onMetadataChange({ [field]: value });
  };

  const handleSignerChange = (index: number, field: keyof Signer, value: string) => {
    const currentSigners = metadata.signers ? [...metadata.signers] : [];
    while (currentSigners.length <= index) {
         currentSigners.push({ name: '', jobTitle: '', role: '' });
    }
    currentSigners[index] = { ...currentSigners[index], [field]: value };
    onMetadataChange({ signers: currentSigners });
  };

  const addSigner = () => {
     const currentSigners = metadata.signers ? [...metadata.signers] : [];
     // Add a reasonable limit to prevent too many signers, e.g., 5
     if (currentSigners.length < 5) {
         currentSigners.push({ name: '', jobTitle: '', role: '' });
         onMetadataChange({ signers: currentSigners });
     }
  };

  const removeSigner = (index: number) => {
     const currentSigners = metadata.signers ? [...metadata.signers] : [];
     if (currentSigners.length > index) {
         currentSigners.splice(index, 1);
         onMetadataChange({ signers: currentSigners });
     }
  };

  const isUndergraduate = documentFormat === 'apa-student' || documentFormat === 'mla' || documentFormat === 'mla-cover' || documentFormat === 'turabian';
  const isDissertation = documentFormat === 'turabian-dissertation';
  const isScholarlyAPA = documentFormat === 'apa-pro';
  const isAPA = documentFormat.startsWith('apa');
  const isMLA = documentFormat.startsWith('mla');
  // const isTurabian = documentFormat.startsWith('turabian'); // Not used directly below, but good for reference

  return (
    <ScrollArea className="flex-grow min-h-0 metadata-editor-scroll-area"> {/* Removed p-4, will be added by parent if needed */}
      <div className="space-y-6 p-4"> {/* Added p-4 here for internal padding */}
        <div>
          <Label htmlFor="documentFormatSelect" className="text-sm font-medium">Document Format</Label>
          <Select
            value={documentFormat}
            onValueChange={(value: DocumentFormat) => onFormatChange(value)}
          >
            <SelectTrigger id="documentFormatSelect" className="mt-1">
              <SelectValue placeholder="Select format" />
            </SelectTrigger>
            <SelectContent>
              {ALL_FORMATS.map((format) => (
                <SelectItem key={format.value} value={format.value}>
                  {format.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-base font-semibold tracking-tight">Document Information</h3>
          <div>
            <Label htmlFor="mainTitle">Title (Required)</Label>
            <Input id="mainTitle" value={metadata.mainTitle || ''} onChange={(e) => handleInputChange('mainTitle', e.target.value)} required className="mt-1"/>
          </div>
          <div>
            <Label htmlFor="subtitle">Subtitle</Label>
            <Input id="subtitle" value={metadata.subtitle || ''} onChange={(e) => handleInputChange('subtitle', e.target.value)} className="mt-1"/>
          </div>
          {isAPA && (
             <div>
                 <Label htmlFor="shortTitle">Short Title (for Running Head)</Label>
                 <Input id="shortTitle" value={metadata.shortTitle || ''} onChange={(e) => handleInputChange('shortTitle', e.target.value)} placeholder={metadata.mainTitle ? metadata.mainTitle.substring(0,50) : "Max 50 chars"} maxLength={50} className="mt-1"/>
             </div>
          )}
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-base font-semibold tracking-tight">Author Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
             <div> <Label htmlFor="firstName">First Name</Label> <Input id="firstName" value={metadata.firstName || ''} onChange={(e) => handleInputChange('firstName', e.target.value)} className="mt-1"/> </div>
             <div> <Label htmlFor="middleName">Middle Name/Initial</Label> <Input id="middleName" value={metadata.middleName || ''} onChange={(e) => handleInputChange('middleName', e.target.value)} className="mt-1"/> </div>
             <div> <Label htmlFor="lastName">Last Name</Label> <Input id="lastName" value={metadata.lastName || ''} onChange={(e) => handleInputChange('lastName', e.target.value)} className="mt-1"/> </div>
          </div>
         {(isScholarlyAPA || isAPA) && (
             <div>
                 <Label htmlFor="coauthors">Co-authors</Label>
                 <Textarea id="coauthors" value={metadata.coauthors || ''} onChange={(e) => handleInputChange('coauthors', e.target.value)} placeholder="e.g., Jane Doe, University of Example; John Smith, Example Corp." rows={2} className="mt-1"/>
                 <p className="text-xs text-muted-foreground mt-1">Degrees & affiliations formatted by export style. Separate authors with semicolons or new lines.</p>
             </div>
         )}
        </div>

        <Separator />

        {(isUndergraduate || isDissertation) && ( // Institution is common
         <div>
             <Label htmlFor="institution">Institution</Label>
             <Input id="institution" value={metadata.institution || ''} onChange={(e) => handleInputChange('institution', e.target.value)} className="mt-1"/>
         </div>
        )}

        {isUndergraduate && (
          <div className="space-y-4">
            <h3 className="text-base font-semibold tracking-tight">Course Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div> <Label htmlFor="courseName">Course Name</Label> <Input id="courseName" value={metadata.courseName || ''} onChange={(e) => handleInputChange('courseName', e.target.value)} className="mt-1"/> </div>
              <div> <Label htmlFor="course">Course Number</Label> <Input id="course" value={metadata.course || ''} onChange={(e) => handleInputChange('course', e.target.value)} placeholder="e.g., ENG 101" className="mt-1"/> </div>
            </div>
            <div> <Label htmlFor="instructor">Instructor</Label> <Input id="instructor" value={metadata.instructor || ''} onChange={(e) => handleInputChange('instructor', e.target.value)} className="mt-1"/> </div>
            <div>
              <Label htmlFor="submissionDate">Submission Date</Label>
              <Input id="submissionDate" type="date" value={metadata.submissionDate || new Date().toLocaleDateString('en-CA')} onChange={(e) => handleInputChange('submissionDate', e.target.value)} className="mt-1"/>
            </div>
          </div>
        )}

        {isDissertation && (
          <>
            <div className="space-y-4">
              <h3 className="text-base font-semibold tracking-tight">Dissertation Content</h3>
              <div> <Label htmlFor="abstract">Abstract</Label> <Textarea id="abstract" value={metadata.abstract || ''} onChange={(e) => handleInputChange('abstract', e.target.value)} rows={5} placeholder="Enter dissertation abstract..." className="mt-1"/> </div>
              <div> <Label htmlFor="dedication">Dedication</Label> <Textarea id="dedication" value={metadata.dedication || ''} onChange={(e) => handleInputChange('dedication', e.target.value)} rows={3} placeholder="e.g., To my family and friends." className="mt-1"/> </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-base font-semibold tracking-tight">Degree Information</h3>
              <div> <Label htmlFor="degreeTitle">Degree Title</Label> <Input id="degreeTitle" value={metadata.degreeTitle || ''} onChange={(e) => handleInputChange('degreeTitle', e.target.value)} placeholder="e.g., Doctor of Philosophy" className="mt-1"/> </div>
              <div> <Label htmlFor="college">College or Department</Label> <Input id="college" value={metadata.college || ''} onChange={(e) => handleInputChange('college', e.target.value)} className="mt-1"/> </div>
              <div> <Label htmlFor="copyrightYear">Copyright Year</Label> <Input id="copyrightYear" type="number" value={metadata.copyrightYear || new Date().getFullYear().toString()} onChange={(e) => handleInputChange('copyrightYear', parseInt(e.target.value))} className="mt-1"/> </div>
            </div>
            <div className="space-y-4">
             <div className="flex justify-between items-center">
                 <h3 className="text-base font-semibold tracking-tight">Committee Members</h3>
                 <Button onClick={addSigner} variant="outline" size="sm" disabled={(metadata.signers?.length || 0) >= 5}>Add Signer</Button>
             </div>
              {(metadata.signers || []).map((signer, index) => (
                <div key={index} className="p-3 border rounded-md space-y-3 relative bg-muted/30">
                  <h4 className="text-sm font-semibold mb-2">Member {index + 1}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div> <Label htmlFor={`signerName-${index}`}>Name</Label> <Input id={`signerName-${index}`} value={signer.name} onChange={(e) => handleSignerChange(index, 'name', e.target.value)} className="mt-1"/> </div>
                    <div> <Label htmlFor={`signerJobTitle-${index}`}>Title/Affiliation</Label> <Input id={`signerJobTitle-${index}`} value={signer.jobTitle} onChange={(e) => handleSignerChange(index, 'jobTitle', e.target.value)} placeholder="e.g., Professor of History" className="mt-1"/> </div>
                  </div>
                  <div> <Label htmlFor={`signerRole-${index}`}>Role</Label> <Input id={`signerRole-${index}`} value={signer.role} onChange={(e) => handleSignerChange(index, 'role', e.target.value)} placeholder="e.g., Committee Chair" className="mt-1"/> </div>
                  <Button onClick={() => removeSigner(index)} variant="ghost" size="sm" className="absolute top-1 right-1 text-destructive hover:bg-destructive/10 px-2 py-1 h-auto leading-none">Remove</Button>
                </div>
              ))}
              {(!metadata.signers || metadata.signers.length === 0) && ( <p className="text-sm text-muted-foreground">No committee members added.</p> )}
            </div>
          </>
        )}

         {isAPA && (
             <div className="space-y-4">
                 <h3 className="text-base font-semibold tracking-tight">APA Specifics</h3>
                 <div> <Label htmlFor="authorNote">Author Note</Label> <Textarea id="authorNote" value={metadata.authorNote || ''} onChange={(e) => handleInputChange('authorNote', e.target.value)} rows={3} className="mt-1"/> </div>
                 {!isDissertation && ( /* Abstract is part of dissertation section already */
                     <div> <Label htmlFor="abstract">Abstract</Label> <Textarea id="abstract" value={metadata.abstract || ''} onChange={(e) => handleInputChange('abstract', e.target.value)} rows={5} className="mt-1"/> </div>
                 )}
                 <div> <Label htmlFor="keywords">Keywords</Label> <Input id="keywords" value={metadata.keywords || ''} onChange={(e) => handleInputChange('keywords', e.target.value)} placeholder="keyword1, keyword2" className="mt-1"/> </div>
             </div>
         )}

         {documentFormat === 'turabian' && !isDissertation && ( // Turabian Standard (non-dissertation)
             <div className="space-y-4">
                 <h3 className="text-base font-semibold tracking-tight">Turabian Headings</h3>
                 <div><Label htmlFor="tocHeading">Table of Contents Heading</Label><Input id="tocHeading" value={metadata.tocHeading || 'Table of Contents'} onChange={(e) => handleInputChange('tocHeading', e.target.value)} className="mt-1"/></div>
                 <div><Label htmlFor="introductionHeading">Introduction Heading</Label><Input id="introductionHeading" value={metadata.introductionHeading || 'Introduction'} onChange={(e) => handleInputChange('introductionHeading', e.target.value)} className="mt-1"/></div>
                 <div><Label htmlFor="conclusionHeading">Conclusion Heading</Label><Input id="conclusionHeading" value={metadata.conclusionHeading || 'Conclusion'} onChange={(e) => handleInputChange('conclusionHeading', e.target.value)} className="mt-1"/></div>
                 <div><Label htmlFor="bibliographyHeadingT">Bibliography Heading</Label><Input id="bibliographyHeadingT" value={metadata.bibliographyHeading || 'Bibliography'} onChange={(e) => handleInputChange('bibliographyHeading', e.target.value)} className="mt-1"/></div>
             </div>
         )}

         {isMLA && (
             <div className="space-y-4">
                 <h3 className="text-base font-semibold tracking-tight">MLA Specifics</h3>
                 <div> <Label htmlFor="bibliographyHeadingM">Works Cited Heading</Label> <Input id="bibliographyHeadingM" value={metadata.bibliographyHeading || 'Works Cited'} onChange={(e) => handleInputChange('bibliographyHeading', e.target.value)} className="mt-1"/> </div>
             </div>
         )}

         <Separator/>
         <div className="space-y-4">
             <h3 className="text-base font-semibold tracking-tight">Citation Style</h3>
              <div>
                 <Label htmlFor="footnotes">Footnote/Endnote Style</Label>
                 <Select value={metadata.footnotes || 'page'} onValueChange={(value: 'page' | 'end') => handleInputChange('footnotes', value)} >
                     <SelectTrigger id="footnotes" className="mt-1"> <SelectValue placeholder="Select citation style" /> </SelectTrigger>
                     <SelectContent>
                         <SelectItem value="page">Footnotes (bottom of page)</SelectItem>
                         <SelectItem value="end">Endnotes (end of document)</SelectItem>
                     </SelectContent>
                 </Select>
              </div>
         </div>
        <div className="h-8"></div> {/* Bottom padding */}
      </div>
    </ScrollArea>
  );
};

/*
CSS to ensure scrollbar visibility for .metadata-editor-scroll-area:
This should be placed in a global CSS file or a CSS module imported by this component.

.metadata-editor-scroll-area [data-radix-scroll-area-viewport] {
  scrollbar-width: auto !important;
  -ms-overflow-style: auto !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

.metadata-editor-scroll-area [data-radix-scroll-area-viewport]::-webkit-scrollbar {
  display: block !important;
  width: 8px;
}

.metadata-editor-scroll-area [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 4px;
}

.metadata-editor-scroll-area [data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
  background-color: hsl(var(--background));
  border-radius: 4px;
}
*/

export default MetadataEditor;

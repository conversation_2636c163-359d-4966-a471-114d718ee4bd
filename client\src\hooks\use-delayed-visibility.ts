import { useState, useRef, useEffect } from 'react';

/**
 * A hook to handle delayed visibility for elements, particularly useful
 * for showing/hiding UI elements with proper interaction timing
 * 
 * @param delayMs The delay in milliseconds before hiding the element (default: 300ms)
 * @returns An object containing state and handlers for managing delayed visibility
 */
export function useDelayedVisibility(delayMs = 1500) {
  const [isFocused, setIsFocused] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Computed visibility state
  const isVisible = isFocused || isHovering;
  
  // Handle focus events
  const handleFocus = () => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsFocused(true);
  };
  
  // Handle blur events with delay
  const handleBlur = () => {
    // Clear any existing timeout first
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set a timeout before actually hiding the element
    timeoutRef.current = setTimeout(() => {
      // Only hide if we're not hovering
      if (!isHovering) {
        setIsFocused(false);
      }
    }, delayMs);
  };
  
  // Handle mouse enter/leave for the popup/button
  const handleMouseEnter = () => {
    // Clear any pending timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsHovering(true);
  };
  
  const handleMouseLeave = () => {
    // Only start a timeout to hide it, don't set it to false immediately
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      // Double-check we're still not hovering before hiding
      setIsHovering(false);
    }, delayMs);
  };
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return {
    isVisible,
    isFocused,
    isHovering,
    handleFocus,
    handleBlur,
    handleMouseEnter,
    handleMouseLeave
  };
}
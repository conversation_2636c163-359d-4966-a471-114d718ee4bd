import { and, eq } from "drizzle-orm";
import { db } from "./db";
import { DatabaseStorage, IStorage, storage }s from "./storage";
import { users, documents, documentShares } from "@shared/schema";
import { afterAll, beforeAll, describe, expect, it } from "vitest";

describe("storage", () => {
  let testUser: any;
  let testDocument: any;
  let guestUser: any;
  let storage: IStorage;

  beforeAll(async () => {
    storage = new DatabaseStorage();
    // Create a test user and a test document
    testUser = await db
      .insert(users)
      .values({
        username: "testuser",
        password: "password",
      })
      .returning();
    testDocument = await storage.createDocument(testUser[0].id, "Test Document");
    guestUser = await db
      .insert(users)
      .values({
        username: "guestuser",
        password: "password",
      })
      .returning();
  });

  afterAll(async () => {
    // Clean up the test data
    await db.delete(users).where(eq(users.id, testUser[0].id));
    await db.delete(documents).where(eq(documents.id, testDocument.id));
    await db.delete(users).where(eq(users.id, guestUser[0].id));
  });

  it("should not allow a user without edit permissions to modify a document", async () => {
    // Share the document with the guest user with 'view' permissions
    await db.insert(documentShares).values({
      documentId: testDocument.id,
      userId: guestUser[0].id,
      permissionLevel: "view",
    });

    const changes = [
      {
        elementType: "outlineItemModified",
        elementId: "1",
        payload: {
          propertyName: "title",
          newValue: "New Title",
        },
      },
    ];

    await expect(
      storage.applyDocumentChanges(
        testDocument.id,
        changes as any,
        guestUser[0].id
      )
    ).rejects.toThrow("User does not have permission to edit this document.");

    // Clean up the share
    await db
      .delete(documentShares)
      .where(
        and(
          eq(documentShares.documentId, testDocument.id),
          eq(documentShares.userId, guestUser[0].id)
        )
      );
  });

  it("should allow a user with edit permissions to modify a document", async () => {
    // Share the document with the guest user with 'edit' permissions
    await db.insert(documentShares).values({
      documentId: testDocument.id,
      userId: guestUser[0].id,
      permissionLevel: "edit",
    });

    const changes = [
      {
        elementType: "outlineItemModified",
        elementId: "1",
        payload: {
          propertyName: "title",
          newValue: "New Title",
        },
      },
    ];

    const updatedDocument = await storage.applyDocumentChanges(
      testDocument.id,
      changes as any,
      guestUser[0].id
    );

    expect(updatedDocument).toBeDefined();

    // Clean up the share
    await db
      .delete(documentShares)
      .where(
        and(
          eq(documentShares.documentId, testDocument.id),
          eq(documentShares.userId, guestUser[0].id)
        )
      );
  });
});

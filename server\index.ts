import "dotenv/config";
import express, { type Request, Response, NextFunction } from "express";
import WebSocket, { WebSocketServer } from "ws";
import http from "http";
import cookie from "cookie";
import { registerRoutes } from "./routes";
import { sessionStoreInstance } from "./auth";
import { setupVite, serveStatic, log } from "./vite";
import { storage } from "./storage";
import { DocumentChangeType } from "../shared/schema"; // Added import

import { githubWebhookRouter } from './github-webhook.ts';

const app = express();

// Mount webhook route first to avoid body-parser interference
app.use('/github-webhook', githubWebhookRouter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = http.createServer(app);
  await registerRoutes(app);

  const wss = new WebSocketServer({ noServer: true }); // Changed to noServer
  const appWsPath = "/app-ws";

  // Store clients per document: Map<documentId, Map<WebSocket, { userId: number; username: string }>>
  const documentClients = new Map<string, Map<WebSocket, { userId: number; username: string }>>();

  server.on('upgrade', (request, socket, head) => {
    if (request.url === appWsPath) {
      log(`Attempting WebSocket upgrade for path: ${request.url}`);
      if (!request.headers.cookie) {
        log("WebSocket upgrade rejected: No cookies found.");
        socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n');
        socket.destroy();
        return;
      }

      const cookies = cookie.parse(request.headers.cookie);
      // Assuming session cookie name is 'connect.sid', which is the default for express-session
      // This might need to be configured if a different name is used.
      const sessionCookieName = process.env.SESSION_COOKIE_NAME || 'connect.sid';
      const sessionId = cookies[sessionCookieName];

      if (!sessionId) {
        log("WebSocket upgrade rejected: Session ID not found in cookies.");
        socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n');
        socket.destroy();
        return;
      }

      // Important: express-session may URL-decode and unsign the cookie value before storing it as session ID.
      // The cookie value itself might be prefixed with 's:'.
      // connect-pg-simple might expect the raw cookie value or the "de-signed" session ID.
      // Typically, the store expects the actual session ID, not the signed cookie.
      // The 'sessionId' variable from cookie.parse is the raw value.
      // express-session's cookieParser usually handles un-signing.
      // For direct store access, we might need to manually "unsign" if using signed cookies,
      // or ensure the session ID stored by connect-pg-simple matches what we extract.
      // For now, let's assume sessionId is what the store expects. This might need adjustment.
      // A common pattern for express-session is that the actual ID is what's after 's%3A' and before the first '.' if signed.
      // Let's assume for now that the raw sessionId from the cookie is what the store uses.
      // This is often a source of bugs if not handled correctly matching how express-session names/signs cookies.
      // The `connect.sid` cookie by default IS signed. The actual ID is inside.
      // A simple way to get express-session to parse it for us is to "trick" it.
      // However, with direct store access, we need the ID the store uses.
      // `express-session` uses `req.sessionID` after its middleware runs.
      // `sessionId` from `cookie.parse` is the raw value of the cookie.
      // Let's use a placeholder for the session cookie name, often 'connect.sid'.
      // The session ID is usually the value of this cookie. If it's signed, it will look like "s:actualSessionId.signature".
      // The store expects "actualSessionId".

      let plainSessionId = sessionId;
      if (sessionId.startsWith('s:')) {
        plainSessionId = sessionId.substring(2).split('.')[0];
      }


      sessionStoreInstance.get(plainSessionId, (err, session) => {
        if (err) {
          log(`WebSocket upgrade error: Session store error - ${err.message}`);
          socket.write('HTTP/1.1 500 Internal Server Error\r\n\r\n');
          socket.destroy();
          return;
        }

        if (!session || !session.passport || !session.passport.user) {
          log("WebSocket upgrade rejected: No authenticated session found.");
          socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n');
          socket.destroy();
          return;
        }

        const userId = session.passport.user; // This is the user ID
        const username = session.passport.session?.user?.username || session.user?.username || "UnknownUser"; // Attempt to get username
        // The actual path to username depends on what Passport serializes.
        // If only ID is serialized, we might need to fetch user details here or ensure username is in session.
        // For now, let's assume session.user (from deserializeUser) might have it, or default.
        // A more robust way: ensure deserializeUser populates req.user with full user object including username.
        // The session object structure for passport is typically session.passport.user (which is the ID).
        // To get the full user object, Passport's deserializeUser must have populated it,
        // which it does by setting req.user. So, if the session middleware ran, req.user would be available.
        // Here, we only have the session store data. `session.passport.user` is the ID.
        // We need to ensure `session.user` (or similar) contains the full deserialized user object.
        // Let's assume `session.user` is populated by deserializeUser and stored in the session.

        let fetchedUsername = "UnknownUser";
        if (session.user && session.user.username) {
          fetchedUsername = session.user.username;
        } else if (userId) {
          // As a fallback, if only ID is in session.passport.user, and full user object isn't directly in session data
          // we might need a quick fetch here if username is critical for ws object.
          // However, for presence, client can send its username on register if needed,
          // or server can fetch it once on register.
          // For now, let's prioritize what's readily in the session.
          // The `User` type in Express namespace suggests req.user would have it.
          // The session object from the store might be more minimal.
          // If `req.user` is stored in `session.user` by the session store:
          // const userObject = session.user as Express.User; // Type assertion
          // if (userObject) fetchedUsername = userObject.username;
          // Given Passport setup, session.passport.user is the ID. The full user isn't typically re-stored in session by default.
          // We will rely on client sending username or fetch it if needed upon registration.
          // For now, we'll primarily use userId for auth.
          console.warn(`Username not found directly in session for user ID: ${userId}. Will rely on client or later fetch.`);
        }


        wss.handleUpgrade(request, socket, head, (ws) => {
          (ws as any).userId = userId;
          // (ws as any).username = fetchedUsername; // Store username if available and deemed necessary here
          wss.emit('connection', ws, request);
        });
      });
    } else {
      log(`WebSocket upgrade request for non-application path: ${request.url}. Letting other handlers (e.g. Vite) manage it.`);
    }
  });

  wss.on("connection", (ws: WebSocket & { userId?: number; username?: string }) => {
    let currentDocumentId: string | null = null;

    if (!ws.userId) {
      log("WebSocket connection established but no userId attached. Closing.");
      ws.close(1008, "User not authenticated");
      return;
    }
    // Username might not be set here if not retrieved during upgrade, will be set on register.
    log(`Client connected via WebSocket, authenticated as user: ${ws.userId} (username: ${ws.username || 'N/A'})`);

    ws.on("message", async (message: WebSocket.RawData) => {
      try {
        const parsedMessage = JSON.parse(message.toString());
        log(`Received message type: ${parsedMessage.type}, from user (in message payload): ${parsedMessage.userId}, connection user: ${ws.userId}`);

        if (parsedMessage.type === 'register' && parsedMessage.documentId) {
          currentDocumentId = parsedMessage.documentId;
          const userForPresence = await storage.getUser(ws.userId!); // Fetch user details for username
          const username = userForPresence?.username || `User_${ws.userId}`;

          ws.username = username; // Store username on ws object for easier access on close

          if (!documentClients.has(currentDocumentId!)) {
            documentClients.set(currentDocumentId!, new Map());
          }
          documentClients.get(currentDocumentId!)!.set(ws, { userId: ws.userId!, username });
          log(`Client (user: ${ws.userId}, username: ${username}) registered for document: ${currentDocumentId}`);

          // Compile list of present users for the new client
          const presentUsersList: { id: number; username: string }[] = [];
          documentClients.get(currentDocumentId!)!.forEach(clientInfo => {
            presentUsersList.push({ id: clientInfo.userId, username: clientInfo.username });
          });
          ws.send(JSON.stringify({
            type: 'presenceEvent', // Using a wrapper type for presence for now
            elementType: DocumentChangeType.PRESENCE_LIST, // Using shared enum
            payload: { users: presentUsersList }
          }));

          // Broadcast USER_JOINED to other clients
          const joinedUserPayload = { user: { id: ws.userId!, username } };
          documentClients.get(currentDocumentId!)!.forEach((clientInfo, clientWs) => {
            if (clientWs !== ws && clientWs.readyState === WebSocket.OPEN) {
              // Check authorization before sending presence updates too
              // For now, assuming all in the room are authorized to see presence.
              // This could be refined with getAuthorizedUserIdsForDocument if needed.
              clientWs.send(JSON.stringify({
                type: 'presenceEvent',
                elementType: DocumentChangeType.USER_JOINED,
                payload: joinedUserPayload
              }));
            }
          });

        } else if (parsedMessage.type === 'documentChange' && currentDocumentId) {
          // const messageSenderUserIdString = parsedMessage.userId; // This was the client-generated random ID
          // The check below using authenticatedUserIdRef was problematic server-side.
          // The server should rely on ws.userId as the source of truth for the sender's identity.
          // Client now sends its authenticated ID, but server can override/validate with ws.userId.
          // log(`Received documentChange from user specified in payload: ${parsedMessage.userId}, actual ws.userId: ${ws.userId}`);

          // Check if this 'documentChange' is actually a chat message
          if (parsedMessage.elementType === DocumentChangeType.CHAT_MESSAGE_SENT) {
            if (ws.userId && ws.username) { // Ensure user context is available on ws connection
              const { messageText } = parsedMessage.payload;
              if (typeof messageText === 'string' && messageText.trim() !== '') {
                try {
                  const chatMessage = await storage.addChatMessage(currentDocumentId, ws.userId, ws.username, messageText.trim());

                  const newChatMessageEvent = {
                    type: 'documentChange', // Keep consistent top-level type for client simplicity
                    elementType: DocumentChangeType.NEW_CHAT_MESSAGE,
                    payload: chatMessage,
                    userId: ws.userId, // Include sender's ID
                    documentId: currentDocumentId, // Echo documentId for client-side filtering if needed
                  };

                  const newChatMessageEventString = JSON.stringify(newChatMessageEvent);
                  log(`Broadcasting NEW_CHAT_MESSAGE to document ${currentDocumentId}: ${newChatMessageEventString}`);
                  documentClients.get(currentDocumentId)?.forEach((clientInfo, clientWs) => {
                    // Removed clientWs !== ws to broadcast to sender as well
                    if (clientWs.readyState === WebSocket.OPEN) {
                      clientWs.send(newChatMessageEventString);
                    }
                  });
                  log(`User ${ws.username} (ID: ${ws.userId}) sent chat message in doc ${currentDocumentId}. Finished broadcast attempt.`);
                } catch (chatError) {
                  log(`Error saving/broadcasting chat message from user ${ws.userId} for doc ${currentDocumentId}: ${chatError}`);
                  ws.send(JSON.stringify({ type: 'error', message: 'Failed to send chat message.' }));
                }
              } else {
                ws.send(JSON.stringify({ type: 'error', message: 'Chat message text cannot be empty.' }));
              }
            } else {
              log(`Cannot process CHAT_MESSAGE_SENT: ws.userId or ws.username is missing for doc ${currentDocumentId}.`);
              ws.send(JSON.stringify({ type: 'error', message: 'User context missing for chat.' }));
            }
          } else if (parsedMessage.elementType === DocumentChangeType.INLINE_COMMENT_ADDED) {
            if (ws.userId && ws.username) {
              const { text, selection } = parsedMessage.payload;
              const comment = await storage.addInlineComment(currentDocumentId, ws.userId, ws.username, text, selection);
              const broadcastMessage = {
                type: 'documentChange',
                elementType: DocumentChangeType.INLINE_COMMENT_ADDED,
                payload: comment,
                userId: ws.userId,
                documentId: currentDocumentId,
              };
              documentClients.get(currentDocumentId)?.forEach((clientInfo, clientWs) => {
                if (clientWs.readyState === WebSocket.OPEN) {
                  clientWs.send(JSON.stringify(broadcastMessage));
                }
              });
            }
          } else if (parsedMessage.elementType === DocumentChangeType.INLINE_COMMENT_DELETED) {
            if (ws.userId) {
              const { commentId } = parsedMessage.payload;
              await storage.deleteInlineComment(commentId, ws.userId);
              const broadcastMessage = {
                type: 'documentChange',
                elementType: DocumentChangeType.INLINE_COMMENT_DELETED,
                payload: { commentId },
                userId: ws.userId,
                documentId: currentDocumentId,
              };
              documentClients.get(currentDocumentId)?.forEach((clientInfo, clientWs) => {
                if (clientWs.readyState === WebSocket.OPEN) {
                  clientWs.send(JSON.stringify(broadcastMessage));
                }
              });
            }
          } else if (parsedMessage.elementType === DocumentChangeType.INLINE_COMMENT_UPDATED) {
            if (ws.userId) {
              const { commentId, text } = parsedMessage.payload;
              const comment = await storage.updateInlineComment(commentId, ws.userId, text);
              const broadcastMessage = {
                type: 'documentChange',
                elementType: DocumentChangeType.INLINE_COMMENT_UPDATED,
                payload: comment,
                userId: ws.userId,
                documentId: currentDocumentId,
              };
              documentClients.get(currentDocumentId)?.forEach((clientInfo, clientWs) => {
                if (clientWs.readyState === WebSocket.OPEN) {
                  clientWs.send(JSON.stringify(broadcastMessage));
                }
              });
            }
          } else {
            // This is a regular document content change, expects `parsedMessage.changes`
            if (!parsedMessage.changes) {
              log(`Error: 'documentChange' message received without 'changes' array for doc ${currentDocumentId} and elementType ${parsedMessage.elementType}. Payload: ${JSON.stringify(parsedMessage.payload)}`);
              ws.send(JSON.stringify({ type: 'error', message: "Invalid documentChange format: 'changes' array is missing." }));
              return; // Exit early
            }
            const updatedDocumentContent = await storage.applyDocumentChanges(currentDocumentId, parsedMessage.changes, ws.userId);

            if (updatedDocumentContent) {
              log(`Document ${currentDocumentId} updated successfully by user ${ws.userId}.`);

              const authorizedUserIds = await storage.getAuthorizedUserIdsForDocument(currentDocumentId);
              log(`Authorizing broadcast for document ${currentDocumentId} to user IDs: ${authorizedUserIds.join(', ')}`);

              // Construct the message to broadcast, using the authenticated ws.userId as the sender
              const broadcastMessage = {
                ...parsedMessage, // Keep original changes, timestamp etc.
                userId: ws.userId // Override userId with the authenticated one
              };

              documentClients.get(currentDocumentId!)?.forEach((clientInfo, clientWs) => {
                if (clientWs.userId && clientWs.readyState === WebSocket.OPEN && authorizedUserIds.includes(clientWs.userId)) {
                  log(`Broadcasting documentChange for doc ${currentDocumentId} to user ${clientWs.userId}`);
                  clientWs.send(JSON.stringify(broadcastMessage));
                } else if (clientWs.userId && !authorizedUserIds.includes(clientWs.userId)) {
                  log(`User ${clientWs.userId} is connected to doc ${currentDocumentId} but not authorized for documentChange broadcast.`);
                }
              });
            } // Correctly closing the 'if (updatedDocumentContent)' block
          }
          //  else {
          //   log(`Failed to apply changes to document ${currentDocumentId} from user ${ws.userId}.`);
          //   ws.send(JSON.stringify({ type: 'error', message: 'Failed to apply changes to the document.' }));
          // }
        }
        // Note: The CHAT_MESSAGE_SENT logic was previously an `else if` at the same level as `parsedMessage.type === 'documentChange'`.
        // It needs to be handled *within* the `parsedMessage.type === 'documentChange'` block if the client sends chat messages with that top-level type.
        // The client currently sends: { type: 'documentChange', elementType: CHAT_MESSAGE_SENT, ... }
        // So, the following logic needs to be nested or the top-level type needs to be distinct for chat.
        // Let's adjust the structure to handle different elementTypes *within* a 'documentChange' type message.

        // The original CHAT_MESSAGE_SENT logic is removed from here as it will be integrated into the
        // `parsedMessage.type === 'documentChange'` block.

        // TODO: Add handler for client sending cursor position updates
      } catch (error) {
        log(`Error processing WebSocket message: ${error}`);
        ws.send(JSON.stringify({ type: 'error', message: 'Invalid message format or server error.' }));
      }
    });

    ws.on("close", () => {
      log(`Client disconnected (was on doc: ${currentDocumentId}, user: ${ws.userId}, username: ${ws.username || 'N/A'})`);
      if (currentDocumentId && documentClients.has(currentDocumentId)) {
        const room = documentClients.get(currentDocumentId)!;
        const userInfo = room.get(ws); // Get user info before deleting
        room.delete(ws);

        if (room.size === 0) {
          documentClients.delete(currentDocumentId);
          log(`Document room ${currentDocumentId} is now empty and removed.`);
        } else if (userInfo) { // Only broadcast if user info was found (meaning they were fully registered)
          // Broadcast USER_LEFT to remaining clients in the room
          const leftUserPayload = { userId: userInfo.userId };
          room.forEach((clientInfo, clientWs) => {
            if (clientWs.readyState === WebSocket.OPEN) {
              clientWs.send(JSON.stringify({
                type: 'presenceEvent',
                elementType: DocumentChangeType.USER_LEFT,
                payload: leftUserPayload
              }));
            }
          });
          log(`Broadcasted USER_LEFT for user ${userInfo.userId} in doc ${currentDocumentId}`);
        }
      }
    });

    ws.on("error", (error) => {
      log(`WebSocket error (doc: ${currentDocumentId}, user: ${ws.userId}, username: ${ws.username || 'N/A'}): ${error.message}`);
    });
  });

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
  }, () => {
    log(`serving on port ${port}`);
  });
})();

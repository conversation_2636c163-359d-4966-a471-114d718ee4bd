import React, { useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Editor } from './editor';
import { AiSuggestion } from '@/components/ai/context-ai-popup';
import { useToast } from '@/hooks/use-toast';
import { processAiSuggestion } from '@/services/ai-service';

interface AiEnhancedEditorProps {
  value: string;
  onChange: (value: string) => void;
  minHeight?: string;
  onBlur?: () => void;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
  onShowSubscriptionModal?: () => void;
}

export function AiEnhancedEditor({
  value,
  onChange,
  minHeight = '150px',
  onBlur,
  placeholder,
  className,
  autoFocus,
  onShowSubscriptionModal,
  ...props
}: AiEnhancedEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleAiSuggestion = async (suggestion: AiSuggestion) => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      // Get current selection or use entire content
      const selection = window.getSelection();
      const selectedText = selection?.toString().trim() || value;

      const response = await processAiSuggestion(suggestion, selectedText);

      if (response.success) {
        if (selection && selection.toString().trim()) {
          // Replace selected text
          const newText = value.replace(selection.toString(), response.result || selectedText);
          onChange(newText);
        } else {
          // Apply to entire content
          onChange(response.result || value);
        }

        toast({
          title: suggestion.label,
          description: "AI suggestion applied successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to process AI suggestion",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div ref={editorRef} className="relative">
      <Editor
        value={value}
        onChange={onChange}
        minHeight={minHeight}
        onBlur={onBlur}
        placeholder={placeholder}
        className={className}
        autoFocus={autoFocus}
        {...props}
      />
      
      {/* Compact AI toolbar - icon-only buttons */}
      <div className="absolute top-2 right-2 flex gap-1">
        <button
          onClick={() => handleAiSuggestion({ id: 'improve', icon: 'ri-magic-line', label: 'Improve Writing' })}
          className="w-6 h-6 flex items-center justify-center rounded text-xs bg-gradient-to-r from-[var(--icon-purple)]/10 to-primary/10 text-[var(--icon-purple)] hover:from-[var(--icon-purple)]/20 hover:to-primary/20 border border-[var(--icon-purple)]/20 shadow-sm transition-all duration-300"
          title="Improve Writing"
          disabled={isLoading}
        >
          <i className="ri-magic-line text-xs"></i>
        </button>
        <button
          onClick={() => handleAiSuggestion({ id: 'grammar', icon: 'ri-check-double-line', label: 'Fix Grammar' })}
          className="w-6 h-6 flex items-center justify-center rounded text-xs bg-gradient-to-r from-[var(--icon-purple)]/10 to-primary/10 text-[var(--icon-purple)] hover:from-[var(--icon-purple)]/20 hover:to-primary/20 border border-[var(--icon-purple)]/20 shadow-sm transition-all duration-300"
          title="Fix Grammar"
          disabled={isLoading}
        >
          <i className="ri-check-double-line text-xs"></i>
        </button>
      </div>
    </div>
  );
}
// This module provides basic text checking utilities
// For a real app, we'd use a proper NLP/grammar checking service

// Check spelling against a simple list of common misspellings
export function checkSpelling(text: string): { index: number; word: string; suggestion: string }[] {
  const errors: { index: number; word: string; suggestion: string }[] = [];
  const words = text.split(/\s+/);
  let currentIndex = 0;
  
  // Simple dictionary of common misspellings
  const misspellings: Record<string, string> = {
    'seperate': 'separate',
    'definately': 'definitely',
    'recieve': 'receive',
    'occured': 'occurred',
    'accomodate': 'accommodate',
    'acheive': 'achieve',
    'adress': 'address',
    'arguement': 'argument',
    'beleive': 'believe',
    'concious': 'conscious',
    'foriegn': 'foreign',
    'grammer': 'grammar',
    'gaurd': 'guard',
    'hight': 'height',
    'occurence': 'occurrence',
    'posession': 'possession',
    'reccommend': 'recommend',
    'relevent': 'relevant',
    'sucess': 'success',
    'tommorow': 'tomorrow',
    'goverment': 'government',
    'priviledge': 'privilege',
    'embarass': 'embarrass',
    'liason': 'liaison',
    'independant': 'independent',
    'appearence': 'appearance',
    'refered': 'referred',
    'exagerate': 'exaggerate',
    'truely': 'truly',
    'accidently': 'accidentally'
  };
  
  for (const word of words) {
    // Clean the word to check (remove punctuation)
    const cleanWord = word.toLowerCase().replace(/[^a-z]/g, '');
    
    if (misspellings[cleanWord]) {
      errors.push({
        index: text.indexOf(word, currentIndex),
        word: cleanWord,
        suggestion: misspellings[cleanWord]
      });
    }
    
    currentIndex += word.length + 1; // +1 for the space
  }
  
  return errors;
}

// Simple grammar checks
export function checkGrammar(text: string): { index: number; phrase: string; issue: string; suggestion: string }[] {
  const errors: { index: number; phrase: string; issue: string; suggestion: string }[] = [];
  
  // Check for double spaces
  const doubleSpaceMatches = [...text.matchAll(/\s{2,}/g)];
  for (const match of doubleSpaceMatches) {
    if (match.index !== undefined) {
      errors.push({
        index: match.index,
        phrase: match[0],
        issue: 'Double space',
        suggestion: ' '
      });
    }
  }
  
  // Check for common grammatical mistakes
  const patterns = [
    { 
      pattern: /\b(their|there|they're)\b/gi, 
      issue: 'Commonly confused words',
      check: (match: string, context: string) => {
        const lowerMatch = match.toLowerCase();
        if (lowerMatch === 'their' && context.includes('their is')) {
          return { suggestion: 'there' };
        }
        if (lowerMatch === 'there' && context.includes('there book')) {
          return { suggestion: 'their' };
        }
        if (lowerMatch === 'they\'re' && context.includes('they\'re book')) {
          return { suggestion: 'their' };
        }
        return null;
      }
    },
    {
      pattern: /\b(its|it's)\b/gi,
      issue: 'Possessive vs. contraction',
      check: (match: string, context: string) => {
        const lowerMatch = match.toLowerCase();
        if (lowerMatch === 'its' && context.includes('its a')) {
          return { suggestion: 'it\'s' };
        }
        if (lowerMatch === 'it\'s' && context.includes('it\'s color')) {
          return { suggestion: 'its' };
        }
        return null;
      }
    },
    {
      pattern: /\b(your|you're)\b/gi,
      issue: 'Possessive vs. contraction',
      check: (match: string, context: string) => {
        const lowerMatch = match.toLowerCase();
        if (lowerMatch === 'your' && context.includes('your going')) {
          return { suggestion: 'you\'re' };
        }
        if (lowerMatch === 'you\'re' && context.includes('you\'re book')) {
          return { suggestion: 'your' };
        }
        return null;
      }
    }
  ];
  
  for (const { pattern, issue, check } of patterns) {
    const matches = [...text.matchAll(pattern)];
    for (const match of matches) {
      if (match.index !== undefined) {
        const startIndex = Math.max(0, match.index - 10);
        const endIndex = Math.min(text.length, match.index + match[0].length + 10);
        const context = text.substring(startIndex, endIndex).toLowerCase();
        
        const checkResult = check(match[0], context);
        if (checkResult) {
          errors.push({
            index: match.index,
            phrase: match[0],
            issue,
            suggestion: checkResult.suggestion
          });
        }
      }
    }
  }
  
  return errors;
}

// Check for potential plagiarism
export function checkPlagiarism(text: string): { score: number; matches: { text: string; source: string }[] } {
  // This is a simplified check that would be replaced by a real service
  // In a real app, we'd use a third-party plagiarism detection API
  
  return {
    score: 0, // 0-100 score, higher means more likely plagiarized
    matches: [] // Matches found in external sources
  };
}

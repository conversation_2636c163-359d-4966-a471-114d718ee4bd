import React, { useEffect } from 'react';
import { useParams } from 'wouter';
import { Button } from '@/components/ui/button';
import { SimpleOutlinePanel } from '@/components/outline/panel/SimpleOutlinePanel';
import { WritingPanel } from '@/components/writing';
import { useDocument } from '@/hooks/use-document';
import { Loader2 } from 'lucide-react';
import { nanoid } from 'nanoid';
import { OutlineItem } from '@/lib/types';
import { calculateOutlineNumbers } from '@/lib/utils/outline';

export function PopoutPanelPage() {
  const { documentId, panel } = useParams<{ documentId: string, panel: string }>();
  const {
    outline,
    notes,
    activeOutlineItemId,
    content,
    citations,
    setOutline,
    createNote,
    updateNote,
    deleteNote,
    uploadFile,
    setActiveOutlineItemId,
    updateWritingContent,
    addCitation,
    updateCitation,
    deleteCitation,
    linkNoteToOutlineItem,
    deleteOutlineItemWithNotes,
    duplicateNote,
    refreshDocumentContent,
    isLoading,
  } = useDocument({ initialDocumentId: documentId });

  // Add manual refresh for debugging
  const handleManualRefresh = () => {
    if (refreshDocumentContent) {
      console.log('Manual refresh triggered in popout');
      refreshDocumentContent();
    }
  };

  // Define missing functions that the components expect
  const handleAddOutlineItem = (parentId?: string) => {
    // Implementation similar to main document page
    const newId = nanoid();
    let newOutline: OutlineItem[];
    if (parentId) {
      const addRecursive = (items: OutlineItem[]): OutlineItem[] => {
        return items.map(item => {
          if (item.id === parentId) {
            return {
              ...item,
              children: [
                ...(item.children || []),
                { id: newId, number: '', title: 'New Section', children: [] }
              ]
            };
          }
          if (item.children) {
            return { ...item, children: addRecursive(item.children) };
          }
          return item;
        });
      };
      newOutline = addRecursive(outline);
    } else {
      newOutline = [
        ...outline,
        { id: newId, number: '', title: 'New Section', children: [] }
      ];
    }
    setOutline(calculateOutlineNumbers(newOutline));
  };

  const handleOutlineChange = (updatedOutline: OutlineItem[]) => {
    setOutline(calculateOutlineNumbers(updatedOutline));
  };

  const handleImportOutline = () => {
    // For now, just a placeholder
    console.log('Import outline not implemented in popout');
  };

  const handlePopIn = () => {
    window.close();
  };

  const renderPanel = () => {
    switch (panel) {
      case 'outline':
        return (
          <SimpleOutlinePanel
            outline={outline}
            onOutlineChange={handleOutlineChange}
            onAddItem={handleAddOutlineItem}
            onImportOutline={handleImportOutline}
            notes={notes}
            onNoteCreate={createNote}
            onNoteUpdate={updateNote}
            onNoteDelete={deleteNote}
            onFileUpload={uploadFile}
            onLinkNoteToOutlineItem={linkNoteToOutlineItem}
            onDeleteOutlineItemWithNotes={deleteOutlineItemWithNotes}
            onNoteDuplicate={duplicateNote}
            activeOutlineItemId={activeOutlineItemId}
            popOutPanel={() => {}}
            isPoppedOut={true}
          />
        );
      case 'writing':
        return (
          <WritingPanel
            outline={outline}
            activeOutlineItemId={activeOutlineItemId}
            onOutlineItemSelect={setActiveOutlineItemId}
            content={content}
            onContentChange={updateWritingContent}
            notes={notes.filter(note =>
              note.linkedOutlineIds?.includes(activeOutlineItemId || '') || !note.linkedOutlineIds?.length
            )}
            citations={citations}
            notesType="footnotes"
            onNotesTypeChange={() => {}}
            onAddCitation={addCitation}
            onUpdateCitation={updateCitation}
            onDeleteCitation={deleteCitation}
            onSpellCheck={() => {}}
            onGrammarCheck={() => {}}
            onNoteCreate={createNote}
            onNoteUpdate={updateNote}
            onNoteDelete={deleteNote}
            onFileUpload={uploadFile}
            popOutPanel={() => {}}
            isPoppedOut={true}
          />
        );
      default:
        return <div>Unknown panel</div>;
    }
  };

  if (isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-16 w-16 animate-spin text-primary" />
          <p className="text-lg font-medium text-foreground">Loading panel...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-full flex flex-col">
      {/* Debug toolbar */}
      <div className="p-2 bg-gray-100 border-b flex gap-2">
        <Button size="sm" variant="outline" onClick={handleManualRefresh}>
          🔄 Refresh Data
        </Button>
        <span className="text-xs text-gray-600 self-center">
          Notes: {notes.length} | Outline: {outline.length}
        </span>
      </div>
      <div className="flex-1 overflow-auto">
        {renderPanel()}
      </div>
    </div>
  );
}

import React from 'react';
import { User } from '@/lib/types';

interface UserPresenceProps {
  connectedUsers: number[];
  userProfiles?: Record<number, User>;
  fallbackMode?: boolean;
}

// Generate a consistent color for each user ID
function getUserColor(userId: number): string {
  const colors = [
    'hsl(var(--icon-blue-hsl))',   // blue
    'hsl(var(--destructive))',     // red
    'hsl(var(--icon-green-hsl))',  // green
    'hsl(var(--icon-amber-hsl))',  // amber
    'hsl(var(--icon-purple-hsl))', // purple
    'hsl(var(--destructive))',     // pink (using destructive as a stand-in)
    'hsl(var(--icon-blue-hsl))',   // cyan (using icon-blue as a stand-in)
    'hsl(var(--icon-amber-hsl))',  // orange (using icon-amber as a stand-in)
  ];
  
  return colors[userId % colors.length];
}

export function UserPresence({ connectedUsers, userProfiles, fallbackMode = false }: UserPresenceProps) {
  // Don't show any collaborators in fallback mode
  if (fallbackMode || connectedUsers.length === 0) {
    return null;
  }

  // Get unique user IDs to prevent duplicates
  const uniqueUserIds = Array.from(new Set(connectedUsers));
  
  // Limit to showing max 5 users to prevent UI overflow
  const displayUsers = uniqueUserIds.slice(0, 5);
  const additionalUsers = uniqueUserIds.length - 5;

  return (
    <div className="flex flex-col space-y-2 p-2">
      <h3 className="text-sm font-medium">Active Collaborators</h3>
      <div className="flex flex-wrap gap-1">
        {displayUsers.map(userId => {
          const userColor = getUserColor(userId);
          const userProfile = userProfiles?.[userId];
          const displayName = userProfile?.username || `User ${userId}`;
          
          return (
            <div 
              key={userId}
              className="flex items-center space-x-1 px-2 py-1 rounded-full text-primary-foreground text-xs"
              style={{ backgroundColor: userColor }}
              title={displayName}
            >
              <span className="w-2 h-2 rounded-full bg-primary-foreground opacity-75"></span>
              <span>{displayName}</span>
            </div>
          );
        })}
        
        {/* Show how many additional users if over the limit */}
        {additionalUsers > 0 && (
          <div 
            className="flex items-center space-x-1 px-2 py-1 rounded-full bg-muted text-muted-foreground text-xs"
            title={`${additionalUsers} more collaborator${additionalUsers !== 1 ? 's' : ''}`}
          >
            <span>+{additionalUsers}</span>
          </div>
        )}
      </div>
    </div>
  );
}
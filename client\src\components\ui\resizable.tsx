import * as ResizablePrimitive from "react-resizable-panels";
import { cn } from "@/lib/utils";

const ResizablePanelGroup = ({
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) => (
  <ResizablePrimitive.PanelGroup
    className={cn(
      "flex h-full w-full data-[panel-group-direction=vertical]:flex-col",
      className
    )}
    {...props}
  />
);

const ResizablePanel = ({
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.Panel>) => (
  <ResizablePrimitive.Panel
    className={cn("flex flex-col", className)}
    {...props}
  />
);

type ResizableHandleProps = React.ComponentProps<
  typeof ResizablePrimitive.PanelResizeHandle
> & {
  withHandle?: boolean;
};

const ResizableHandle = ({
  className,
  withHandle = false,
  ...props
}: ResizableHandleProps) => (
  <ResizablePrimitive.PanelResizeHandle
    className={cn(
      "relative flex w-px items-center justify-center bg-neutral-200 after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary-500 focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90",
      withHandle &&
        "group/handle before:absolute before:inset-y-0 before:left-1/2 before:w-4 before:-translate-x-1/2 before:cursor-col-resize",
      className
    )}
    {...props}
  >
    {withHandle && (
      <div className="z-10 flex h-4 w-3 items-center justify-center rounded-sm border border-neutral-200 bg-neutral-100 group-hover/handle:bg-neutral-200 group-focus-visible/handle:outline-none group-focus-visible/handle:ring-1 group-focus-visible/handle:ring-primary-500 group-focus-visible/handle:ring-offset-1">
        <svg
          width="6"
          height="12"
          viewBox="0 0 6 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="h-3 w-1.5 text-neutral-500"
        >
          <path
            d="M1 0.25C1.41421 0.25 1.75 0.585786 1.75 1C1.75 1.41421 1.41421 1.75 1 1.75C0.585786 1.75 0.25 1.41421 0.25 1C0.25 0.585786 0.585786 0.25 1 0.25Z"
            fill="currentColor"
          />
          <path
            d="M1 5.25C1.41421 5.25 1.75 5.58579 1.75 6C1.75 6.41421 1.41421 6.75 1 6.75C0.585786 6.75 0.25 6.41421 0.25 6C0.25 5.58579 0.585786 5.25 1 5.25Z"
            fill="currentColor"
          />
          <path
            d="M1 10.25C1.41421 10.25 1.75 10.5858 1.75 11C1.75 11.4142 1.41421 11.75 1 11.75C0.585786 11.75 0.25 11.4142 0.25 11C0.25 10.5858 0.585786 10.25 1 10.25Z"
            fill="currentColor"
          />
          <path
            d="M5 0.25C5.41421 0.25 5.75 0.585786 5.75 1C5.75 1.41421 5.41421 1.75 5 1.75C4.58579 1.75 4.25 1.41421 4.25 1C4.25 0.585786 4.58579 0.25 5 0.25Z"
            fill="currentColor"
          />
          <path
            d="M5 5.25C5.41421 5.25 5.75 5.58579 5.75 6C5.75 6.41421 5.41421 6.75 5 6.75C4.58579 6.75 4.25 6.41421 4.25 6C4.25 5.58579 4.58579 5.25 5 5.25Z"
            fill="currentColor"
          />
          <path
            d="M5 10.25C5.41421 10.25 5.75 10.5858 5.75 11C5.75 11.4142 5.41421 11.75 5 11.75C4.58579 11.75 4.25 11.4142 4.25 11C4.25 10.5858 4.58579 10.25 5 10.25Z"
            fill="currentColor"
          />
        </svg>
      </div>
    )}
  </ResizablePrimitive.PanelResizeHandle>
);

export { ResizablePanelGroup, ResizablePanel, ResizableHandle };
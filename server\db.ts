import "dotenv/config";
import fs from "fs";
import path from "path";
import pg from "pg";
import { drizzle } from "drizzle-orm/node-postgres";
import * as schema from "@shared/schema";

const { Pool } = pg;

if (!process.env.DATABASE_URL && (!process.env.DB_HOST || !process.env.DB_NAME)) {
  throw new Error("DATABASE_URL or individual DB credentials must be set.");
}

const isProduction = process.env.NODE_ENV === "production";

export const pool = new Pool({
  host: process.env.DB_HOST!,
  port: parseInt(process.env.DB_PORT!),
  user: process.env.DB_USER!,
  password: process.env.DB_PASSWORD!,
  database: process.env.DB_NAME!,
  ssl: isProduction ? {
    ca: fs.readFileSync(path.resolve(process.cwd(), "certs/ca-certificate.crt"), "utf8")
          .toString(),
    rejectUnauthorized: true,
  } : false,
});

export const db = drizzle(pool, { schema });
import React from 'react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

interface DocumentTitleProps {
  title: string;
  onTitleChange: (title: string) => void;
  saveStatus?: 'draft' | 'saved' | 'saving';
  rightContent?: ReactNode; // Added to support the History button
}

export function DocumentTitle({
  title,
  onTitleChange,
  saveStatus = 'draft',
  rightContent
}: DocumentTitleProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onTitleChange(e.target.value);
  };
  
  return (
    <div className="container mx-auto px-4 py-3 flex items-center justify-between bg-background">
      <div className="flex-1 flex items-center">
        <div className="relative flex-grow">
          <div className="flex items-center space-x-2">
            <Input
              type="text"
              placeholder="Document Title"
              value={title}
              onChange={handleChange}
              className="text-lg font-medium border-none focus:ring-0 focus:outline-none bg-transparent w-full max-w-md text-foreground"
            />
            

          </div>
        </div>
      </div>
      
      {/* Right content (History button, etc.) */}
      {rightContent && (
        <div className="flex-shrink-0 ml-6">
          {rightContent}
        </div>
      )}
    </div>
  );
}
